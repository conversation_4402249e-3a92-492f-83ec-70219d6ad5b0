import asyncio
import random

from common.models.chat_model import AddWaterMid


async def skip_now(water_mid: AddWaterMid):
    if not water_mid.add_water:
        return water_mid
    if water_mid.config_skip_count and water_mid.skip_count <= 0:
        water_mid.skip_count = water_mid.config_skip_count
        water_mid.skip_output = False
        await asyncio.sleep(
            random.uniform(water_mid.config_sleep_min, water_mid.config_sleep_max)
        )
        return water_mid

    if water_mid.skip_count > 0:
        water_mid.skip_count -= 1
        water_mid.skip_output = True
    return water_mid
