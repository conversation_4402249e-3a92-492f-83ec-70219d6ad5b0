from decimal import Decimal
from common.common_constant import SKIP_QUEUE_RECHARGE_PRODUCT_ID
from persistence.models.models import RechargeChannel<PERSON>num, RechargeOrder
from utils import translate_util


def format_charge_channel(order: RechargeOrder, language: str) -> str:
    display_fee = str(Decimal(order.pay_fee) / 1000 / 100)

    if order.recharge_product_id == SKIP_QUEUE_RECHARGE_PRODUCT_ID:
        return translate_util.translate_common("9.9免费聊天不排队", language)
    
    if order.recharge_channel == RechargeChannelEnum.INVITATION:
        desc = translate_util.translate_common("邀请奖励", language)
    elif order.recharge_channel == RechargeChannelEnum.MANUAL_AIRDROP:
        desc = translate_util.translate_common("运营赠送", language)
    elif (
        order.recharge_channel == RechargeChannelEnum.CHECK_IN
        or order.recharge_channel == RechargeChannelEnum.CHECK_IN_WITH_LINK
        or order.recharge_channel == RechargeChannelEnum.CHECK_IN_IN_ROLE_CHANNEL
    ):
        format_str = translate_util.translate_common("{} 签到", language)
        desc = format_str.format(order.created_at.strftime("%m-%d"))
    elif order.recharge_channel == RechargeChannelEnum.JOIN_CHAT_GROUP:
        desc = translate_util.translate_common("加群奖励", language)
    elif order.recharge_channel == RechargeChannelEnum.CHAT_BOT_TASKS:
        desc = translate_util.translate_common("聊天任务奖励", language)
    elif order.recharge_channel == RechargeChannelEnum.FC_REWARD:
        desc = translate_util.translate_common("首充奖励", language)
    elif order.recharge_channel in RechargeChannelEnum.pay_channels():
        format_str = translate_util.translate_common("充值 {}", language)
        desc = format_str.format(display_fee)
    elif order.recharge_channel == RechargeChannelEnum.CONSUMPTION_ACTIVITY_RETURN:
        desc = translate_util.translate_common("消费活动返还", language)
    elif order.recharge_channel == RechargeChannelEnum.CONSUMPTION_ACTIVITY_GRAND_PRIZE:
        desc = translate_util.translate_common("消费活动大奖", language)
    elif order.recharge_channel == RechargeChannelEnum.WELFARE:
        desc = translate_util.translate_common("福利活动", language)
    elif order.recharge_channel == RechargeChannelEnum.PUBLISH_CARD:
        desc = translate_util.translate_common("卡审核通过奖励", language)
    else:
        desc = translate_util.translate_common("其他", language)
    return desc
