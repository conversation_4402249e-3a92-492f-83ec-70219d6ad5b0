from itertools import groupby
from itertools import groupby
import logging
import re
from typing import Optional
from common.chat_bot_model import PresetSwitchQuery
from common.common_constant import (
    Language,
    RoleChatType,
    RoleFilterChatType,
    RoleSubTag,
    RoleTag,
)
from common.models.chat_model import SelectUserR<PERSON>Name
from common.role_card import CharacterBook
from common.role_model import (
    RoleDataConfig,
    RoleEditDetail,
    RoleFilterResponse,
    RoleFilterResponseV2,
    RoleRes,
    SceneConfig,
)
from common.translate_model import TranslateRole
from persistence.models.models import (
    RoleCategoryOrder,
    RoleConfig,
    RoleOperationConfig,
    SubTag,
)
from utils import (
    char_book_util,
    json_util,
    security_util,
    str_util,
    token_util,
    translate_util,
)


log = logging.getLogger(__name__)


def list_sub_tags_from_roles(roles: list[RoleRes]) -> list[str]:
    sub_tag_roles = {}
    for role in roles:
        for sub_tag in role.sub_tags:
            if sub_tag not in sub_tag_roles:
                sub_tag_roles[sub_tag] = 0
            sub_tag_roles[sub_tag] += 1
    ret_list = list(sub_tag_roles.keys())
    ret_list.sort(key=lambda x: sub_tag_roles.get(x, 0), reverse=True)
    return ret_list


def format_role_config(role_config: RoleConfig, nickname: str):
    role_config.card_name = str_util.format_char_and_user(
        role_config.card_name, role_config.role_name, nickname
    )
    role_config.introduction = str_util.format_char_and_user(
        role_config.introduction, role_config.role_name, nickname
    )
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    data_config.simple_intro = str_util.format_char_and_user(
        data_config.simple_intro, role_config.role_name, nickname
    )
    data_config.scenario = str_util.format_char_and_user(
        data_config.scenario, role_config.role_name, nickname
    )
    if len(data_config.muilte_scenes) > 0:
        for scene in data_config.muilte_scenes:
            scene.first_message = str_util.format_char_and_user(
                scene.first_message, role_config.role_name, nickname
            )
            scene.scenario = str_util.format_char_and_user(
                scene.scenario, role_config.role_name, nickname
            )
    data_config.description = str_util.format_char_and_user(
        data_config.description, role_config.role_name, nickname
    )
    data_config.personality = str_util.format_char_and_user(
        data_config.personality, role_config.role_name, nickname
    )
    data_config.status_block_init = str_util.format_char_and_user(
        data_config.status_block_init, role_config.role_name, nickname
    )
    data_config.status_block_rule = str_util.format_char_and_user(
        data_config.status_block_rule, role_config.role_name, nickname
    )
    data_config.status_block = str_util.format_char_and_user(
        data_config.status_block, role_config.role_name, nickname
    )
    role_config.data_config = data_config.model_dump()
    return role_config


def translate_role_config(
    role_config: RoleConfig, trans_task: Optional[TranslateRole] = None
) -> RoleConfig:
    if trans_task is not None:
        role_config = trans_task.translate_role_config(role_config)
    return role_config


def translate_role_tag(
    role_config: RoleConfig, sub_tags_map: dict[str, str] = {}
) -> RoleConfig:
    if sub_tags_map is not None and role_config.sub_tags is not None:
        role_config.sub_tags = [sub_tags_map.get(x, x) for x in role_config.sub_tags]
    return role_config


def translate_role_sub_tags(sub_tags: list[str], language: str) -> list[str]:
    if not sub_tags:
        return []
    if language == Language.ZH.value:
        return sub_tags
    return [translate_util._tl(x, language, SubTag.__name__) for x in sub_tags]


def get_user_role_name(role_config: RoleConfig, user_name: str):
    if (
        role_config is None
        or role_config.operation_config is None
        or role_config.operation_config == {}
    ):
        return user_name
    operation_config: RoleOperationConfig = RoleOperationConfig(
        **json_util.convert_to_dict(role_config.operation_config)
    )
    if len(operation_config.user_role_name) > 0:
        return operation_config.user_role_name
    return user_name


def get_new_user_role_name(
    role_config: RoleConfig, nickname: str
) -> SelectUserRoleName:
    select_name = SelectUserRoleName()
    if role_config.operation_config:
        operation_config = RoleOperationConfig(
            **json_util.convert_to_dict(role_config.operation_config)
        )
        select_name.user_role_name = operation_config.user_role_name
    select_name.nickname = nickname
    # user_role_name 是否为空，不能包括空格
    # if select_name.user_role_name and " " not in select_name.user_role_name:
    #     select_name.request_user_name = select_name.user_role_name
    # elif select_name.nickname and " " not in select_name.nickname:
    #     select_name.request_user_name = select_name.nickname
    # else:
    #     select_name.request_user_name = "Charlie"

    if select_name.user_role_name:
        select_name.request_user_name = select_name.user_role_name
    else:
        select_name.request_user_name = "Charlie"

    select_name.display_user_name = select_name.nickname
    return select_name


def format_first_message(role_config: RoleConfig, user_name: str) -> str:
    if role_config is None or role_config.data_config is None:
        return ""
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    scenes: list[SceneConfig] = data_config.format_new_scenes()
    first_message = ""
    if len(scenes) == 0:
        return first_message
    first_message = scenes[0].first_message
    if not first_message:
        return ""
    first_message = f"<rep>\n{role_config.role_name}: {first_message.strip()}\n</rep>"
    initial_sb = data_config.status_block_init
    if initial_sb and data_config.statusBlockEnable:
        c = str_util.format_status_block(initial_sb, data_config.statusBlockType)
        first_message = f"{first_message}\n<StatusBlock>\n{c}\n</StatusBlock>"
    first_message = str_util.format_char_and_user(
        first_message, role_config.role_name, user_name
    )
    return first_message


def format_first_message_on_save(role_config: RoleConfig) -> str:
    if not role_config or not role_config.data_config:
        return ""
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    scenes: list[SceneConfig] = data_config.format_new_scenes()
    first_message = ""
    if len(scenes) == 0:
        return first_message
    first_message = scenes[0].first_message
    if not first_message:
        return ""
    first_message = f"<rep>\n{role_config.role_name}: {first_message.strip()}\n</rep>"
    initial_sb = data_config.status_block_init
    if initial_sb and data_config.statusBlockEnable:
        c = str_util.format_status_block(initial_sb, data_config.statusBlockType)
        first_message = f"{first_message}\n<StatusBlock>\n{c}\n</StatusBlock>"
    first_message = str_util.format_char(first_message,  role_config.role_name)
    return first_message


def format_first_message_on_save_by_str(
    first_message: str, role_config: RoleConfig, status_block_init: str = ""
) -> str:
    if not first_message or not role_config or not role_config.data_config:
        return ""
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    initial_sb = data_config.status_block_init if not status_block_init else status_block_init
    first_message = f"<rep>\n{role_config.role_name}: {first_message.strip()}\n</rep>"
    if initial_sb and data_config.statusBlockEnable:
        c = str_util.format_status_block(initial_sb, data_config.statusBlockType)
        first_message = f"{first_message}\n<StatusBlock>\n{c}\n</StatusBlock>"
    first_message = str_util.format_char(first_message, role_config.role_name)
    return first_message


def format_display_scenario(role_config: Optional[RoleConfig], user_name: str) -> str:
    if role_config is None or role_config.data_config is None:
        return ""
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    scenes: list[SceneConfig] = data_config.format_new_scenes()
    if len(scenes) > 0:
        scenario = scenes[0].scenario
        return str_util.format_char_and_user(scenario, role_config.role_name, user_name)
    return ""


def bot_display_message(role_config: RoleConfig, message: str) -> str:
    # message = re.sub(r"<StatusBlock>.*?</StatusBlock>", "", message, flags=re.DOTALL)
    # message = re.sub(r"<StatusBlock>.*?</StatusBlock>", "", message, flags=re.DOTALL)
    # message = message.replace("<StatusBlock>", "<p>").replace("</StatusBlock>", "</p>")
    # 获取StatusBlock的内容
    # remove all xml tags in fm
    status_block = re.search(
        r"<StatusBlock>(.*?)</StatusBlock>", message, flags=re.DOTALL
    )
    message = re.sub(r"<StatusBlock>.*?</StatusBlock>", "", message, flags=re.DOTALL)
    message = re.sub(r"<[^>]+>", "", message)
    prefix_role_name = f"{role_config.role_name}:"
    if message.startswith(prefix_role_name):
        message = message[len(prefix_role_name) :]
    prefix_role_name = f"\n{role_config.role_name}:"
    message = re.sub(prefix_role_name, "\n", message, flags=re.DOTALL)

    if status_block:
        status_block = re.search(r"```(.*?)```", status_block.group(1), flags=re.DOTALL)
    if status_block:
        status_block = status_block.group(1)
        # message = f"{message}\n```markdown\n{status_block}\n```"
        if "```" in status_block:
            status_block = status_block.replace("```", "")
        message = f"{message}\n<blockquote>\n{status_block}\n</blockquote>"
    return message


def get_role_scenario(role_config: Optional[RoleConfig]) -> str:
    if role_config is None or role_config.data_config is None:
        return ""
    data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
    scenes: list[SceneConfig] = data_config.format_new_scenes()
    if len(scenes) > 0:
        scenario = scenes[0].scenario
        return scenario
    return ""


def format_scenario(
    scenario_format: str, data_config: dict, user_name: str, role_name: str
) -> str:
    data_config_model: RoleDataConfig = RoleDataConfig(**data_config)
    content = data_config_model.scenario
    if len(data_config_model.format_new_scenes()) > 0:
        content = data_config_model.format_new_scenes()[0].scenario
    if str_util.is_empty(content):
        return ""
    if str_util.is_not_empty(scenario_format):
        content = scenario_format.replace("{{scenario}}", content)
    content = str_util.format_status_bar_with_config(data_config, content)
    content = str_util.format_user(content, user_name)
    content = str_util.format_char(content, role_name)
    return content


def remove_html_tag(role: RoleEditDetail):
    if role is None:
        return role
    role.name = security_util.remove_html_tags(role.name)
    role.card_name = security_util.remove_html_tags(role.card_name)
    role.role_name = security_util.remove_html_tags(role.role_name)
    role.introduction = security_util.remove_html_tags(role.introduction)
    role.description = security_util.remove_html_tags(role.description)
    role.first_message = security_util.remove_html_tags(role.first_message)
    role.status_block_init = security_util.remove_html_tags(role.status_block_init)
    role.status_block_rule = security_util.remove_html_tags(role.status_block_rule)
    if len(role.muilte_scenes) > 0:
        for scene in role.muilte_scenes:
            scene.first_message = security_util.remove_html_tags(scene.first_message)
            scene.scenario = security_util.remove_html_tags(scene.scenario)
    return role


# 识别角色卡片语言
def detect_language(role: RoleEditDetail) -> Language:
    check_text = ""
    check_text += role.name
    check_text += role.role_name
    check_text += role.card_name
    scenes: list[SceneConfig] = role.format_new_scenes()
    if len(scenes) > 0:
        check_text += scenes[0].first_message
    return translate_util.detect_language(check_text)


def num_token_dict(data_config: RoleDataConfig) -> dict:
    ret = {}
    ret["description"] = token_util.num_tokens_from_string(data_config.description)
    ret["personality"] = token_util.num_tokens_from_string(data_config.personality)
    ret["example_dialog"] = token_util.num_tokens_from_string(
        data_config.example_dialog
    )
    cal_examples_tokens = 0
    for mid in data_config.muilte_examples:
        cal_examples_tokens += token_util.num_tokens_from_string(mid.char)
        cal_examples_tokens += token_util.num_tokens_from_string(mid.user)
    ret["muilte_examples"] = cal_examples_tokens

    cal_muilte_scenes = 0
    for mid in data_config.muilte_scenes:
        cal_muilte_scenes += token_util.num_tokens_from_string(mid.first_message)
        cal_muilte_scenes += token_util.num_tokens_from_string(mid.scenario)
    ret["muilte_scenes"] = cal_muilte_scenes
    if data_config.statusBlockEnable == True:
        ret["status_block_init"] = token_util.num_tokens_from_string(
            data_config.status_block_init
        )
        ret["status_block_rule"] = token_util.num_tokens_from_string(
            data_config.status_block_rule
        )
        ret["status_block"] = token_util.num_tokens_from_string(
            data_config.status_block
        )
    return ret


def reformat_sub_tags(role: RoleConfig) -> RoleConfig:
    if not role.sub_tags:
        return role
    data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
    sub_tags = json_util.convert_to_list(role.sub_tags)
    if RoleSubTag.STATUS.value in sub_tags:
        sub_tags.remove(RoleSubTag.STATUS.value)
    if RoleSubTag.LIGHT_CHAT.value in sub_tags:
        sub_tags.remove(RoleSubTag.LIGHT_CHAT.value)
    if RoleSubTag.ROLE_PLAY.value in sub_tags:
        sub_tags.remove(RoleSubTag.ROLE_PLAY.value)
    if data_config.statusBlockEnable:
        sub_tags.append(RoleSubTag.STATUS.value)
    if role.chat_type == RoleChatType.CHAT.value:
        sub_tags.append(RoleSubTag.LIGHT_CHAT.value)
    if role.chat_type == RoleChatType.ROLE_PLAY.value:
        sub_tags.append(RoleSubTag.ROLE_PLAY.value)
    return role


def get_safe_image_url(role: RoleConfig) -> str:
    if not role.image_nsfw:
        return role.role_avatar
    return str_util.handle_spoiler_avatar(role.role_avatar)


def replace_group_scenario_role_name(scenario: str, role_name_map: dict[int, str]):
    if not scenario:
        return scenario
    for id, role_name in role_name_map.items():
        if not role_name or role_name not in scenario:
            continue
        target = "{{char@" + str(id) + "}}"
        scenario = scenario.replace(role_name, target)
    return scenario


# 填充场景
def fill_group_scenario_role_name(scenario: str, role_name_map: dict[int, str]):
    if not scenario:
        return scenario
    for id, role_name in role_name_map.items():
        replace = "{{char@" + str(id) + "}}"
        if not role_name or replace not in scenario:
            continue
        scenario = scenario.replace(replace, role_name)
    return scenario


def parse_role_filter_chat_type_desc(
    real_role: bool, chat_type: str, sub_tags: list[str]
):
    if "生成器" in sub_tags:
        return "生成器(剧情)"
    if chat_type == RoleChatType.CHAT.value and real_role:
        return "独立角色(轻聊天)"
    if real_role:
        return "独立角色(剧情)"
    return "多角色(剧情)"


def reset_sort_by_uid(role_ids: list[int], role_uid_map: dict[int, int]):
    # role_ids重新排序，保证同一个uid，不同的role_id位置不能小于10
    role_id_index = {}
    for index, role_id in enumerate(role_ids):
        role_id_index[role_id] = index
    uid_index = {}
    for index, role_id in enumerate(role_ids):
        if index >= 100:
            break
        uid = role_uid_map[role_id]
        if uid == 0:
            continue
        if uid in uid_index and index < uid_index[uid] + 10:
            new_index = uid_index[uid] + 10
            role_id_index[role_id] = new_index
            uid_index[uid] = new_index
            continue
        uid_index[uid] = index

    role_ids.sort(key=lambda x: role_id_index[x])
    return role_ids


def public_author(role_config: RoleConfig):
    if not role_config or not role_config.operation_config or not role_config.uid:
        return False
    operation_config = RoleOperationConfig(
        **json_util.convert_to_dict(role_config.operation_config)
    )
    return operation_config.author


def map_role_authors(
    role_configs: list[RoleConfig], user_nickname_map: dict[int, str]
) -> dict[int, str]:
    role_author_name = {}
    for role in role_configs:
        author_name = user_nickname_map.get(role.uid, "")
        if not public_author(role) or not author_name:
            author_name = user_nickname_map.get(0, "")
        role_author_name[role.id] = author_name
    return role_author_name


def anonymous_response_authors(ret: RoleFilterResponse | RoleFilterResponseV2):
    if isinstance(ret, RoleFilterResponse):
        for r in ret.roles:
            r.author_name = "anonymous"
    elif isinstance(re, RoleFilterResponseV2):
        for r in ret.card_list:
            r.author_name = "anonymous"
