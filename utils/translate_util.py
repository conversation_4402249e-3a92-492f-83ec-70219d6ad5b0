# # To make the results reproducible
# from langdetect import detect, DetectorFactory
# from langdetect.lang_detect_exception import LangDetectException

# def detect_language(text):
#     try:
#         language = detect(text)
#         return language
#     except LangDetectException:
#         return "Language could not be detected"

# # Example usage
# language = detect_language('速度超快，性價比超高，文筆細膩絲滑')
# print(f"The detected language is: {language}")

from hashlib import md5
import logging

from cachetools import TTLCache
from common.common_constant import Language
from properties import prop_util
import zhconv

log = logging.getLogger(__name__)


def local_convert(text: str, lang: str) -> str:
    if lang == Language.ZH_TW.value:
        return zhconv.convert(text, "zh-tw")
    if lang == Language.ZH.value:
        return zhconv.convert(text, "zh-cn")
    return text


def check_all_zh(text: str) -> bool:
    # 排除各种符合
    if not text or len(text) == 0:
        return True
    zh_count = sum(1 for s in text if "\u4e00" <= s <= "\u9fa5")
    en_count = sum(1 for s in text if "a" <= s <= "z" or "A" <= s <= "Z")
    return zh_count > 0 and en_count == 0


def check_all_en(text: str) -> bool:
    # 排除各种符合
    if not text or len(text) == 0:
        return True
    en_count = sum(1 for s in text if "a" <= s <= "z" or "A" <= s <= "Z")
    zh_count = sum(1 for s in text if "\u4e00" <= s <= "\u9fa5")
    return en_count > 0 and zh_count == 0


def detect_language(text: str) -> Language:
    # 统计text中英文字母数量
    def count_en(text):
        count = 0
        for s in text:
            if "a" <= s <= "z" or "A" <= s <= "Z":
                count += 1
        return count

    # 统计text中中文字符数量
    def count_cn(text):
        count = 0
        for s in text:
            if "\u4e00" <= s <= "\u9fa5":
                count += 1
        return count

    if count_en(text) > count_cn(text):
        return Language.EN
    # traditional = local_convert(text, Language.ZH.value)
    # traditional与text不同的数量
    diff = 0
    for i in range(len(text)):
        if text[i] != local_convert(text[i], Language.ZH.value):
            # if text[i] != traditional[i]:
            diff += 1
    if diff > len(text) * 0.1:
        return Language.ZH_TW
    return Language.ZH


def _t(text: str, lang: str, category: str = "COMMON") -> str:
    return translate_common(text, lang)
    # return translate(text, lang, category)


def _tl(text: str, lang: str, category: str = "COMMON") -> str:
    if not text or len(text) == 0:
        return text
    translate_value = translate(text, lang, category)
    if translate_value:
        return translate_value
    return text


def translate_common(text: str, lang: str) -> str:
    if len(text) == 0:
        return text
    ret = prop_util.load_translate(text, lang)
    if ret == None:
        # log.warning(f"translate_common: {lang},{text} not found")
        return text
    return ret


translate_cache = {}
uncache_text_queue = set()
# 没有缓存的队列


def translate(text: str, lang: str, category: str = "COMMON") -> str:
    if not text or len(text) == 0:
        return text
    cache_key = text
    if len(text) > 32:
        cache_key = md5(text.encode()).hexdigest()
    cache_key = f"{category}_{cache_key}"

    cache_value = translate_cache.get(cache_key)
    if cache_value and lang not in cache_value:
        return text
    if cache_value and lang in cache_value:
        translate_value = cache_value.get(lang)
        if translate_value:
            return translate_value
    uncache_text_queue.add((category, text))
    return text


def add_cache(text: str, category: str, data: dict):
    cache_key = text
    if len(text) > 32:
        cache_key = md5(text.encode()).hexdigest()
    cache_key = f"{category}_{cache_key}"
    translate_cache[cache_key] = data


# def model_equal(source: dict, target: dict) -> bool:
#     if len(source) != len(target):
#         return False
#     for key in source:
#         if source[key] != target[key]:
#             return False
#     return True
