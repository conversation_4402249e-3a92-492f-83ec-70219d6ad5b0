from enum import Enum

from pydantic import BaseModel

from common.common_constant import Language
from common.role_card import CharacterBook
from common.role_model import RoleDataConfig, SceneConfig
from persistence.models.models import ChatGroupConfig, RoleConfig
from utils import json_util


class TranslateTaskType(Enum):
    ROLE = "role"
    CHAR_BOOK_KEYS = "char_book_keys"
    SUB_TAG = "sub_tag"
    TAG = "tag"
    GROUP = "group"
    ROLE_DESCRIPTION = "role_description"


class TranslateTaskStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    FINISHED = "finished"
    ERROR = "error"


class TranslateRole(BaseModel):
    card_name: str = ""
    role_name: str = ""
    introduction: str = ""
    first_message: str = ""
    scenario: str = ""
    simple_intro: str = ""
    status_block: str = ""
    status_block_init: str = ""
    muilte_scenes: list[SceneConfig] = []

    @staticmethod
    def init_from_role_config(role_config: RoleConfig) -> "TranslateRole":
        role_data_config = RoleDataConfig(
            **json_util.convert_to_dict(role_config.data_config)
        )
        return TranslateRole(
            card_name=role_config.card_name,
            role_name=role_config.role_name,
            first_message=role_data_config.first_message,
            scenario=role_data_config.scenario,
            introduction=role_config.introduction,
            simple_intro=role_data_config.simple_intro,
            status_block=role_data_config.status_block,
            status_block_init=role_data_config.status_block_init,
            muilte_scenes=role_data_config.muilte_scenes,
        )

    @staticmethod
    def trans_str(translate_target: str, original: str) -> str:
        return translate_target if len(translate_target) > 0 else original

    def translate_role_config(self, role_config: RoleConfig) -> RoleConfig:
        dc = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
        dc.simple_intro = TranslateRole.trans_str(self.simple_intro, dc.simple_intro)
        dc.first_message = TranslateRole.trans_str(self.first_message, dc.first_message)
        dc.scenario = TranslateRole.trans_str(self.scenario, dc.scenario)
        dc.status_block = TranslateRole.trans_str(self.status_block, dc.status_block)
        dc.status_block_init = TranslateRole.trans_str(
            self.status_block_init, dc.status_block_init
        )
        role_config.introduction = TranslateRole.trans_str(
            self.introduction, role_config.introduction
        )
        role_config.role_name = TranslateRole.trans_str(
            self.role_name, role_config.role_name
        )
        role_config.card_name = TranslateRole.trans_str(
            self.card_name, role_config.card_name
        )
        if len(dc.muilte_scenes) == len(self.muilte_scenes):
            dc.muilte_scenes = self.muilte_scenes
        role_config.data_config = dc.model_dump()
        return role_config


class TranslateRoleDescription(BaseModel):
    description: str = ""
    terms: list[str] = []  # 术语:original/simplified/traditional/english
    text_guide: str = ""

    def parse_terms(self) -> dict:
        ret_list = {}
        for term in self.terms:
            term_list = term.split("/")
            if len(term_list) != 4:
                continue
            original = term_list[0]
            ret_list[original] = {
                Language.ZH.value: term_list[1],
                Language.ZH_TW.value: term_list[2],
                Language.EN.value: term_list[3],
            }
        return ret_list


class TranslateSubTag(BaseModel):
    tag_name: str = ""


class TranslateTag(BaseModel):
    tags: list[str] = []


class TranslateBookEntry(BaseModel):
    keys: list[str] = []
    # secondary_keys: list[str] = []


class TranslateBook(BaseModel):
    entries: list[TranslateBookEntry] = []

    @staticmethod
    def from_char_book(char_book: CharacterBook) -> "TranslateBook":
        if not char_book.entries:
            return TranslateBook(entries=[])
        return TranslateBook(
            entries=[TranslateBookEntry(keys=entry.keys) for entry in char_book.entries]
        )


class TranslateGroup(BaseModel):
    name: str = ""
    simple_intro: str = ""
    introduction: str = ""
    scenario: str = ""

    @staticmethod
    def from_group(group_config: ChatGroupConfig) -> "TranslateGroup":
        return TranslateGroup(
            name=group_config.name,
            simple_intro=group_config.simple_intro,
            introduction=group_config.introduction,
            scenario=group_config.scenario,
        )
