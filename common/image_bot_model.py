from typing import Optional, Dict
from pydantic import BaseModel
import json

from persistence.models.models_bot_image import BotImgBasicProfile


class GenImageBaseProfileBO(BaseModel):
    style: str
    resolution: str = "img_resolution_low"
    # resolution: str = "medium"
    privacy: Optional[str] = "public"

    @staticmethod
    def from_model(basic_profile: BotImgBasicProfile):

        profile_json = basic_profile.img_gen_profile

        return GenImageBaseProfileBO.model_validate_json(json.dumps(profile_json))

    def to_json(self):
        return self.model_dump_json(exclude=None)


class ImageBotSettingsBO(BaseModel):
    bot_id: int
    settings: dict
    start_msg: str = "欢迎👏"
    style_group_mapping: Dict[str, Dict[str, int]] = {}  # 封装为嵌套字典

    def add_style_mapping(self, style: str, group_id: int, topic_id: int):
        """添加一个 style 到 group_id 和 topic_id 的映射"""
        self.style_group_mapping[style] = {"group_id": group_id, "topic_id": topic_id}

    def remove_style_mapping(self, style: str):
        """移除一个 style 的映射"""
        if style in self.style_group_mapping:
            del self.style_group_mapping[style]

    def get_style_mapping(self, style: str) -> Optional[Dict[str, int]]:
        """获取一个 style 的映射"""
        return self.style_group_mapping.get(style)

    def update_style_mapping(
        self, style: str, group_id: Optional[int] = None, topic_id: Optional[int] = None
    ):
        """更新一个 style 的映射"""
        if style in self.style_group_mapping:
            if group_id is not None:
                self.style_group_mapping[style]["group_id"] = group_id
            if topic_id is not None:
                self.style_group_mapping[style]["topic_id"] = topic_id
