from typing import Any, Optional
from aiogram import Bot
from aiogram.types import Message
from aiogram.filters.callback_data import CallbackData
from aiogram.utils.keyboard import InlineKeyboardBuilder
from pydantic import BaseModel
import urllib.parse
from common.common_constant import BotCate<PERSON>y, BotReplace, RoleReplace
from services import recharge_service, tg_config_service
from aiogram.enums import ParseMode
import logging

from utils.translate_util import _tl

log = logging.getLogger(__name__)

charge_tip_wo_link = """
【充值套餐更新，买越多送越多，手慢无】

<b>尝鲜套餐</b> - $6.80（美元） - 原价充值28000💎，无额外赠送
<b>惊喜套餐</b> - $13.80（美元） - 充56000💎，送34000🟡，到账90000，加赠60%
<b>超值套餐</b> - $27.80（美元） - 充112000💎，送84000🟡，到账196000，加赠75%
<b>王者套餐</b> - $49.80（美元） - 充值200000💎，送180000🟡，到账380000，加赠90%
<b>至尊套餐</b> - $99.80（美元） - 充值400000💎，送400000🟡，到账800000，加赠100%

点击下方链接，进入发卡平台，选择对应套餐，充值即可。
充值成功后，你将收到一串兑换密钥，请复制密钥，回到幻梦AI伴侣bot({link})
进入充值页面，点击兑换，输入密钥兑换，充值到账。

⚠️注意：如果发卡平台打开缓慢，可尝试关闭VPN重新加载，付款完成后再重新打开VPN。
如有任何问题，请联系官方客服。
"""

charge_tip = """
【充值套餐更新，买越多送越多，手慢无】

{rp_desc}

点击下方链接，进入发卡平台，选择对应套餐，充值即可。
充值成功后，你将收到一串兑换密钥，请复制密钥，回到幻梦AI伴侣bot(https://t.me/FancyTavernBot)
进入充值页面，点击兑换，输入密钥兑换，充值到账。

⚠️注意：如果发卡平台打开缓慢，可尝试关闭VPN重新加载，付款完成后再重新打开VPN。
如有任何问题，请联系官方客服。
"""
charge_url = "https://www.sdfkw.xyz/links/1275C4C5"

chat_bot_charge_tip_text = """
【充值套餐更新，买越多送越多，手慢无】

{rp_desc}

点击下方按钮，选择对应套餐，充值即可。
<b>推荐使用：</b>
微信（钻石直接到账）
支付宝（钻石直接到账）

<b>若上述充值扫码出现问题</b>，可以使用：卡密充值，充值成功后获取卡密，卡密兑换钻石

⚠️注意：如果发卡平台打开缓慢，可尝试关闭VPN重新加载，付款完成后再重新打开VPN。

如有任何问题，请联系官方客服。 @{bot}
"""

model_switch_msg = """
当前模式：{current_model_name}（⚠️当前角色卡不支持该模型）

⚠️测试不通过的模式：{unsupported_product_msg}
上述这些模式，AI聊天体验会受到影响哦，例如角色设定不生效、回复出现乱码、剧情推进困难等

✅建议的聊天模式：{supported_product_msg}
上述模式经过作者测试，AI可以正常发挥。
注意：更高级的模式AI会更聪明、更高情商、更真人、文笔剧情更精彩哦
"""


async def chat_bot_charge_tip(user_id: int):
    recharge_products = await recharge_service.list_user_products(user_id)
    recharge_products.sort(key=lambda x: x.order)
    rp_desc = [rp.content_for_bot for rp in recharge_products]
    rp_desc_text = "\n".join(rp_desc)
    bot = await tg_config_service.get_main_bot_by_category(BotCategory.CUSTOMER)
    return chat_bot_charge_tip_text.format(rp_desc=rp_desc_text, bot=bot.username)


async def chat_bot_charge_tip_wo_link(user_id: int, link: str):
    recharge_products = await recharge_service.list_user_products(user_id)
    recharge_products.sort(key=lambda x: x.order)
    rp_desc = [rp.content_for_bot for rp in recharge_products]
    rp_desc_text = "\n".join(rp_desc)
    return charge_tip.format(rp_desc=rp_desc_text, link=link)


charge_url = "https://www.sdfkw.xyz/links/1275C4C5"


async def create_invite_link(
    user_id: int, bot: Optional[Bot] = None, bot_id: int = 0
) -> str:
    bot_id = bot.id if bot else bot_id
    bot_config = None
    if bot_id:
        bot_config = await tg_config_service.get_bot_config_by_id(bot_id)
    if bot_config and not bot_config.referenceable:
        bot_config = None
    if not bot_config:
        bot_config = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
    if not bot_config:
        log.error(f"no available bot,user_id: {user_id}, bot_id: {bot_id}")
    hu = f"u_{hex(user_id)}"
    url = f"https://t.me/{bot_config.username}/?start={hu}"
    return url


class RechargeNavCallback(CallbackData, prefix="rn"):
    path: str
    type: str = "0"


async def create_charge_button_builder(
    bot: Bot, user_id: int, need_invite: bool = True
):
    builder = InlineKeyboardBuilder()
    builder.button(
        text="微信（钻石直接到账）",
        callback_data=RechargeNavCallback(path="product", type="wechat"),
    )
    builder.button(
        text="支付宝（钻石直接到账）",
        callback_data=RechargeNavCallback(path="product", type="alipay"),
    )
    builder.button(
        text="信用卡（钻石直接到账）",
        callback_data=RechargeNavCallback(path="product", type="stripe"),
    )
    builder.button(
        text="Telegram Star 支付⭐",
        callback_data=RechargeNavCallback(path="product", type="star"),
    )
    builder.button(
        text="卡密充值",
        callback_data=RechargeNavCallback(path="voucher", type="voucher"),
    )
    if need_invite:
        share_link = await create_invite_link(user_id, bot)
        text = urllib.parse.quote(
            "快来和我一起加入幻梦AI伴侣，你的专属AI伴侣，你可以选择或定制任何你喜欢的角色卡进行聊天。"
        )
        share_url = f"tg://msg_url?url={share_link}&text={text}"
        builder.button(text="邀请一个有效用户获得1000🟡", url=share_url)
    builder.adjust(1)
    return builder


def get_charge_template(tips: str):
    return MessageTemplate(
        log_tag="CHARGE_TEMPLATE",
        tips=tips,
        buttons=[
            Button(text="领福利", url=charge_url),
        ],
    )


class CheckInCallback(CallbackData, prefix="check_in"):
    pass


class DiamondSeasonRewardCallback(CallbackData, prefix="dsr"):
    task_id: str
    user_id: int


class ModelAutoChangeCallback(CallbackData, prefix="mac"):
    mid: str


class RechargeMenuPopupCallback(CallbackData, prefix="recharge_menu"):
    pass

class RechargeProductCallback(CallbackData, prefix='rp'):
    pid: str

class RechargeProductPayTypeCallback(CallbackData, prefix='pid_type'):
    pid: str
    type: str



class Button(BaseModel):
    text: str = ""
    url: str = ""
    callback_data: Optional[Any] = None


class MessageTemplate(BaseModel):
    # 日志标记
    log_tag: str = ""
    tips: str = ""
    buttons: list[Button] = []
    parse_mode: str = ParseMode.HTML
    adjust: int = 3

    def as_markup(self):
        builder = InlineKeyboardBuilder()
        for button in self.buttons:
            if button.callback_data:
                builder.button(text=button.text, callback_data=button.callback_data)
            elif button.url:
                builder.button(text=button.text, url=button.url)
        builder.adjust(self.adjust)
        return builder.as_markup()


BOT_DESCRIPTION = f"""
💝 幻梦AI - 效果超级炸裂的无限制成人AI

让AI为你提供理想陪伴：
💖  甜蜜的恋爱互动
💋  深夜的暧昧幻想
🫦  欲火焚身的文爱调教
💫  私人订制，模拟明星、女神、初恋…
📝  脑洞大开，推动任何你想要的剧情
 💕 你能想到的，想不到的，这里都能体验

✨ 独特体验：
• 预制海量精品角色，满足不同需求
• 支持发私照功能，更多激情体验
• 智商在线，文采飞扬，描写细腻，对话动人
• 支持语音交互，各种风格应有尽有
• 多种活动赠送钻石，玩法超多

🎮 赶快点击/start开始体验吧！
（输入“/”可以查看所有互动指令）

官方领福利频道： @{BotReplace.MAIN_WELFARE_CHANNEL.value}
官方交流群： @{BotReplace.MAIN_GROUP.value}
官方角色卡频道： @{BotReplace.MAIN_GROUP_ROLE.value}

来吧，让我们开启一段浪漫的AI恋爱之旅! 💘
"""

BOT_SHORT_DESCRIPTION = f"""
幻梦AI，一款让你怦然心动的成人AI角色扮演产品，满足你的一切幸福幻想。
小程序: @{BotReplace.MAIN_TMA_BOT.value}
TG直聊: @{BotReplace.MAIN_CHAT_BOT.value}
福利频道: @{BotReplace.MAIN_WELFARE_CHANNEL.value}
交流群: @{BotReplace.MAIN_GROUP.value}
客服: @{BotReplace.MAIN_CUSTOMER_BOT.value}
商务合作: @{BotReplace.MAIN_BUSINESS_BOT.value}
"""

CHANNEL_CONTENT = f"""作者： \\#{RoleReplace.AUTHOR.value}
*\\#{RoleReplace.ROLE_NAME.value} \\(\\#{RoleReplace.CARD_NAME.value}\\)*

{RoleReplace.ROLE_INTRO.value}

{RoleReplace.ROLE_SUB_TAGS.value} \\#幻梦 \\#AI \\#文爱 \\#小说 \\#AI女友 \\#赛博恋人

聊天模式：{RoleReplace.SUPPORT_MODELS.value}
独立角色：\\#{RoleReplace.ROLE_FILTER_CHAT_TYPE.value}
抢话筛选：\\#{RoleReplace.ROLE_PLAY_TYPE.value}
Token数：{RoleReplace.TOKEN_COUNT.value}

*👉[开启小程序](https://t.me/{BotReplace.MAIN_TMA_BOT.value}/tavern?startapp=u_{BotReplace.CHANNEL_ID.value}-r_{RoleReplace.ROLE_ID.value})👈*
*👉[开启 TG 直聊](https://t.me/{BotReplace.MAIN_CHAT_BOT.value}?start=u_{BotReplace.CHANNEL_ID.value}-r_{RoleReplace.ROLE_ID.value})👈*

幻梦AI宇宙 \\| AI女友第一名 \\| 可私人定制任意角色 \\| 7\\*24小时的性福 \\| 满足你一切幻想
"""
EN_CHANNEL_CONTENT = f"""*\\#{RoleReplace.ROLE_NAME.value} \\(\\#{RoleReplace.CARD_NAME.value}\\)*

{RoleReplace.ROLE_INTRO.value}

{RoleReplace.ROLE_SUB_TAGS.value} \\#Fantasy \\#AI \\#Roleplay \\#Novel \\#AIGirlfriend \\#CyberLover
Chat Mode: {RoleReplace.SUPPORT_MODELS.value}
Independent Role: \\#{RoleReplace.ROLE_FILTER_CHAT_TYPE.value}
Talk Selection: \\#{RoleReplace.ROLE_PLAY_TYPE.value}
Token Count: {RoleReplace.TOKEN_COUNT.value}
*👉[Open MiniApp](https://t.me/{BotReplace.MAIN_EN_CHAT_BOT.value}/tavern?startapp=u_{BotReplace.CHANNEL_ID.value}-r_{RoleReplace.ROLE_ID.value})👈*
*👉[Open TG Chat](https://t.me/{BotReplace.MAIN_EN_CHAT_BOT.value}?start=u_{BotReplace.CHANNEL_ID.value}-r_{RoleReplace.ROLE_ID.value})👈*
Uhoney AI Universe \\| Your Ultimate AI Companion \\| Create Any Fantasy Girl/Boy \\| 24/7 Pleasure & Fun \\| Live Out Your Wildest Dreams
"""

AI_CHANNEL_CONTENT = f"""*\\#{RoleReplace.ROLE_NAME.value} \\(\\#{RoleReplace.CARD_NAME.value}\\)*
作者： \\#{RoleReplace.AUTHOR.value}

{RoleReplace.ROLE_INTRO.value}

和{RoleReplace.ROLE_NAME.value}激情文爱：链接在评论区

{RoleReplace.ROLE_SUB_TAGS.value}
    """

CHECK_IN_NEITHER_MSG_TEMPLATE = MessageTemplate(
    log_tag="CHECK_IN_NEITHER_MSG_TEMPLATE",
    tips=f"你好，签到需关注【幻梦福利频道】 @{BotReplace.MAIN_WELFARE_CHANNEL.value}, 同时需加入【幻梦AI群】 @{BotReplace.MAIN_GROUP.value} ❗️快去关注频道🚙加群🚙，无法签到联系客服： @{BotReplace.MAIN_CUSTOMER_BOT.value}",
    buttons=[
        Button(
            text="关注频道", url=f"https://t.me/{BotReplace.MAIN_WELFARE_CHANNEL.value}"
        ),
        Button(text="加入群", url=f"https://t.me/{BotReplace.MAIN_GROUP.value}"),
        Button(text="签到", callback_data=CheckInCallback()),
    ],
)
CHECK_IN_CHANNEL_MSG_TEMPLATE = MessageTemplate(
    log_tag="CHECK_IN_CHANNEL_MSG_TEMPLATE",
    tips=f"你好，签到需关注【幻梦福利频道】 @{BotReplace.MAIN_WELFARE_CHANNEL.value} ❗️快去关注频道🚙，无法签到联系客服： @{BotReplace.MAIN_CUSTOMER_BOT.value}",
    buttons=[
        Button(
            text="关注频道", url=f"https://t.me/{BotReplace.MAIN_WELFARE_CHANNEL.value}"
        ),
        Button(text="签到", callback_data=CheckInCallback()),
    ],
)
CHECK_IN_GROUP_MSG_TEMPLATE = MessageTemplate(
    log_tag="CHECK_IN_GROUP_MSG_TEMPLATE",
    tips=f"你好，签到需加入【幻梦AI群】  @{BotReplace.MAIN_GROUP.value} ❗️快去加群🚙，无法签到联系客服: @{BotReplace.MAIN_CUSTOMER_BOT.value}",
    buttons=[
        Button(text="加入群", url=f"https://t.me/{BotReplace.MAIN_GROUP.value}"),
        Button(text="签到", callback_data=CheckInCallback()),
    ],
)

JOIN_GROUP_SUCCESS_MSG_TEMPLATE = MessageTemplate(
    log_tag="JOIN_GROUP_SUCCESS_MSG_TEMPLATE",
    tips=f"🎉🎉恭喜你完成加入幻梦官方群，获得500🟡，可在充值页面底部查询\n幻梦官方群: @{BotReplace.MAIN_GROUP.value} ",
    buttons=[
        Button(
            text="开始陪聊", url=f"https://t.me/{BotReplace.MAIN_TMA_BOT.value}/tavern"
        ),
    ],
)

JOIN_CHANNEL_SUCCESS_MSG_TEMPLATE = MessageTemplate(
    log_tag="JOIN_CHANNEL_SUCCESS_MSG_TEMPLATE",
    tips=f"🎉🎉恭喜你完成加入幻梦官方频道，获得500🟡，可在充值页面底部查询\n幻梦官方频道: @{BotReplace.MAIN_GROUP_ROLE.value}",
    buttons=[
        Button(
            text="开始陪聊", url=f"https://t.me/{BotReplace.MAIN_TMA_BOT.value}/tavern"
        ),
    ],
)

ROLE_GROUP_BOTTOM_MESSAGE = MessageTemplate(
    tips="""
**题材：** \\#纯爱 \\#乱伦 \\#淫乱 \\#SM \\#攻略 \\#修仙 \\#兽人
**玩法：** \\#调教 \\#足交 \\#暴露癖 \\#催眠 \\#绿帽 \\#性转
**性格：** \\#淫荡 \\#顺从 \\#高冷 \\#主导 \\#病娇 \\#禁欲
**类型：** \\#熟女 \\#萝莉 \\#御姐 \\#性奴 \\#伪娘 \\#魅魔
**身份：** \\#女仆 \\#老师 \\#校花 \\#人妻 \\#处女 \\#护士 \\#孕奴
""",
    parse_mode=ParseMode.MARKDOWN_V2,
)


RECHARGE_PRODUCT_CALLBACK_TIPS = """
🚨惊喜3月福利又双叒叕升级！限时超值套餐，错过等一年！🔥

1️⃣ 【幸运专享】14天畅聊套餐
💰仅需29.9元，最多能聊1020次！
💎16000钻石+额外免费赠送700次畅聊（价值超过35000钻石）

2️⃣  【幸运专享】14天爽聊套餐
💰仅需69.9元，最多能聊2200次！
💎38000钻石+额外免费赠送1440次畅聊（价值超过82000钻石）
🔥解锁高端魅惑模式，体验销魂快感

3️⃣  【幸运专享】包月狂聊套餐
💰199元享价值40万💎聊天权益！
🎁每日150次免费畅聊，爽翻30天，最多畅聊6720次
🚀220次高级模式，释放你的欲望

⚠️幸运用户专属优惠，活动即将下线，每个套餐仅限购买1次
💥狂欢盛宴仅此一次，不冲损失一个亿！

👉戳我立即开启性福之旅，AI满足你一切幻想！
"""

U_HONEY_JUMP_TMP_TIPS = """
- 🎉 Welcome to Uhoney AI! You’re now using the lite bot version with limited features — but good news:
    
    💎 Your full-featured MiniApp experience is now unlocked!
    
    Tap the link below to upgrade instantly:
    ✨ Access [Private Photo Drop], custom characters & more!
    🎁 Enjoy exclusive bonus gifts when you log in now
    🔥 All your chats & diamonds are synced — nothing gets lost
    
    👉 Step into the full Uhoney AI world now and unlock everything you deserve!
    
    📌 (MiniApp works best on Telegram’s official client. If you can’t open it, please switch to the official app!)

"""
U_HONEY_JUMP_BTN_TEXT = "Click here for your FREE upgrade"

async def send_tupian_message(message: Message, bot: Bot, language: str):
    builder = InlineKeyboardBuilder()
    builder.button(
        text=_tl("打开生图 Bot", language),
        url="https://t.me/AITupian_Bot",
    )
    content = _tl("幻夢AI生圖—全网最强AI色图大模型，独立生图机器人", language)
    content += "\n\n@AITupian_Bot"
    await bot.send_message(
        chat_id=message.chat.id,
        text=content,
        reply_markup=builder.as_markup(),
        parse_mode=ParseMode.HTML,
    )