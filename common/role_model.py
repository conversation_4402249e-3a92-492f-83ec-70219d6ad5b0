from datetime import datetime
import json
from typing import Any, List, Optional
from pydantic import BaseModel
from common.common_constant import (
    AuditStatus,
    ChatModeType,
    Language,
    RoleChatType,
    RoleLevelType,
    RoleTag,
)
from common.models.common_res_model import SpeakerRes
from common.role_card import CharacterBook, CharacterBookEdit
from persistence.models.models import (
    ChatGroupConfig,
    Product,
    RoleAudit,
    RoleConfig,
    RoleOperationConfig,
    UserRoleShare,
)
from persistence.models.mongo_models import RolePublishHistory
from utils import date_util, json_util, str_util


class ProductResponse(BaseModel):
    mid: str
    product_type: str
    price: int
    model_name: str
    desc: str
    permission: str
    short_name: str = ""
    icon: str = ""


    # @staticmethod
    # def from_model(product: Product, model: LLMModel) -> "ProductResponse":
    #     return ProductResponse(
    #         mid=model.mid,
    #         product_type=product.type,
    #         price=product.price,
    #         model_name=model.display_name,
    #         desc=model.desc,
    #     )
    @staticmethod
    def from_product(product: Product) -> "ProductResponse":
        return ProductResponse(
            mid=product.mid,
            product_type=product.type,
            price=product.price,
            model_name=product.display_name,
            desc=product.desc,
            permission=product.permission,
            short_name=product.short_name,
            icon=product.icon,
        )

# 废弃该class
class SupportModelConfig(BaseModel):
    all_product: bool = True  # 废弃
    support_product_ids: list[str] = []


# first_message and scenario
class SceneConfig(BaseModel):
    index: int = 0
    scenario: str = ""
    first_message: str = ""


class ExampleDialog(BaseModel):
    char: str = ""
    user: str = ""


# 卡的附加信息
class RoleDataConfig(BaseModel):
    description: str = ""
    scenario: str = ""
    personality: str = ""

    # 上线之后，可删除
    first_message: str = ""
    example_dialog: str = ""
    muilte_examples: list[ExampleDialog] = []

    simple_intro: str = ""
    status_block: str = ""
    statusBlockType: str = ""
    statusBlockEnable: bool = False
    status_block_init: str = ""
    status_block_rule: str = ""

    # chat_type: str = RoleChatType.CHAT.value # deleted
    muilte_scenes: list[SceneConfig] = []
    support_all_language: bool = True
    support_languages: list[str] = []

    # replay config
    replay_len_ratio: int = 50

    def fetch_support_languages(self) -> list[str]:
        if self.support_all_language:
            return Language.fetch_all()
        return self.support_languages

    def format_new_scenes(self) -> list[SceneConfig]:
        ret_list = []
        if len(self.muilte_scenes) > 0:
            ret_list = self.muilte_scenes
        elif len(self.scenario) > 0 or len(self.first_message) > 0:
            ret_list = [
                SceneConfig(
                    index=0, scenario=self.scenario, first_message=self.first_message
                )
            ]
        for mid in ret_list:
            if "类脑社区" in mid.first_message:
                mid.first_message = ""
        ret_list = [
            mid
            for mid in ret_list
            if len(mid.first_message) > 0 or len(mid.scenario) > 0
        ]
        return ret_list

    def format_list_examples(self) -> list[ExampleDialog]:
        if len(self.muilte_examples) > 0:
            return [x for x in self.muilte_examples if x.char and x.user]
        return []


class RoleEditDetail(BaseModel):
    id: int = 0

    # operation config
    card_name: str = ""  # 卡片名称
    # 角色名称（之前老板本的字段，新版本修改为role_name，服务端做了兼容）
    name: str = ""
    role_name: str = ""  # v0718新增-角色名称
    user_role_name: str = ""  # v0718新增-用户角色名称
    introduction: str = ""  # 角色介绍 (在聊天界面展示，不参与对话内容)
    role_avatar: str = ""  # 角色头像
    role_cover: str = ""  # v0718新增-角色封面
    speaker_id: str = "zh_female_speaker_1"
    tags: str = ""  # 标签
    sub_tags: list[str] = []  # 子标签
    simple_intro: str = ""  # 营销文案（公开卡必须的）
    chat_background: str = ""  # v0718新增-聊天背景
    albums: list[str] = []  # v0718新增-相册

    # card common config
    nsfw: bool = False
    # v0718新增-角色等级('normal', 'premium')
    level_type: str = RoleLevelType.NORMAL.value
    support_all_product: bool = True  # 废弃
    support_product_ids: list[str] = []

    # v0718新增-支持所有语言
    support_all_language: bool = True
    # v0718新增-支持语言
    support_languages: list[str] = []

    # chat api config
    description: str  # 卡详细描述
    scenario: str = ""  # 场景 # v0718删除-支持muilte_scenes
    personality: str = ""  # 个性
    first_message: str = ""  # v0718删除-支持muilte_scenes
    example_dialog: str = ""  # 示例对话
    status_block: str = ""  # 状态屏蔽
    statusBlockEnable: bool = False  # 状态屏蔽是否开启
    status_block_init: str = ""  # 状态屏蔽初始化
    status_block_rule: str = ""  # 状态屏蔽规则
    book_id: str = ""
    muilte_scenes: list[SceneConfig] = []  ## v0718新增-场景列表
    # deleted
    muilte_exapmles: list[ExampleDialog] = []  # v0718新增-示例对话列表
    muilte_examples: list[ExampleDialog] = []  # v0718新增-示例对话列表

    # preset config
    chat_type: str = "Chat"
    statusBlockType: str = ""
    replay_len_ratio: Optional[int] = 50  # v0718新增-回复长度比例

    role_book: Optional[CharacterBookEdit] = {}

    author: bool = False
    support_photo: bool = False
    image_nsfw: bool = False
    real_role: bool = True
    play_type: str = ""

    # def verify_create(self):
    def publish_verify(self):
        if (
            len(self.card_name) == 0
            or len(self.introduction) == 0
            or len(self.simple_intro) == 0
            or len(self.role_avatar) == 0
        ):
            return False
        return True

    def fetch_role_name(self) -> str:
        if self.name:
            return self.name
        if self.role_name:
            return self.role_name
        if self.card_name:
            return self.card_name
        return ""

    def format_new_scenes(self) -> list[SceneConfig]:
        if len(self.muilte_scenes) > 0:
            return self.muilte_scenes
        if len(self.scenario) > 0 and len(self.first_message) > 0:
            return [
                SceneConfig(
                    index=0, scenario=self.scenario, first_message=self.first_message
                )
            ]
        return []

    def to_role_config(
        self, all_online_product_ids: list[str], uid: int = 0
    ) -> RoleConfig:
        data_config = RoleDataConfig(
            description=self.description,
            scenario=self.scenario,
            personality=self.personality,
            first_message=self.first_message,
            example_dialog=self.example_dialog,
            simple_intro=self.simple_intro,
            status_block=self.status_block,
            statusBlockType=self.statusBlockType,
            statusBlockEnable=self.statusBlockEnable,
            status_block_init=self.status_block_init,
            status_block_rule=self.status_block_rule,
            # chat_type=self.chat_type,
            muilte_scenes=self.format_new_scenes(),
            replay_len_ratio=self.replay_len_ratio if self.replay_len_ratio else 50,
            support_all_language=self.support_all_language,
            support_languages=self.support_languages,
            muilte_examples=self.muilte_examples,
        )
        # 上线删除
        if len(self.muilte_exapmles) > 0:
            data_config.muilte_examples = self.muilte_exapmles

        if self.support_product_ids:
            excluded_product_ids = [
                x for x in all_online_product_ids if x not in self.support_product_ids
            ]
        else:
            # 兼容前端历史代码（如果前端传了空列表+ 已废弃字段support_all_product）
            if self.support_all_product:
                excluded_product_ids = []
            else:
                raise ValueError("support_product_ids is empty")

        operation_config = RoleOperationConfig(
            user_role_name=self.user_role_name,
            albums=self.albums,
            chat_background=self.chat_background,
            author=self.author,
        )

        role_config = RoleConfig(
            id=self.id,
            card_name=self.card_name,
            role_name=self.fetch_role_name(),
            role_avatar=self.role_avatar,
            introduction=self.introduction,
            data_config=json.dumps(data_config.model_dump()),
            tags=self.tags,
            speaker_id=self.speaker_id,
            sub_tags=self.sub_tags,
            nsfw=self.nsfw,
            uid=uid,
            book_id=self.book_id if self.book_id else "",
            excluded_product_ids=excluded_product_ids,
            operation_config=json.dumps(operation_config.model_dump()),
            level_type=self.level_type,
            image_nsfw=self.image_nsfw,
            real_role=self.real_role,
            play_type=self.play_type,
            chat_type=self.chat_type,
        )
        return role_config

    @staticmethod
    def from_role_config(
        role_config: RoleConfig, all_online_product_ids: list[str]
    ) -> "RoleEditDetail":
        data_config = RoleDataConfig(
            **json_util.convert_to_dict(role_config.data_config)
        )
        excluded_product_ids = json_util.convert_to_list(
            role_config.excluded_product_ids
        )
        if excluded_product_ids:
            support_product_ids = [
                x for x in all_online_product_ids if x not in excluded_product_ids
            ]
        else:
            support_product_ids = all_online_product_ids

        operation_config = RoleOperationConfig(
            **json_util.convert_to_dict(role_config.operation_config)
        )

        return RoleEditDetail(
            id=role_config.id,
            card_name=role_config.card_name,
            name=role_config.role_name,
            role_name=role_config.role_name,
            user_role_name=operation_config.user_role_name,
            introduction=role_config.introduction,
            role_avatar=str_util.format_avatar(role_config.role_avatar),
            speaker_id=role_config.speaker_id,
            tags=role_config.tags,
            sub_tags=json_util.convert_to_list(role_config.sub_tags),
            simple_intro=data_config.simple_intro,
            nsfw=role_config.nsfw,
            level_type=role_config.level_type,
            # support_all_product=model_config.all_product,#废弃
            support_product_ids=support_product_ids,
            support_all_language=data_config.support_all_language,
            support_languages=data_config.support_languages,
            description=data_config.description,
            scenario=data_config.scenario,
            personality=data_config.personality,
            first_message=data_config.first_message,
            example_dialog=data_config.example_dialog,
            status_block=data_config.status_block,
            statusBlockEnable=data_config.statusBlockEnable,
            status_block_init=data_config.status_block_init,
            status_block_rule=data_config.status_block_rule,
            statusBlockType=data_config.statusBlockType,
            replay_len_ratio=data_config.replay_len_ratio,
            book_id=role_config.book_id,
            muilte_scenes=data_config.format_new_scenes(),
            chat_background=operation_config.chat_background,
            albums=operation_config.albums,
            muilte_exapmles=data_config.muilte_examples,  # 上线删除
            muilte_examples=data_config.muilte_examples,
            author=operation_config.author,
            support_photo=role_config.support_photo,
            image_nsfw=role_config.image_nsfw,
            real_role=role_config.real_role,
            play_type=role_config.play_type,
            chat_type = role_config.chat_type,
        )


# 定义角色模型
class RoleRes(BaseModel):
    id: int  # 角色ID
    card_name: str = ""  # 卡片名称
    role_name: str  # 角色名称
    introduction: str  # 角色介绍
    role_avatar: str  # 角色头像URL，使用HttpUrl确保传入的是有效的URL
    description: str  # 描述
    personality: str = ""  # 个性
    scenario: str  # 场景
    first_message: str  # 第一条消息
    example_dialog: str = ""  # 示例对话
    tags: str = ""  # 标签
    speaker_id: str
    sub_tags: list[str] = []
    simple_intro: str = ""
    nsfw: bool = False
    status_block: str = ""
    statusBlockType: str = ""
    statusBlockEnable: bool = False
    status_block_init: str = ""
    status_block_rule: str = ""
    chat_type: str = ""
    popular_count: int = 0  # 热度
    support_all_product: bool = True
    support_product_ids: list[str] = []
    support_products: list[ProductResponse] = []
    book_id: str = ""
    audit_status: str = ""
    audit_reason: str = ""
    user_id: int = 0
    author: bool = True
    author_name: str = ""
    image_nsfw: bool = False
    play_type: str = ""

    @staticmethod
    def from_model_with_config(
        role: RoleConfig,
        full: bool = False,
        support_all_product: bool = True,
        product_ids: list[str] = [],
    ) -> "RoleRes":
        data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
        role_res = RoleRes(
            id=role.id,
            card_name=role.card_name if role.card_name else role.role_name,
            role_name=role.role_name,
            introduction=role.introduction,
            role_avatar=str_util.format_avatar(role.role_avatar),
            description=data_config.description if full else "",
            personality=data_config.personality if full else "",
            scenario=data_config.scenario,
            first_message=data_config.first_message if full else "",
            example_dialog=data_config.example_dialog if full else "",
            tags=role.tags if len(role.tags.strip()) > 0 else "其他",
            speaker_id=role.speaker_id,
            sub_tags=json_util.convert_to_list(role.sub_tags),
            simple_intro=data_config.simple_intro,
            nsfw=role.nsfw,
            status_block=data_config.status_block if full else "",
            statusBlockType=data_config.statusBlockType if full else "",
            statusBlockEnable=data_config.statusBlockEnable,
            status_block_init=data_config.status_block_init if full else "",
            status_block_rule=data_config.status_block_rule if full else "",
            chat_type=role.chat_type,
            image_nsfw=role.image_nsfw,
            play_type=role.play_type,
        )
        operation_config = RoleOperationConfig(
            **json_util.convert_to_dict(role.operation_config)
        )
        role_res.support_all_product = support_all_product
        role_res.support_product_ids = product_ids
        role_res.book_id = role.book_id
        role_res.user_id = role.uid
        role_res.author = operation_config.author
        return role_res

    @staticmethod
    def from_model(
        role: RoleConfig,
        full: bool = False,
    ) -> "RoleRes":
        return RoleRes.from_model_with_config(role, full, True, [])


class RoleFilterRequest(BaseModel):
    nsfw: bool = True
    tag: str = ""
    sub_tags: list[str] = []
    product_id: str = ""
    real_role: Optional[bool] = None
    offset: int = 0
    limit: int = 10
    language: str = Language.ZH.value
    play_type: str = ""
    chat_types: list[str] = []


class RoleFilterBrief(BaseModel):
    id: int
    card_name: str
    role_name: str = ""
    introduction: str = ""
    role_avatar: str = ""
    tags: str = ""
    speaker_id: str = ""
    sub_tags: list[str] = []
    simple_intro: str = ""
    nsfw: bool = False
    popular_count: int = 0

    @staticmethod
    def from_model(role: RoleConfig) -> "RoleFilterBrief":
        data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
        return RoleFilterBrief(
            id=role.id,
            card_name=role.card_name,
            role_name=role.role_name,
            introduction=role.introduction,
            role_avatar=str_util.format_avatar(role.role_avatar),
            tags=role.tags,
            speaker_id=role.speaker_id,
            sub_tags=json_util.convert_to_list(role.sub_tags),
            simple_intro=data_config.simple_intro,
            nsfw=role.nsfw,
        )


class TagRes(BaseModel):
    tag_name: str  # 标签名称
    order: int  # 排序
    content: List[RoleRes]  # 内容，是多个角色的列表


# -------------response----------------#
class RoleFilterResponse(BaseModel):
    count: int = 0
    tags: list[str] = []
    current_tag: str = ""
    current_sub_tag: str = ""
    sub_tags: list[str] = []
    roles: list = []
    latest_card_created_at: int = 0


class RoleConfigResponse(BaseModel):
    sum_token_limit: int = 6000
    replay_max_token: dict = RoleLevelType.replayMaxTokenConfig()
    role: Optional[RoleEditDetail] = None
    role_token_count: dict = {}
    speakers: list[SpeakerRes] = []
    all_tags: list[str] = []
    tag_orders: list[str] = []
    chat_products: list[ProductResponse] = []
    sub_tag_category_list: list[dict] = []


class UserRoleBrief(BaseModel):
    id: int
    card_name: str
    role_name: str = ""
    introduction: str = ""
    role_avatar: str = ""
    tags: str = ""
    sub_tags: list[str] = []
    simple_intro: str = ""
    audit_status: str = ""
    audit_reason: str = ""
    published_at: Optional[datetime] = None
    published_version: int = 0
    private_card: bool = False
    public_role_id: int = 0
    popular_count: str = "0"
    deducted_popular_count: str = "0"
    final_popular_count: str = "0"
    like_count: int = 0
    author: bool = True
    author_name: str = ""
    author_avatar: str = ""
    author_id: int = 0
    support_photo: bool = False
    nsfw: bool = False
    image_nsfw: bool = False
    chat_type: str = RoleChatType.CHAT.value
    play_type: str = ""
    reward_desc: str = ""
    receive_reward:bool = False

    @staticmethod
    def from_config_and_audit(
        role: RoleConfig,
        audit: Optional[RoleAudit] = None,
        nickname: Optional[str] = "",
        admin: bool = False,
    ) -> "UserRoleBrief":
        data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
        operation_config = RoleOperationConfig(
            **json_util.convert_to_dict(role.operation_config)
        )
        user_role_brief = UserRoleBrief(
            id=role.id,
            card_name=role.card_name,
            role_name=role.role_name,
            introduction=role.introduction,
            role_avatar=str_util.format_avatar(role.role_avatar),
            tags=role.tags,
            sub_tags=json_util.convert_to_list(role.sub_tags),
            simple_intro=data_config.simple_intro,
            private_card=not bool(role.privacy),
            support_photo=role.support_photo,
            nsfw=role.nsfw,
            image_nsfw=role.image_nsfw,
            chat_type=role.chat_type,
            play_type=role.play_type,
        )
        if audit:
            user_role_brief.audit_status = audit.status
            user_role_brief.audit_reason = audit.reason
            user_role_brief.published_at = audit.published_at
            user_role_brief.published_version = audit.published_version
            user_role_brief.public_role_id = audit.open_role_id
            user_role_brief.receive_reward = audit.receive_reward
            

        user_role_brief.author_name = nickname if nickname else ""
        if user_role_brief.id == 177:
            user_role_brief.author_name = "ST官方竞赛角色扮演方向优胜者"

        user_role_brief.author = operation_config.author
        if user_role_brief.author or admin:
            user_role_brief.author_id = role.uid
        else:
            user_role_brief.author_id = 0
        return user_role_brief


# 后台审核列表
class ManageRoleBrief(BaseModel):
    id: int
    card_name: str
    role_name: str = ""
    introduction: str = ""
    description: str = ""
    role_avatar: str = ""
    tags: str = ""
    sub_tags: list[str] = []
    simple_intro: str = ""
    audit_status: str = ""
    audit_reason: str = ""
    published_at: Optional[datetime] = None
    published_version: int = 0

    @staticmethod
    def from_config_and_audit(
        role: RoleConfig, audit: Optional[RoleAudit]
    ) -> "ManageRoleBrief":
        data_config = RoleDataConfig(**json_util.convert_to_dict(role.data_config))
        user_role_brief = ManageRoleBrief(
            id=role.id,
            card_name=role.card_name,
            role_name=role.role_name,
            introduction=role.introduction,
            description=data_config.description,
            role_avatar=str_util.format_avatar(role.role_avatar),
            tags=role.tags,
            sub_tags=json_util.convert_to_list(role.sub_tags),
            simple_intro=data_config.simple_intro,
        )
        if audit:
            user_role_brief.audit_status = audit.status
            user_role_brief.audit_reason = audit.reason
            user_role_brief.published_at = audit.published_at
            user_role_brief.published_version = audit.published_version
        return user_role_brief


class ManageRoleFilterResponse(BaseModel):
    count: int = 0
    tags: list[str] = []
    sub_tags: list[str] = []
    roles: list[ManageRoleBrief] = []


class ChatGroupEdit(BaseModel):
    id: int = 0
    user_id: int = 0
    name: str = ""
    simple_intro: str = ""
    introduction: str = ""
    scenario: str = ""
    avatar: str = ""
    sub_tags: list[str] = []
    role_ids: list[int] = []
    public_card: bool = False
    author: bool = False


class AdminChatGroupEdit(BaseModel):
    name: str = ""
    simple_intro: str = ""
    introduction: str = ""
    scenario: str = ""
    sub_tags: list[str] = []
    author: bool = False


class UserChatGroupEdit(BaseModel):
    id: int = 0
    name: str = ""
    simple_intro: str = ""
    introduction: str = ""
    scenario: str = ""
    sub_tags: list[str] = []
    publish_card: bool = False
    author: bool = False
    role_ids: list[int] = []


class AuditChatGroupEdit(BaseModel):
    name: str = ""
    simple_intro: str = ""
    introduction: str = ""
    scenario: str = ""
    avatar: str = ""
    sub_tags: list[str] = []
    author: bool = False


class ChatGroupSimple(BaseModel):
    id: int = 0
    name: str = ""
    simple_intro: str = ""
    introduction: str = ""
    avatar: str = ""
    sub_tags: list[str] = []


class ChatGroupDetail(BaseModel):
    id: int = 0
    name: str = ""
    simple_intro: str = ""
    introduction: str = ""
    scenario: str = ""
    sub_tags: list[str] = []
    user_id: int = 0
    roles: list[UserRoleBrief] = []
    deleted_roles: list[UserRoleBrief] = []
    public: bool = False
    private_card: bool = False
    audit_status: str = ""
    audit_reason: str = ""
    author: bool = False
    author_name: str = ""
    author_id: int = 0
    popular_count: str = ""
    def_language: str = "zh"
    favorite: bool = False
    nsfw: bool = False

    @staticmethod
    def from_model(
        group_config: ChatGroupConfig,
        roles: List[UserRoleBrief],
        audit: Optional[RoleAudit] = None,
        popular_count: int = 0,
    ) -> "ChatGroupDetail":
        result = ChatGroupDetail(
            id=group_config.id,
            name=group_config.name,
            simple_intro=group_config.simple_intro,
            introduction=group_config.introduction,
            scenario=group_config.scenario,
            sub_tags=json_util.convert_to_list(group_config.sub_tags),
            user_id=group_config.user_id,
            roles=roles,
            public=group_config.public,
            private_card=not group_config.public,
            audit_status=audit.status if audit else "",
            audit_reason=audit.reason if audit else "",
            popular_count=str_util.format_hot(popular_count),
            author=group_config.author,
            # author_id=group_config.user_id,
            def_language=group_config.def_language,
            nsfw=group_config.nsfw,
        )
        if result.author:
            result.author_id = group_config.user_id
        else:
            result.author_id = 0
        return result


class RecentChat(BaseModel):
    mode_type: str = ""
    mode_target_id: int = 0
    record_count: int = 0
    timestamp: int = 0
    role: Optional[UserRoleBrief] = None
    group: Optional[ChatGroupDetail] = None


class AuditDetail(BaseModel):
    mode_type: str = ""
    mode_target_id: int = 0
    role: Optional[UserRoleBrief] = None
    group: Optional[ChatGroupDetail] = None


class CardDetail(BaseModel):
    mode_type: str = ""
    mode_target_id: int = 0
    role: Optional[UserRoleBrief] = None
    group: Optional[ChatGroupDetail] = None

    def self_key(self) -> str:
        return f"{self.mode_type}_{self.mode_target_id}"

    @staticmethod
    def from_group(group: ChatGroupDetail) -> "CardDetail":
        return CardDetail(
            mode_type=ChatModeType.GROUP.value, mode_target_id=group.id, group=group
        )

    @staticmethod
    def from_role(role: UserRoleBrief) -> "CardDetail":
        return CardDetail(
            mode_type=ChatModeType.SINGLE.value, mode_target_id=role.id, role=role
        )

    @staticmethod
    def key(mode_type: str, mode_target_id: int) -> str:
        return f"{mode_type}_{mode_target_id}"

    @staticmethod
    def key_role(mode_target_id: int) -> str:
        return f"{ChatModeType.SINGLE.value}_{mode_target_id}"

    @staticmethod
    def key_group(mode_target_id: int) -> str:
        return f"{ChatModeType.GROUP.value}_{mode_target_id}"

    @staticmethod
    def key_type(key: str):
        return key.split("_")[0]

    @staticmethod
    def key_target(key: str):
        return int(key.split("_")[1])

    def fetch_author_id(self):
        if self.role and self.role.author and self.role.author_id > 0:
            return self.role.author_id
        if self.group and self.group.author and self.group.author_id > 0:
            return self.group.author_id
        return 0

    def update_nickname(self, nickname: str):
        if self.role:
            self.role.author_name = nickname
        if self.group:
            self.group.author_name = nickname


class UserSnapshotCardDetail(BaseModel):
    id: int = 0
    title: str = ""
    card_detail: CardDetail

class UserChatRoleRes(BaseModel):
    create_roles: list = []
    create_groups: list = []
    recent_chats: list = []
    publish_card_tips: str = ""
    display_summary_rank_tag:str = ""


class UserRolePublicStat(BaseModel):
    id: int
    card_name: str = ""
    role_name: str = ""
    nickname: str = ""
    user_count: int = 0


class RoleDetail(BaseModel):
    id: int
    card_name: str = ""
    role_name: str = ""
    introduction: str = ""
    role_avatar: str = ""
    first_message: str = ""
    scenario: str = ""
    support_product_ids: list[str] = []
    favorite: bool = False
    like: bool = False
    like_count: int = 0
    dislike: bool = False
    dislike_count: int = 0
    sub_tags: list[str] = []
    author: bool = False
    author_name: str = ""
    author_id: int = 0
    nsfw: bool = False
    image_nsfw: bool = False
    private_card: bool = False
    play_type: str = ""
    sum_token: int = 0
    token_map: dict = {}

    @staticmethod
    def from_config(
        role_config: RoleConfig, all_online_product_ids: list[str]
    ) -> "RoleDetail":
        operation_config = RoleOperationConfig(
            **json_util.convert_to_dict(role_config.operation_config)
        )
        mid_data = json_util.convert_to_dict(role_config.data_config)
        data_config = RoleDataConfig(**mid_data)
        scenes = data_config.format_new_scenes()

        excluded_product_ids = json_util.convert_to_list(
            role_config.excluded_product_ids
        )
        if excluded_product_ids:
            support_product_ids = [
                x for x in all_online_product_ids if x not in excluded_product_ids
            ]
        else:
            support_product_ids = all_online_product_ids
        role_detail = RoleDetail(
            id=role_config.id,
            card_name=role_config.card_name,
            role_name=role_config.role_name,
            introduction=role_config.introduction,
            role_avatar=str_util.format_avatar(role_config.role_avatar),
            first_message=scenes[0].first_message if len(scenes) > 0 else "",
            scenario=scenes[0].scenario if len(scenes) > 0 else "",
            nsfw=role_config.nsfw,
            sub_tags=json_util.convert_to_list(role_config.sub_tags),
            support_product_ids=support_product_ids,
            image_nsfw=role_config.image_nsfw,
            private_card=not bool(role_config.privacy),
            play_type=role_config.play_type,
        )
        role_detail.author = operation_config.author
        if role_detail.author:
            role_detail.author_id = role_config.uid
        else:
            role_detail.author_id = 0
        return role_detail


# audit model

class RolePublishDetail(BaseModel):
    user_id: int
    mode_type: str
    mode_target_id: int
    publish_version: int
    submit_at: str
    published_at: str
    publish_data: dict
    original_data: dict = {} #该次审核中作者提交的原始数据
    status: str = ""
    status_desc: str = ""
    reason: str = ""

    @staticmethod
    def from_history(history: RolePublishHistory) -> "RolePublishDetail":
        result = RolePublishDetail(
            user_id=history.user_id,
            mode_type=history.mode_type,
            mode_target_id=history.mode_target_id,
            publish_version=history.publish_version,
            submit_at=date_util.datetime2utc8str(history.submit_at),
            published_at=date_util.datetime2utc8str(history.published_at),
            publish_data=history.publish_data,
            status=history.status,
            status_desc=AuditStatus.get_desc(history.status),
            reason=history.reason,
        )
        if history.original_data:
            result.original_data = history.original_data
        return result


class RoleAuditDetail(BaseModel):
    mode_type: str
    mode_target_id: int
    role: Optional[RoleEditDetail] = None
    group: Optional[ChatGroupEdit] = None


class RoleFilterResponseV1(BaseModel):
    count: int = 0
    tags: list[str] = []
    current_tag: str = ""
    current_sub_tag: str = ""
    current_sub_tags: list[str] = []
    sub_tags: list[str] = []
    card_list: list = []
    latest_card_created_at: int = 0


class RoleFilterResponseV2(BaseModel):
    count: int = 0
    card_list: list = []
    latest_card_created_at: int = 0


class UserRoleShareDetail(BaseModel):
    id: int
    user_id: int
    role_id: int
    conversation_id: str
    title: str = ""
    description: str = ""
    content_snapshot: list = []  # CharHistory type
    created_at: int = 0
    message_count: int = 0

    @staticmethod
    def from_model(
        role_share: UserRoleShare, history_list: list
    ) -> "UserRoleShareDetail":
        return UserRoleShareDetail(
            id=role_share.id,
            user_id=role_share.user_id,
            role_id=role_share.role_id,
            conversation_id=role_share.conversation_id,
            title=role_share.title,
            description=role_share.description,
            content_snapshot=history_list,
            created_at=int(role_share.created_at.timestamp()),
            message_count=role_share.message_count,
        )


class UserRoleShareBriefDetail(BaseModel):
    id: int
    user_id: int
    role_id: int
    title: str = ""
    description: str = ""
    created_at: int = 0
    message_count: int = 0

    @staticmethod
    def from_model(role_share: UserRoleShare) -> "UserRoleShareBriefDetail":
        return UserRoleShareBriefDetail(
            id=role_share.id,
            user_id=role_share.user_id,
            role_id=role_share.role_id,
            title=role_share.title,
            description=role_share.description,
            created_at=int(role_share.created_at.timestamp()),
            message_count=role_share.message_count,
        )


class UserRoleShareRequest(BaseModel):
    role_id: int
    title: str = ""
    description: str = ""
    conversation_id: str


class RoleShareResponse(BaseModel):
    chat_list: list
    share_title: str
    share_description: str
    role_id: int
    role_name: str = ""
    role_avatar: str = ""
    sharer_uid: int
    sharer_name: str = ""
    sharer_avator: str = ""
    author_id: int
    author_name: str = ""
    author_avator: str = ""
    message_count: int = 0


class RoleShareBriefResponse(BaseModel):
    chat_list: list = []
    share_id: int
    share_title: str = ""
    share_description: str = ""
    role_id: int
    role_name: str = ""
    role_avatar: str = ""
    sharer_uid: int
    sharer_name: str = ""
    sharer_avator: str = ""
    created_at: int = 0
    message_count: int = 0

    @staticmethod
    def from_model(role_share: UserRoleShareBriefDetail) -> "RoleShareBriefResponse":
        return RoleShareBriefResponse(
            share_id=role_share.id,
            share_title=role_share.title,
            share_description=role_share.description,
            role_id=role_share.role_id,
            sharer_uid=role_share.user_id,
            created_at=role_share.created_at,
            message_count=role_share.message_count,
        )

    @staticmethod
    def from_detail_model(role_share: UserRoleShareDetail) -> "RoleShareBriefResponse":
        return RoleShareBriefResponse(
            share_id=role_share.id,
            share_title=role_share.title,
            share_description=role_share.description,
            role_id=role_share.role_id,
            sharer_uid=role_share.user_id,
            created_at=role_share.created_at,
            message_count=role_share.message_count,
        )


class UserShareCheckResponse(BaseModel):
    check_success: bool = True
    forbidden_words: list = []
    message: str = ""
    ban_categories: list = []
    category_scores: dict = {}


class TagNode(BaseModel):
    key: str
    name: str
    sub_tags: list["TagNode"] = []


class SelectOption(BaseModel):
    key: str
    name: str
    category: str = ""

    @staticmethod
    def init(key: str, name: str, category: str = "") -> "SelectOption":
        return SelectOption(key=key, name=name, category=category)
