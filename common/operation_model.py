from typing import Optional
from pydantic import BaseModel

from common.common_constant import (
    ChatPlatform,
    PopupPosition,
    PopupShowPeriod,
    PopupType,
)
from persistence.models.models import PopupConfig
from utils import date_util, json_util


class Popup(BaseModel):
    type: str = ""
    title: str = ""
    content: str = ""
    button_text: str = ""
    button_link_url: str = ""
    button_action_type: str = ""
    show_close_icon: bool = True
    extra: str = ""

    @staticmethod
    def from_model(popup_type: PopupType, popup_config: Optional[PopupConfig]):
        if not popup_config:
            return None
        return Popup(
            type=popup_type.value,
            title=popup_config.title,
            content=popup_config.content,
            button_text=popup_config.button_text,
            button_link_url=popup_config.button_link_url,
            button_action_type=popup_config.button_action_type,
            show_close_icon=popup_config.show_close_icon,
            )


class PopupResponse(BaseModel):
    popup: Optional[Popup] = None

    @staticmethod
    def of_model(popup: Optional[Popup]):
        return PopupResponse(popup=popup)


class PopupConfigDetail(BaseModel):
    id: int
    title: str
    content: str
    start_at: int
    end_at: int
    show_period: PopupShowPeriod = PopupShowPeriod.ONCE
    chat_platform: list[ChatPlatform] = []
    position: PopupPosition = PopupPosition.HOME
    published: bool = False
    created_at:int = 0
    button_text: str = ""
    button_action_type: str = ""
    button_link_url: str = ""
    show_close_icon: bool = True
    user_scopes: list[str] = []

    @staticmethod
    def from_model(popup_config: PopupConfig):
        return PopupConfigDetail(
            id=popup_config.id,
            title=popup_config.title,
            content=popup_config.content,
            start_at=int(date_util.add_utc_zone(popup_config.start_at).timestamp()),
            end_at=int(date_util.add_utc_zone(popup_config.end_at).timestamp()),
            show_period=PopupShowPeriod(popup_config.show_period),
            chat_platform=[ChatPlatform(x) for x in popup_config.chat_platform],
            position=PopupPosition(popup_config.position),
            published=popup_config.published,
            created_at=int(date_util.add_utc_zone(popup_config.created_at).timestamp()),
            button_text=popup_config.button_text,
            button_action_type=popup_config.button_action_type,
            button_link_url=popup_config.button_link_url,
            show_close_icon=popup_config.show_close_icon,
            user_scopes=json_util.convert_to_list(popup_config.user_scopes),
        )


class PopupConfigEdit(BaseModel):
    id: int = 0
    title: str
    content: str
    start_at: int
    end_at: int
    show_period: PopupShowPeriod = PopupShowPeriod.ONCE
    chat_platform: list[ChatPlatform] = []
    position: PopupPosition = PopupPosition.HOME
    button_text: str = ""
    button_action_type: str = ""
    button_link_url: str = ""
    show_close_icon: bool = True
    user_scopes:list[str] = []

    
