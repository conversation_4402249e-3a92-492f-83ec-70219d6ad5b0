from enum import Enum
from typing import List, Optional
from pydantic import BaseModel

from persistence.models.models import RoleConfig
from utils import str_util


# 一期需要考虑的字段
# 1. 按照不同位置插入（固定位置与自定义位置）、深度与顺序 以及触发方式（关键词、常量并支持正则）
# 2. 插入概率
# 3. 插入逻辑相关处理
# 4. 递归相关排除与处理

# 5. 分组相关处理
# 6. 自动化相关处理
# 7. 筛选角色与排除角色


# 相关设计
# 1. 角色书配置，包含角色的全局配置
# Activation Settings
# Recursive Scan（递归扫描）Case-sensitive（区分大小写）Match wholewords（匹配整个单词）
# 2、角色的条目信息配置


# 世界书条目扩展信息
class EntryExtensions(BaseModel):

    # 插入深度（已启用）
    depth: Optional[int] = None
    # 位置(角色前后、注释前后、指定位置(系统、人、AI)) （已启用）
    # 0,1（角色前后）,2,3（注释前后）,4(指定位置，关注一下角色,通过role字段确认类型)
    position: Optional[int] = None
    # 插入位置的角色类型（已启用）
    #  0 系统 1 机器人 2 AI
    # position=4时，role必填（其他为空）
    role: Optional[int] = None

    # 通用配置（已启用）
    # display_index: int = 0  # 显示的索引，需要按照索引排序

    # 概率相关配置（已启用）
    # 概率：0-100，useProbability默认可为空，不为空时，probability必填
    probability: Optional[int] = None  # 概率
    useProbability: Optional[bool] = None  # 是否使用概率

    # 选择逻辑匹配相关(0,1,2,3)条件匹配逻辑 配合keys与【无配置】secondary_keys使用
    # 当constant为False时，必填项（已启用）
    # 0 （AND ANY）仅当主键和任何一个可选过滤键处于扫描上下文中时才激活该条目。
    # 1 （NOT ALL) 如果所有可选过滤器都在扫描上下文中，则尽管有主键触发器，仍会阻止激活条目
    # 2 （NOT ANY）如果主键触发器不在扫描上下文中，则激活条目
    # 3 （AND ALL）仅当主键和所有可选过滤键都在扫描上下文中时才激活该条目。
    # selectiveLogic: Optional[int] = None
    # 扫描深度 （扫描历史消息的深度，去匹配世界书（已启用））
    # 0 只扫描递归条目与作者注释，1扫描最后一条消息，>1 扫描最后N条消息）【无配置】
    scan_depth: Optional[int] = None

    # 自动化相关，指令触发相关（暂时不考虑）
    # automation_id: str       # 自动化ID【无配置】

    # vectorized:bool         # 是否向量化、搭配constant、disable使用

    # 角色相关 未使用到，暂时不考虑
    # role:int                # 角色
    # characterFilter:dict    # 角色过滤器
    # 分组相关（还没搞明白，暂时不考虑，卡上基本上都没有相关配置）
    # group: str             # 分组【无配置】
    # group_override: bool   # 是否覆盖分组【无配置】
    # group_weight: int      # 分组权重【无配置】
    # use_group_scoring: bool # 是否使用分组评分【无配置】

    # 全局配置相关
    # exclude_recursion:bool # 是否排除递归（不能被其他条目触发与激活）【无配置】
    # prevent_recursion: bool # 是否防止递归（不能触发激活其他条目）【无配置】

    # match_whole_word: bool  # 是否匹配整个单词【无配置】
    # case_sensitive: bool     # 是否区分大小写【无配置】
    @staticmethod
    def get_role_str(input_role: int):
        if input_role == 0:
            return "system"
        elif input_role == 1:
            return "user"
        elif input_role == 2:
            return "assistant"
        return "user"

    @staticmethod
    def get_role_int(input_role: str):
        if input_role == "system":
            return 0
        elif input_role == "user":
            return 1
        elif input_role == "assistant":
            return 2
        return 1

    def load_role_str(self):
        if self.role is None:
            return "user"
        return EntryExtensions.get_role_str(self.role)


# 世界书条目信息
class BookEntry(BaseModel):
    id: int = 0
    # 关键词列表 （已启用）
    keys: list[str] = []
    # 次要关键词列表（已启用）
    # secondary_keys: list[str] = []
    # 备注（已启用）
    comment: Optional[str] = ""
    # 条目内容（已启用）
    content: str = ""
    # 是否为常量(True:常量，False:非常量&关键词触发)（已启用）
    constant: Optional[bool] = None
    # 是否同时使用keys与secondary_keys触发，默认为True
    # selective: Optional[bool] = None
    # 插入顺序，优先级，越大越优先，靠近最新消息（已启用）
    insertion_order: Optional[int] = 0
    # 是否启用（已启用）
    enabled: Optional[bool] = None
    # 扩展信息配置
    extensions: Optional[EntryExtensions] = None

    def load_order(self):
        return self.insertion_order if self.insertion_order is not None else 0

    def load_scan_depth(self):
        if not self.extensions or not self.extensions.scan_depth:
            return 1
        return self.extensions.scan_depth


class CharacterBook(BaseModel):
    name: Optional[str] = ""
    book_id: Optional[str] = ""
    entries: Optional[list[BookEntry]] = []
    enabled: bool = True
    extensions: Optional[dict] = {}

    created_at: int = 0
    updated_at: int = 0


class RoleCardInfo(BaseModel):
    name: str = ""
    description: str = ""
    personality: str = ""
    scenario: str = ""
    first_mes: str = ""  # 第一句话
    mes_example: str = ""  # 例句
    creator_notes: str = ""  # 创作者注释
    system_prompt: str = ""  # 系统提示
    post_history_instructions: str = ""  # 发送历史消息指令
    tags: list[str] = []  # 标签
    creator: str = ""  # 创作者
    character_version: str = ""  # 角色版本
    alternate_greetings: list[str] = []  # 备选问候语
    spec: str = ""  # 版本信息事例（chara_card_v2）
    spec_version: str = ""  # 版本号
    sub_tags: list[str] = []  # 子标签

    # 世界书相关
    character_book: Optional[CharacterBook] = None
    extensions: Optional[dict] = {}

    #
    cus_data: Optional[dict] = {}

    def exist_book(self) -> bool:
        return self.character_book is not None and len(self.character_book.entries) > 0

    def format_fist_mes(self):
        if "类脑社区" in self.first_mes:
            return ""
        return self.first_mes


class TavernCard(BaseModel):
    name: str = ""
    description: str = ""
    personality: str = ""
    scenario: str = ""
    first_mes: str = ""
    mes_example: str = ""
    creatorcomment: str = ""
    avatar: str = ""
    tags: list[str] = []
    talkativeness: str = "0.5"
    spec: str = ""
    spec_version: str = ""

    # v2
    data: Optional[RoleCardInfo] = None

    def to_role_card(self):
        if self.spec == "chara_card_v2":
            return self.data

        return RoleCardInfo(
            name=self.name,
            description=self.description,
            personality=self.personality,
            scenario=self.scenario,
            first_mes=self.first_mes,
            mes_example=self.mes_example,
            creator_notes=self.creatorcomment,
            system_prompt=self.avatar,
            tags=self.tags,
            creator="",
            character_version="",
            alternate_greetings=[],
            spec=self.spec,
            spec_version=self.spec_version,
            sub_tags=[],
            character_book=None,
        )

    def from_model(
        role_config: RoleConfig, book: CharacterBook = None, user_name: str = None
    ) -> "TavernCard":
        tavern_card = TavernCard(
            name=role_config.card_name,
            description=role_config.data_config["description"],
            personality=role_config.data_config["personality"],
            scenario=role_config.data_config["scenario"],
            first_mes=role_config.data_config["first_message"],
            mes_example=role_config.data_config["example_dialog"],
            creatorcomment="",
            avatar=str_util.format_avatar(role_config.role_avatar),
            tags=(
                []
                if role_config.tags is None or len(role_config.tags) == 0
                else [role_config.tags]
            ),
            talkativeness="0.5",
            spec="chara_card_v2",
            spec_version="2.0",
        )
        role_card_info = RoleCardInfo(
            name=role_config.card_name,
            description=role_config.data_config["description"],
            personality=role_config.data_config["personality"],
            scenario=role_config.data_config["scenario"],
            first_mes=role_config.data_config["first_message"],
            mes_example=role_config.data_config["example_dialog"],
            creator_notes="",
            system_prompt="",
            tags=(
                []
                if role_config.tags is None or len(role_config.tags) == 0
                else [role_config.tags]
            ),
            creator=user_name,
            character_version="1.0",
            alternate_greetings=[],
            spec="chara_card_v2",
            spec_version="2.0",
            sub_tags=role_config.sub_tags,
            character_book=book,
        )
        role_card_info.extensions = {
            "talkativeness": "0.5",
            "fav": False,
            "world": book.name if book is not None else "",
        }
        tavern_card.data = role_card_info
        return tavern_card


class CharacterBookEdit(BaseModel):
    name: str = ""
    book_id: str = ""
    entries: list[BookEntry] = []
    enabled: bool = True
    extensions: dict = {}

    @staticmethod
    def from_model(book: CharacterBook) -> "CharacterBookEdit":
        return CharacterBookEdit(
            name=book.name,
            book_id=book.book_id,
            entries=book.entries,
            enabled=book.enabled,
            extensions=book.extensions,
        )


class ThirdPlatform(Enum):
    RISUAI = "realm.risuai.net"
    ROCHAT = "rochat.ai"


class ThirdCardStatus(Enum):
    NEW = "new"
    PROCESS = "process"
    INSTALLED = "installed"
    HIDDEN = "hidden"
    ERROR = "error"


class ThirdCardInfo(BaseModel):
    card_id: str
    created_at: str
    platform: str
    image_url: str
    popularity: int
    tags: List[str] = []
    status: str = ""
    language: str = ""
    full_image_url: str = ""

    contains_book: bool = False
    card_name: str = ""
    role_name: str = ""
    role_id: int = 0

    # 世界书相关

    entry_sum_num: int = 0
    entry_enable_num: int = 0
    entry_constant_num: int = 0
    entry_constant_token: int = 0
    entry_selective_num: int = 0
    entry_selective_token: int = 0

    entry_valid_num: int = 0
    entry_valid_token: int = 0

    def format_tags(self):
        if self.platform == ThirdPlatform.ROCHAT.value:
            return "RoChat"
        return "RisuAi"

    def load_image_url(self):
        return (
            "https://sgp-image-1323765209.cos.ap-singapore.myqcloud.com/"
            + self.platform
            + "/"
            + self.image_url
        )
