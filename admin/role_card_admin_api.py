from hashlib import md5
from io import BytesIO
import io
import json
import logging
import os
import time
import uuid
from PIL import Image
from fastapi import APIRouter, Depends, Response, UploadFile
from fastapi.responses import JSONResponse, StreamingResponse
from common.role_card import (
    RoleCardInfo,
    TavernCard,
    ThirdCardInfo,
    ThirdPlatform,
)
from common.role_model import RoleDataConfig, RoleEditDetail, SceneConfig
from persistence import char_book_dao, third_card_dao
from persistence.models.models import RoleConfig
from presets import role_fill_preset
from services.role import third_card_service
from services.role_config_service import RoleConfigService
from dotenv import load_dotenv
from urllib.parse import quote

from .auth import is_op_user

from utils import image_util, json_util, response_util, role_util

load_dotenv()

char_book_router = APIRouter(dependencies=[Depends(is_op_user)])

log = logging.getLogger(__name__)


UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


@char_book_router.post("/role_card/analysis/img")
async def analysis_card_img(image: UploadFile):
    analysis_card_info = await RoleConfigService.analysis_card_img(image)
    return JSONResponse(content=analysis_card_info)


@char_book_router.get("/role_card/export/json")
async def export_json(role_id: int):
    export_ret = await RoleConfigService.load_tavern_card(role_id=role_id)
    return JSONResponse(content=export_ret.model_dump())


@char_book_router.get("/role_card/export/img")
async def export_img(role_id: int):
    export_ret = await RoleConfigService.load_tavern_card(role_id=role_id)
    if export_ret is None:
        return JSONResponse(content={"message": "No card detected"}, status_code=400)
    img_byte_arr = await image_util.build_card_image(export_ret)
    if img_byte_arr is None:
        return JSONResponse(content={"message": "No card detected"}, status_code=400)

    img_byte_arr = img_byte_arr.getvalue()  #
    encoded_filename = quote(export_ret.name + ".png", safe="")  # 对文件名进行编码
    headers = {
        "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
    }
    return StreamingResponse(
        BytesIO(img_byte_arr), media_type="image/png", headers=headers
    )


# @char_book_router.post("/role_card/import/batch/imgs")
# async def batch_import(file_path: str):
#     # 读取文件
#     all_files = []
#     for root, dirs, files in os.walk(file_path):
#         for file in files:
#             all_files.append(os.path.join(root, file))
#     for file_path in all_files:
#         try:
#             file_name = f"{uuid.uuid4().hex}"
#             image = Image.open(file_path)
#             tavern_card = image_util.load_new_image_info(image)
#             image = image_util.remove_image_info(image)
#             # 读取image的bytes
#             img_byte_arr = io.BytesIO()
#             image.save(img_byte_arr, format="PNG")
#             image_url = image_util.image_util.upload_image(
#                 img_byte_arr.getvalue(), file_name, "tr-avatar-1323765209", "image/png"
#             )
#             if tavern_card is None:
#                 log.error(f"Failed to analysis image: {file_path}")
#                 continue
#             book_id = ""
#             role_card_config = tavern_card.to_role_card()
#             if role_card_config.character_book is not None:
#                 continue
#                 # role_card_config.character_book.book_id = str(uuid.uuid4())
#                 # book_id = role_card_config.character_book.book_id
#                 # await char_book.char_book_persistence.insert(
#                 #     role_card_config.character_book
#                 # )
#             # 只支持酒馆的图片
#             new_role = RoleEditDetail(
#                 card_name=role_card_config.name,
#                 name=role_card_config.name,
#                 role_name=role_card_config.name,
#                 introduction="",
#                 role_avatar=image_url,
#                 description=role_card_config.description,
#                 personality=role_card_config.personality,
#                 scenario=role_card_config.scenario,
#                 first_message=role_card_config.first_mes,
#                 example_dialog=role_card_config.mes_example,
#                 book_id=book_id,
#             )
#             role_config = new_role.to_role_config()
#             role_config.privacy = True
#             role_config.tags = "上新"
#             role_config.def_language = role_util.detect_language(new_role).value
#             await RoleConfigService.add_role_config(role_config)
#         except Exception as e:
#             log.error(f"Failed to import image: {file_path}, error: {e}")


# @char_book_router.get("/role_card/analysis_third_card/get_original_json")
# async def analysis_third_card_get_original_json(
#     card_id: str,
# ):
#     third_card = await third_card_service.analysis_third_card_original(card_id)
#     return JSONResponse(content=third_card)


# @char_book_router.get("/role_card/analysis_third_card/batch")
# async def analysis_third_card_batch(
#     platform: ThirdPlatform,
# ):
#     # await third_card_task.start_task()
#     await third_card_task.update_character_book_info()
#     return JSONResponse(content={"message": "Success"})


@char_book_router.get("/role_card/analysis_third_card/update_orders")
async def analysis_third_card_batch_orders():
    third_cards = await third_card_dao.list(ThirdPlatform.ROCHAT.value, 0, 10000)
    third_card = [x for x in third_cards if x.role_id > 0]
    third_card.sort(key=lambda x: x.popularity, reverse=True)
    order_ids = [x.role_id for x in third_card]
    await RoleConfigService.add_update_role_orders("RoChat", order_ids)

    third_cards = await third_card_dao.list(ThirdPlatform.RISUAI.value, 0, 10000)
    third_card = [x for x in third_cards if x.role_id > 0]
    third_card.sort(key=lambda x: x.popularity, reverse=True)
    order_ids = [x.role_id for x in third_card]
    await RoleConfigService.add_update_role_orders("RisuAi", order_ids)

    return JSONResponse(content={"message": "Success"})


# @char_book_router.get("/role_card/analysis_third_card/single")
# async def analysis_third_card(card_id: str):
#     third_card = await third_card_dao.get_by_card_id(card_id)
#     role_card_config = await third_card_service.analysis_third_card(card_id)
#     if role_card_config is None:
#         return JSONResponse(content={"message": "No card detected"}, status_code=400)

#     role_config = await third_card_service.init_or_update_role_config(
#         role_card_config, third_card
#     )
#     third_card.language = role_config.def_language
#     third_card.role_id = role_config.id
#     third_card.contains_book = role_card_config.character_book is not None
#     third_card.card_name = role_card_config.name
#     third_card.role_name = role_card_config.name

#     log.info(f"update third card: {third_card.model_dump()}")
#     await third_card_dao.update(third_card)
#     return JSONResponse(
#         content=jsonable_encoder(
#             {
#                 "role_config": role_config,
#                 "third_card": third_card.model_dump(),
#                 "tavern_card": role_card_config.model_dump(),
#             }
#         )
#     )


@char_book_router.get("/role_card/third_card/init")
async def init_third_card():
    local_dir = "/Users/<USER>/data/AIProject/rochat/"
    all_files = []
    for root, dirs, files in os.walk(local_dir):
        for file in files:
            all_files.append(os.path.join(root, file))
    all_files.sort(key=lambda x: os.path.getmtime(x))
    # 获取创建时间
    popularity = 1000000
    for file_path in all_files:
        try:

            image = Image.open(file_path)
            # 获取文件创建时间
            info: TavernCard = image_util.load_new_image_info(image)
            base_name = os.path.basename(file_path)
            base_name = base_name.replace(".png", "")
            card_id = md5(base_name.encode("utf-8")).hexdigest()
            role_card: RoleCardInfo = info.to_role_card()

            # 读取image的bytes
            # img_byte_arr = io.BytesIO()
            # image.save(img_byte_arr)
            # 读取file_path的bytes
            # file_bytes = None
            # with open(file_path, "rb") as f:
            #     file_bytes = f.read()

            # url = image_util.image_util.upload_image(
            #     file_bytes,
            #     ThirdPlatform.ROCHAT.value + "/" + card_id + ".png",
            #     "sgp-image-1323765209",
            #     "image/png",
            # )
            file_ct: float = os.path.getctime(file_path)
            created_at = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(file_ct))

            third_card = ThirdCardInfo(
                card_id=card_id,
                created_at=created_at,
                popularity=popularity,
                platform=ThirdPlatform.ROCHAT.value,
                image_url=card_id + ".png",
                tags=role_card.tags,
                contains_book=role_card.character_book is not None,
                card_name=role_card.name,
                role_name=role_card.name,
            )
            popularity -= 10
            log.info(f"update third card: {third_card.model_dump()}")
            # await third_card_dao.upsert(third_card)
            await third_card_dao.update_popularity(card_id, popularity)

        except Exception as e:
            print(e)



# @char_book_router.get("/role_card/third_card/download")
# async def download():

#     role_cards = await third_card_dao.list(ThirdPlatform.RISUAI.value, 0, 10000)
#     for role_card in role_cards:
#         try:
#             url = role_card.load_image_url()
#             response = requests.get(url)
#             if response.status_code == 200:
#                 with open(f"/Users/<USER>/Downloads/ai/risuai/{role_card.card_name}.png", "wb") as f:
#                     f.write(response.content)
#                     log.info(f"download success: {url}")
#             else:
#                 print(f"Failed to download: {url}")
#         except Exception as e:
#             print(f"Failed to download: {url}, error: {e}")