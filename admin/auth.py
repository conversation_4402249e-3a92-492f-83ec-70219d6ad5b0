from datetime import UTC, datetime, timedelta
import json
import logging
import os
import bcrypt
from dotenv import load_dotenv
from fastapi import <PERSON><PERSON>, HTTPException, Header, Request, Response
from jose import jwt

from common.common_constant import AdminUserRole, Env
from persistence.models.models import Admin<PERSON><PERSON><PERSON><PERSON>, AdminLog
from utils import env_util, exception_util, tg_util

load_dotenv()
log = logging.getLogger(__name__)

SECRET_KEY = "b'g5gFjPqzsi0V4sNUO9q5GyvYb1uiKvZ0'"
ALGORITHM = "HS256"


async def check_auth_new(user_mail: str, password: str) -> AdminAuthUser | None:
    auth_user = await AdminAuthUser.filter(email=user_mail).first()
    if auth_user is None:
        password_hash = bcrypt.hashpw(password.encode(), bcrypt.gensalt())
        name = user_mail.split("@")[0]
        user = await <PERSON>min<PERSON><PERSON><PERSON><PERSON>.create(
            email=user_mail, name=name, password_hash=password_hash.decode("utf-8")
        )
        tg_util.send_message({"text": f"新用户注册：{user_mail}"})
        return user
    auth_pass: str = auth_user.password_hash
    if bcrypt.checkpw(password.encode(), auth_pass.encode()):
        return auth_user
    return None


def set_cookie(email: str, request: Request, response: Response):
    expire = datetime.now(UTC) + timedelta(days=30)
    to_encode = {"sub": email, "exp": expire}
    token = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    response.set_cookie(
        "token",
        token,
        samesite="None",
        secure=True,
        expires=datetime.now(UTC) + timedelta(days=30),
    )
    request.state.email = email


def get_current_user(token: str) -> str:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Cookie"},
    )
    # For local development, use the ADMIN_AUTH_EMAIL environment variable
    # env = os.environ.get("ENV")
    # auth_user = os.environ.get("ADMIN_AUTH_EMAIL")
    # if env is not None and env == "local" and auth_user is not None:
    #     return auth_user
    try:
        # if os.getenv("ENV") == "local":
        #     return "<EMAIL>"
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub", "")
        if not username:
            raise credentials_exception
    except:
        raise credentials_exception
    return username


def is_admin_user(token: str = Cookie(None)) -> str:
    username = get_current_user(token)
    # if username != "<EMAIL>":
    #     raise HTTPException(status_code=401, detail="Forbidden")
    return username


API_TOKEN = "dify:aa7e1280cd514ad2834637f6f8896676"


def is_op_user(token: str = Cookie(None), api_token: str = Header(None)) -> str:
    if api_token and api_token == API_TOKEN:
        return "api"
    username = get_current_user(token)
    # if username != "<EMAIL>" and username != "<EMAIL>":
    #     raise HTTPException(status_code=401, detail="Forbidden")
    return username


USER_ROLE_AUTH_URLS = {
    AdminUserRole.ADMIN.value: ["/"],
    AdminUserRole.OP.value: [
        "/channel_id",
        "/generate_link",
        "/all_links",
        "/links_by_channel",
        "/order_by_id",
        "/order_by_out_order_id",
        "/roles",
        "/role_card",
        "/sub_tag",
        "/tag_orders",
        "/save_role_orders",
        "/save_tag_orders",
        "/queryBalance",
        "/charge",
        "/decrease_balance",
        "/user_info_reset",
        "/add_user_chat_black",
        "/revoke_user_chat_black",
        "/ban_user_tg_group",
        "/unban_user_tg_group",
        "/is_banned_in_group",
        "/user_info",
        "/diamond_earning_history",
        "/diamond_consumption_history",
        "/usdt",
        "/logout",
        "/tokenizers/count",
        "/manage/user",
        "/operation",
        "/remove_publish_role_black",
        "/remove_user_invitation_black",
        "/add_user_invitation_black",
        "/translate",
        "/activity_diamond",
        "/global/config",
        "/stat"
    ],
    AdminUserRole.API.value: ["/open/api", "/presets_v2"],
}

UN_AUTH_URL = ["/login", "/docs", "/openapi.json", "/tg/config", "/no_auth", "/tools","/open/api"]


def verify_url(path: str, role: AdminUserRole):
    url_paths = USER_ROLE_AUTH_URLS.get(role.value)
    if not url_paths:
        return False
    for url_path in url_paths:
        if path.startswith(url_path):
            return True
    return False


async def add_operation_log(email: str, path: str, method: str, data: dict):
    await AdminLog.create(email=email, path=path, method=method, data=json.dumps(data))


async def request_data(request: Request):
    params = request.path_params
    query_param = request.query_params
    data = {"path_params": dict(params), "query_params": dict(query_param)}
    if request.url.path in ["/login"]:
        return data
    # 判断是form-data还是json
    if request.headers.get("Content-Type") == "application/x-www-form-urlencoded":
        body = await request.form()
        form = dict(body)
        # 判断是否有文件，有文件则不记录
        if form.get("file"):
            form["file"] = "file"
        data["form"] = dict(form)
    if request.headers.get("Content-Type") == "application/json":
        # 判断request json为
        body = await request.body()
        data["json"] = body.decode("utf-8") if body else {}
    return data


async def add_operation_log_by_req(
    request: Request, req_data: dict, response_code: int
):
    email = request.state.email
    if not email:
        email = "<EMAIL>"
    if request.method != "POST":
        return
    res_data = {"code": response_code}
    ig_paths = ["/tokenizers/count"]
    if request.url.path in ig_paths:
        return
    await AdminLog.create(
        email=email,
        path=request.url.path,
        method=request.method,
        req_data=json.dumps(req_data) if req_data else {},
        res_data=json.dumps(res_data),
    )


API_USER_EMAIL = "<EMAIL>"


async def verify_token(request: Request):
    token = request.cookies.get("token")
    api_token = request.headers.get("api_token")
    authorization = request.headers.get("Authorization")
    request.state.email = ""
    path = request.url.path
    if env_util.get_current_env() == Env.LOCAL:
        request.state.email = "<EMAIL>"
        return True
    if any(path.startswith(url) for url in UN_AUTH_URL):
        return True
    if api_token and api_token == API_TOKEN and verify_url(path, AdminUserRole.API):
        request.state.email = API_USER_EMAIL
        return True
    if authorization == "Bearer " + os.environ["ADMIN_AUTH_HEADER"]:
        request.state.email = API_USER_EMAIL
        return True
    if not token:
        return False
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    email: str = payload.get("sub", "")
    request.state.email = email
    if not email:
        return False
    return True


async def verify_auth(request: Request):
    path = request.url.path
    email = request.state.email
    if any(path.startswith(url) for url in UN_AUTH_URL):
        return True
    if not email:
        return False
    user = await AdminAuthUser.filter(email=email).first()
    if not user or not user.role:
        return False
    return verify_url(path, AdminUserRole(user.role))


def get_login_email(request: Request):
    return request.state.email


async def current_user(request: Request):
    email = request.state.email
    if not email:
        raise exception_util.http_auth("请登录")
    user = await AdminAuthUser.filter(email=email).first()
    if not user:
        raise exception_util.http_auth("用户不存在")
    return user
