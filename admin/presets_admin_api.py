import os
import re
from fastapi import Depends, HTTPException, Header
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.routing import APIRouter
from admin.auth import is_admin_user
from common.entity import RegexRuleRequest, RegexRuleResponse
from common.models.chat_request import PresetConfig
from persistence.models.models import RegexAffect, RegexOption
from persistence.presets import presetsPersistence
from services import config_service, regex_service
from pydantic import BaseModel
from utils import message_utils, response_util
import utils.regex_converter as rc


presets_router = APIRouter(dependencies=[Depends(is_admin_user)])


@presets_router.get("/presets/db_original")
async def get_presets_db_original(model: int, nsfw: int, scenario: int):
    presets = await presetsPersistence.get_preset_v2(model, nsfw, scenario)
    return JSONResponse(content=presets)


@presets_router.get("/presets_v2")
async def get_current_presets_v2():
    presets = await presetsPersistence.get_all_preset_for_admin()
    return JSONResponse(content=presets)


@presets_router.get("/presets/llm_models")
async def get_llm_models():
    llm_models = await config_service.list_llm_model_config()
    llm_models = [x.llm_model for x in llm_models]
    llm_models.sort(key=lambda x: x.lower())
    return response_util.ok(data=llm_models)


@presets_router.get("/presets/get")
async def get_presets(model: str, nsfw: int, scenario: int):
    llm_model_config = await config_service.get_llm_model_config_by_model(model)
    if not llm_model_config:
        return response_util.param_error("model not found")

    preset = await presetsPersistence.get_preset_v2(
        llm_model_config.model_int, nsfw, scenario
    )
    return response_util.ok(data=preset)


@presets_router.post("/saveReg")
async def saveReg(rule: RegexRuleRequest):
    rule_model = await regex_service.add_regex_rule(rule)
    return RegexRuleResponse.from_model(rule_model)


@presets_router.post("/updateReg")
async def updateReg(request: RegexRuleRequest) -> RegexRuleResponse:
    rule = await regex_service.get_regex_rule_by_id(request.rule_id)
    if rule is None:
        return JSONResponse(content={"msg": "rule not found"}, status_code=404)
    request.copy_to_model(rule)
    rule = await regex_service.update_regex_rule(rule)
    return JSONResponse(content=jsonable_encoder(RegexRuleResponse.from_model(rule)))


class TestRegexRuleRequest(BaseModel):
    regex: str = ""
    testRegTxt: str = ""
    replacement: str = ""


@presets_router.post("/testReg")
async def test_regex_rule(rule: TestRegexRuleRequest):
    if not rule.regex or not rule.testRegTxt:
        return JSONResponse(
            content=jsonable_encoder({"success": True, "testRegTxt": "Empty"})
        )
    content = message_utils.run_js_regex(rule.regex, rule.replacement, rule.testRegTxt)
    return JSONResponse(
        content=jsonable_encoder({"success": True, "testRegTxt": content})
    )


@presets_router.get("/regex_all")
async def get_all_regex_rules():
    affects = list(RegexAffect.__members__.values())
    options = list(RegexOption.__members__.values())
    all_regex = await regex_service.get_all_regex_rules()
    rules = [RegexRuleResponse.from_model(rule) for rule in all_regex]
    return JSONResponse(
        content=jsonable_encoder(
            {"all_regex": rules, "affects": affects, "options": options}
        )
    )


@presets_router.post("/deleteReg")
async def add_regex_rule(rule_id: str) -> RegexRuleResponse:
    rule = await regex_service.get_regex_rule_by_id(rule_id)
    if rule is None:
        return JSONResponse(content={"msg": "rule not found"}, status_code=404)
    rule = await regex_service.delete_regex_rule(rule)
    return JSONResponse(content={"success": True})
