import asyncio
from io import String<PERSON>
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from ai import inner_bot
from common.common_constant import Language
from common.translate_model import (
    TranslateSubTag,
    TranslateTaskStatus,
    TranslateTaskType,
)
from persistence import redis_client
from persistence.models.models import TranslateResource
from services import translate_service
from tasks import translate_task
from utils import response_util

translate_router = APIRouter()


@translate_router.get("/translate/run/task")
async def download_sub_tags(task_type: TranslateTaskType, task_key: str):
    task = await translate_service.get_task(task_type.value, task_key)
    if not task:
        return response_util.def_error("task not found")
    method = translate_task.task_method_map.get(task_type.value)
    if not method:
        return response_util.def_error("method not found")
    target = await method(task)
    return response_util.ok({"target": target})


@translate_router.get("/translate/resource/configs")
async def get_resource_config():
    # 获取翻译资源配置
    config = await TranslateResource.filter().only("category").distinct()
    categories = [item.category for item in config]
    status = [
        TranslateTaskStatus.FINISHED.value,
        TranslateTaskStatus.PENDING.value,
        TranslateTaskStatus.PROCESSING.value,
        TranslateTaskStatus.ERROR.value,
    ]
    return response_util.ok({"category_list": categories, "status_list": status})


@translate_router.get("/translate/resource/list")
async def get_resource_list(
    category: str = "",
    keywords: str = "",
    status: str = TranslateTaskStatus.PENDING.value,
    review: bool = False,
):
    filter = {}
    category = category if category != "全部" else ""
    if category:
        filter["category"] = category
    if keywords:
        filter["text__contains"] = keywords
    filter["status"] = status
    filter["review"] = review
    resource_list = await TranslateResource.filter(**filter).order_by("updated_at")
    return response_util.ok({"resource_list": resource_list})


@translate_router.get("/translate/resource/retry")
async def retry_translate(
    id: int,
):

    resource = await TranslateResource.get(id=id)
    if not resource:
        return response_util.def_error("resource not found")
    resource.zh_tw = await inner_bot.translate_web(resource.text, Language.ZH_TW.value)
    resource.en_us = await inner_bot.translate_web(resource.text, Language.EN.value)
    if not resource.zh_cn:
        resource.zh_cn = resource.text
    return response_util.ok({"resource": resource})


class ReviewRequest(BaseModel):
    id: int
    zh_tw: str = ""
    en_us: str = ""
    zh_cn: str = ""


@translate_router.post("/translate/resource/review")
async def review_translate(
    review_request: ReviewRequest,
):

    resource = await TranslateResource.get(id=review_request.id)
    if not resource:
        return response_util.def_error("resource not found")
    resource.zh_tw = review_request.zh_tw
    resource.en_us = review_request.en_us
    resource.zh_cn = review_request.zh_cn
    resource.review = True
    resource.status = TranslateTaskStatus.FINISHED.value
    await resource.save()
    return response_util.ok({"resource": resource})


@translate_router.post("/translate/resource/translate_all")
async def translate_all_resources():
    if not redis_client.acquire_lock("translate_all_resources", "1", 60):
        return response_util.def_error("翻译任务正在进行中，请稍后再试")

    async def run_all_task():
        resources = await TranslateResource.filter(status="pending").all()
        for resource in resources:
            if not resource.text:
                continue
            if not resource.zh_cn:
                resource.zh_cn = resource.text
            resource.zh_tw = await inner_bot.translate(
                resource.text, Language.ZH_TW.value
            )
            resource.en_us = await inner_bot.translate(resource.text, Language.EN.value)
            resource.status = "finished"
            resource.review = False
            await resource.save()

        redis_client.release_lock("translate_all_resources", "1")

    asyncio.create_task(run_all_task())
    return response_util.ok({"message": "翻译任务已提交，请稍后查看结果"})
