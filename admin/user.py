from datetime import datetime, timedelta, timezone
import json
import logging
import os
import random
from typing import Annotated, Dict, Optional
import uuid
import bcrypt
from fastapi import (
    Depends,
    FastAPI,
    File,
    Header,
    Query,
    Request,
    Form,
    Response,
    UploadFile,
)
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.routing import APIRouter
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from aiogram.utils.deep_linking import encode_payload

from common.bot_common import MessageTemplate
from common.common_constant import AdminUserRole, GroupCategory, Language, ProductType
from persistence import chat_history_dao, redis_client
from persistence.models.models import PayOrder, Product, RegexOption, User, UserStatus
from services import (
    bot_message_service,
    bot_services,
    product_service,
    recharge_service,
    role_statistic_service,
    tg_config_service,
    user_active_service,
    user_alt_persona_service,
    user_alt_profile_service,
)
from services import role_config_service
from services.account_service import AccountService
from services.chat import chat_message_service
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from services.user import user_benefit_chat_queue_service, user_benefit_service
from services.user_service import UserService
from utils import date_util, env_const, json_util, response_util, token_util
from utils.translate_util import _tl
from .auth import is_admin_user, is_op_user, set_cookie
from admin import auth

user_router = APIRouter(dependencies=[Depends(is_op_user)])


class ChargeRequest(BaseModel):
    id: int
    amount: int
    expire_days: int = 31


user_service = UserService()


@user_router.get("/queryBalance")
async def query_tg_user_balance(id: int):
    balance = await AccountService.get_total_balance(id)
    return JSONResponse(content=jsonable_encoder({"balance": balance}))


# 手动给用户增加钻石
@user_router.post("/charge")
async def manual_charge(request: Request, req: ChargeRequest):
    user = await auth.current_user(request)
    if user.role == AdminUserRole.ADMIN.value and req.amount > 500000:
        return response_util.param_error("amount should be less than 100000")
    if user.role != AdminUserRole.ADMIN.value and req.amount > 20000:
        return response_util.param_error("amount should be less than 20000")
    if user.role != AdminUserRole.ADMIN.value:
        # redis记录当天充值的总数量，超过20000则不允许充值
        today = datetime.now().strftime("%Y-%m-%d")
        key = f"op:charge_amount:{req.id}:{today}"
        total = redis_client.redis_client.get(key)
        if total is None:
            total = 0
        else:
            total = int(total.decode("utf-8"))
        if total + req.amount > 20000:
            return response_util.error(
                400,
                f"今日该user_id:{req.id} 充值总额已有{total}，不允许再充值{req.amount}, 超过单日充值总量上限20000",
            )
        redis_client.redis_client.incr(key, req.amount)
        redis_client.redis_client.expire(key, 3600 * 24)

    await recharge_service.airdrop_as_expirable(req.id, req.amount, req.expire_days)
    return response_util.ok()


# 手动给用户减少钻石
@user_router.post("/decrease_balance")
async def manual_decrease_balance(req: ChargeRequest):
    if req.amount <= 0:
        return response_util.param_error("amount should be positive")
    # product = Product(price=req.amount, **product_service.MANUAL_REDUCTION_DICT)
    product = await product_service.get_online_by_type_first(ProductType.MANUAL.value)
    if product is None:
        return response_util.def_error("product not found")
    product.price = req.amount

    await AccountService.create_pay_order(req.id, product)
    return response_util.ok()


@user_router.get("/user_info_reset")
async def user_info_reset(user_id: int):
    user = await user_service.get_user_by_id(user_id)
    if user == None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    flag = random.randint(0, 99)
    user.email = user.email + f"_{flag}_deleted"
    user.status = UserStatus.DELETED.value
    await user_service.update_user(user)
    await user_service.del_tg_info_by_user_id(user_id)
    return JSONResponse(content=jsonable_encoder({"message": "ok"}))


# 加入黑名单
@user_router.get("/add_user_chat_black")
async def add_user_chat_black(user_id: int):
    user = await user_service.get_user_by_id(user_id)
    if user == None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    flag = random.randint(0, 99)
    user.status = UserStatus.CHAT_BLACK.value
    await user_service.update_user(user)

    return JSONResponse(content=jsonable_encoder({"message": "ok"}))


# 移除黑名单
@user_router.post("/revoke_user_chat_black")
async def revoke_user_chat_black(user_id: int):
    user = await user_service.get_user_by_id(user_id)
    if user == None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    user.status = UserStatus.NORMAL.value
    await user_service.update_user(user)

    return JSONResponse(content=jsonable_encoder({"message": "ok"}))


# 将用户从tg群中封禁
@user_router.post("/ban_user_tg_group")
async def ban_user_tg_group(user_id: int):
    tg_info = await user_service.get_tg_info_by_user_id(user_id)
    if tg_info is None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    resp = {}
    groups = await tg_config_service.list_group(category=GroupCategory.CHAT)
    for group in groups:
        result = await bot_services.ban_in_chat(tg_info.tg_id, group.chat_id)
        logging.info(
            f"ban user {user_id}, tg id:{tg_info.tg_id} in group {group.chat_id}: result: {result}"
        )
        resp[group.chat_id] = result
    return response_util.ok(resp)


# 将用户从tg群中解封
@user_router.post("/unban_user_tg_group")
async def unban_user_tg_group(user_id: int):
    tg_info = await user_service.get_tg_info_by_user_id(user_id)
    if tg_info is None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    resp = {}
    groups = await tg_config_service.list_group(category=GroupCategory.CHAT)
    group_ids = [group.chat_id for group in groups]
    for group_id in group_ids:
        result = await bot_services.unban_in_chat(tg_info.tg_id, group_id)
        logging.info(
            f"unban user {user_id},tg id:{tg_info.tg_id} in group {group_id}: result: {result}"
        )
        resp[group_id] = result
    return response_util.ok(resp)


# 查询用户是否在tg群中被封禁
@user_router.get("/is_banned_in_group")
async def check_banned_in_group(user_id: int):
    tg_info = await user_service.get_tg_info_by_user_id(user_id)
    if tg_info is None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    resp = {}
    groups = await tg_config_service.list_group(category=GroupCategory.CHAT)
    group_ids = [group.chat_id for group in groups]
    for group_id in group_ids:
        result = await bot_services.check_if_banned_in_chat(tg_info.tg_id, group_id)
        resp[group_id] = result
    return response_util.ok(resp)


class AuthorProfileRequest(BaseModel):
    user_id: int
    author: bool
    role_count: int = 0


@user_router.post("/manage/user/update_author_profile")
async def update_author_profile(body: AuthorProfileRequest):
    user = await user_service.get_user_by_id(body.user_id)
    user.status = (
        UserStatus.NORMAL.value if not body.author else UserStatus.AUTHOR.value
    )
    user.role_count = 0 if not body.author else body.role_count
    await user.save()
    return response_util.ok()


# @user_router.get("/user/chat/history")
# async def chat_history(
#     user_id: int,
#     sum_token: int,
#     role_id: int,
#     conversation_id: str,
#     api_token:str = Header()
# ):
#     if api_token != "dify:aa7e1280cd514ad2834637f6f8896676":
#         return response_util.param_error("token error")

#     user = await user_service.get_user_by_id(user_id)
#     if user is None:
#         return response_util.param_error("user not found")
#     query = BuildHistoryQuery(
#         user_id=user_id,
#         nickname=user.nickname,
#         request_user_name=user.nickname,
#         mode_type="single",
#         mode_target_id=role_id,
#         conversation_id=conversation_id,
#         language = Language.ZH.value,
#         add_name=True,
#         regex_option=RegexOption.FORMAT_PROMPT.value

#     )
#     history_items = await chat_message_service.build_history(query)
#     history_ret = []
#     history_items = list(reversed(history_items))
#     for item in history_items:
#         sum_token -= token_util.num_tokens_from_string(item.content)
#         if sum_token < 0:
#             break
#         history_ret.append(item)
#     return response_util.ok({"history": list(reversed(history_ret))})


@user_router.get("/user/login_by_session")
async def login_by_session(user_id: int):
    session_code = str(uuid.uuid4())
    tg_info = await user_service.get_tg_info_by_user_id(user_id)
    if tg_info is None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    uid_payload = encode_payload(str(tg_info.tg_id))
    redis_client.redis_client.set(
        f"tg_login:{session_code}", str(tg_info.tg_id), ex=60 * 5
    )
    url = f"{env_const.WEB_VERSION_URL}?uid={uid_payload}&session_code={session_code}"
    return RedirectResponse(url=url)


# 查询用户信息
@user_router.get("/user_info")
async def get_user_info(other_user_id: int = 0, tg_user_id: int = 0):
    if other_user_id == 0 and tg_user_id == 0:
        return response_util.param_error("user_id or tg_id should be provided")
    if other_user_id > 0:
        # 用户信息
        user = await user_service.safe_get_user(other_user_id)
        if user is None:
            return response_util.error(400, "user not found")
    elif tg_user_id > 0:
        user = await user_service.get_user_by_tg_id(tg_user_id)
        if user is None:
            return response_util.error(400, "user not found")
        other_user_id = user.id
    tg_info = await user_service.get_tg_info_by_user_id(other_user_id)
    # 当前剩余钻石数
    balance = await AccountService.get_total_balance(other_user_id)
    payed_balance = await AccountService.get_payed_total_balance(other_user_id)
    reward_balance = await AccountService.get_reward_total_balance(other_user_id)
    # 累计充值金额（除以100000才是元为单位）
    total_recharge = await recharge_service.get_user_rmb_payed_fee(other_user_id)
    # 累计消耗钻石
    total_consumed = await AccountService.get_total_consumed_amount(other_user_id)
    reward_total_consumed = await AccountService.get_total_consumed_amount(
        other_user_id
    )
    payed_total_consumed = total_consumed - reward_total_consumed
    # tg群封禁状态
    tg_group_banned_status = {}
    if tg_info is not None:
        groups = await tg_config_service.list_group(category=GroupCategory.CHAT)
        group_ids = [group.chat_id for group in groups]
        for group_id in group_ids:
            result = await bot_services.check_if_banned_in_chat(tg_info.tg_id, group_id)
            tg_group_banned_status[group_id] = result
    detail_list = []
    active_bot_detail = await user_active_service.list_active_bots_detail(user)
    active_bot_detail.sort(key=lambda x: x.user_last_active_at, reverse=True)
    bot_desc = [
        f"{x.bot_first_name}(@{x.bot_username}):{date_util.datetime2utc8str(x.user_last_active_at)}"
        for x in active_bot_detail
        if x
    ]
    detail_list.append(
        {
            "title": f"活跃Bot列表",
            "content": "\n".join(bot_desc),
        }
    )
    user_benefits = await user_benefit_service.list_valid(user)
    if user_benefits:
        user_benefits = [
            f"{x.benefit_title}:{x.remain_times}/{x.reward_times}"
            for x in user_benefits
        ]
        detail_list.append(
            {
                "title": "用户权益",
                "content": "\n".join(user_benefits),
            }
        )
    # 用户的tg群组和频道

    chat_maps = await tg_config_service.map_all_chat_names()
    chat_join_records = await user_active_service.list_join_chat_records(user.id)
    if chat_join_records:
        join_log = []
        for record in chat_join_records:
            chat_name = chat_maps.get(record.chat_id, "未知")
            join_log.append(
                f"{chat_name}: {date_util.datetime2utc8str(record.created_at)}"
            )
        detail_list.append(
            {
                "title": "加入群组或者频道时间",
                "content": "\n".join(join_log),
            }
        )
    user_product = await product_service.get_user_chat_product(user)
    if user_product:
        detail_list.append(
            {
                "title": "选择的模型产品",
                "content": f"{user_product.display_name}({user_product.mid})-{user_product.model}",
            }
        )
    if not user_product:
        detail_list.append(
            {
                "title": "选择的模型产品",
                "content": f"选择模型异常或已下架{user.chat_product_mid}",
            }
        )
    skip_queue_benefit = await user_benefit_chat_queue_service.get_latest_skip_queue_benefit(user.id)
    if skip_queue_benefit:
        detail_list.append(
            {
                "title": "9.9不排队",
                "content": f"有效期: {date_util.datetime2utc8str(skip_queue_benefit.valid_end_at, format="%Y-%m-%d %H:%M:%S")}",
            }
        )
    user_roles = await role_config_service.list_effective_role_by_uid(
        user.id, user.nickname
    )
    role_hot_map = await role_statistic_service.user_role_statistic(
        [x.id for x in user_roles if x.id > 0]
    )
    if user_roles:
        public_roles = [
            f"{x.card_name}({x.public_role_id})"
            for x in user_roles
            if not x.private_card
        ]
        private_roles = [
            f"{x.role_name}({x.id}-h[{role_hot_map.get(x.id, 0)})]"
            for x in user_roles
            if x.private_card
        ]
        if public_roles:
            detail_list.append(
                {
                    "title": "公开角色",
                    "content": "\n".join(public_roles),
                }
            )
        if private_roles:
            detail_list.append(
                {
                    "title": "私密角色",
                    "content": "\n".join(private_roles),
                }
            )
    # 增加User信息
    user_info_list = [
        f"Email: {user.email}",
        f"Status: {user.status}",
        f"注册来源: {user.register_source}",
        f"角色数量: {user.role_count}",
        f"是否开启状态栏: {user.status_block_switch}",
        f"聊天模式: {user.chat_channel}",
    ]
    detail_list.append(
        {
            "title": "User信息",
            "content": "\n".join(user_info_list),
        }
    )
    nicknames = await user_alt_profile_service.get_user_history_nicknames(user.id)
    if nicknames:
        detail_list.append(
            {
                "title": "历史昵称",
                "content": "\n".join(nicknames),
            }
        )
    publish_role_privilege = True
    publish_privilege = json_util.convert_to_dict(user.publish_role_privilege)
    if publish_privilege and publish_privilege["reject_count"] >= 6:
        publish_role_privilege = False

    return response_util.ok(
        {
            "user_id": other_user_id,
            "nickname": user.nickname,
            "tg_id": tg_info.tg_id if tg_info is not None else 0,
            "created_at": user.created_at.timestamp(),
            "balance": balance,
            "payed_balance": payed_balance,
            "reward_balance": reward_balance,
            "total_recharge": total_recharge,
            "total_consumed": total_consumed,
            "status": user.status,
            "tg_group_banned_status": tg_group_banned_status,
            "author": user.status == UserStatus.AUTHOR.value,
            "role_count": user.role_count,
            "payed_total_consumed": payed_total_consumed,
            "reward_total_consumed": reward_total_consumed,
            "detail_list": detail_list,
            "invitation_privilege": user.invitation_privilege,
            "publish_role_privilege": publish_role_privilege,
        }
    )


# 查询用户的钻石获取记录
@user_router.get("/diamond_earning_history")
async def get_diamond_earning_history(
    user_id: int,
    limit: int,
    offset: int,
    only_recharge: bool,
    start_date: str = Query(..., description="Date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="Date in YYYY-MM-DD format"),
):
    # start_date_time = datetime.strptime(start_date, '%Y-%m-%d')
    # end_date_time = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
    # if start_date_time > end_date_time:
    #     return response_util.param_error("start_date should be less than end_date")
    recharge_list, total = await recharge_service.get_recharge_orders_paging(
        user_id, offset, limit, only_recharge
    )
    recharge_data = []
    recharge_products = await recharge_service.get_all_recharge_products()
    recharge_product_map = {
        str(product.recharge_product_id): product for product in recharge_products
    }
    for recharge in recharge_list:
        rp = recharge_product_map.get(recharge.recharge_product_id)
        if rp is None:
            cny_fee = (
                f"{recharge.pay_fee/100000}元"
                if recharge.pay_currency == "CNY"
                else f"{recharge.pay_fee * 7.5 / 100000}元"
            )
        else:
            cny_fee = f"{rp.cny_price/100000}元"
        recharge_data.append(
            {
                "recharge_order_id": recharge.recharge_order_id,
                "out_order_id": recharge.out_order_id,
                "created_at": recharge.created_at.timestamp(),
                "updated_at": recharge.updated_at.timestamp(),
                "amount": recharge.amount,
                "pay_fee": recharge.pay_fee,
                "pay_currency": recharge.pay_currency,
                "recharge_channel": recharge.recharge_channel.description,
                "cny_fee": cny_fee,
                "title": rp.title if rp else "未知",
            }
        )
    return response_util.ok({"list": recharge_data, "total": total})


class ConsumptionInfo(BaseModel):
    created_at: float = 0
    type: str = "未知"
    role_id: int = 0
    role_name: str = ""
    chat_desc: str = ""
    amount: int = 0
    extra_info: Dict = {}


# 查询用户的消费记录
@user_router.get("/diamond_consumption_history")
async def get_diamond_consumption_history(
    user_id: int,
    start_date: str = Query(..., description="Date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="Date in YYYY-MM-DD format"),
):
    start_date_time = datetime.strptime(start_date, "%Y-%m-%d")
    end_date_time = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
    if start_date_time > end_date_time:
        return response_util.param_error("start_date should be less than end_date")
    # 时间跨度不能超过7天
    if (end_date_time - start_date_time).days > 7:
        return response_util.error(400, "时间跨度不能超过7天")
    pay_orders, _ = await AccountService.get_pay_orders_paging(
        user_id, 0, 0, start_date_time, end_date_time
    )
    role_ids = list(
        set([pay_order.role_id for pay_order in pay_orders if pay_order.role_id != 0])
    )
    expired_diamonds = await AccountService.get_expired_diamonds_history(
        user_id, start_date_time, end_date_time
    )
    role_name_map = await role_loader_service.map_role_name_by_ids(role_ids)
    product_map = await product_service.map_by_product_id()
    # 使用pay_orders的created_at字段 和 expired_diamonds的expires_at字段做排序
    # 两个列表合并后按时间倒序排序
    pay_orders_index = 0
    expired_diamonds_index = 0
    history_list = []

    def _handle_pay_order(pay_order: PayOrder, product_map: dict[str, Product]):
        history = ConsumptionInfo()
        history.created_at = pay_order.created_at.timestamp()
        history.amount = pay_order.total_fee
        history.extra_info = json_util.convert_to_dict(pay_order.extra_info)
        product = product_map.get(str(pay_order.product_id))
        if product:
            history.type = product.name
            history.chat_desc = product.display_name
            history.role_id = pay_order.role_id
            history.role_name = role_name_map.get(pay_order.role_id, "")
        return history

    while pay_orders_index < len(pay_orders) and expired_diamonds_index < len(
        expired_diamonds
    ):
        if (
            pay_orders[pay_orders_index].created_at
            > expired_diamonds[expired_diamonds_index].expires_at
        ):
            pay_order = pay_orders[pay_orders_index]
            history = _handle_pay_order(pay_order, product_map)
            history_list.append(history)
            pay_orders_index += 1
        else:
            history = _handle_expired_history(expired_diamonds[expired_diamonds_index])
            history_list.append(history)
            expired_diamonds_index += 1

    while pay_orders_index < len(pay_orders):
        pay_order = pay_orders[pay_orders_index]
        history = _handle_pay_order(pay_order, product_map)
        history_list.append(history)
        pay_orders_index += 1

    while expired_diamonds_index < len(expired_diamonds):
        history = _handle_expired_history(expired_diamonds[expired_diamonds_index])
        history_list.append(history)
        expired_diamonds_index += 1

    return response_util.ok({"list": history_list})


def _handle_expired_history(expired_diamond):
    history = ConsumptionInfo()
    history.created_at = expired_diamond.expires_at.timestamp()
    history.amount = expired_diamond.balance
    history.type = "赠送金币过期"
    return history


@user_router.get("/add_user_invitation_black")
async def add_user_invitation_black(user_id: int):
    user = await user_service.get_user_by_id(user_id)
    if user == None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    user.invitation_privilege = False
    await user_service.update_user(user)
    await bot_message_service.send_user_template_message(
        user,
        MessageTemplate(tips="您的邀请行为存在异常，如有疑问请联系管理员 @ai_x01_bot"),
    )

    return JSONResponse(content=jsonable_encoder({"message": "ok"}))


@user_router.get("/remove_user_invitation_black")
async def remove_user_invitation_black(user_id: int):
    user = await user_service.get_user_by_id(user_id)
    if user == None:
        return JSONResponse(content=jsonable_encoder({"message": "user not found"}))
    user.invitation_privilege = True
    await user_service.update_user(user)
    await bot_message_service.send_user_template_message(
        user,
        MessageTemplate(
            tips="您的邀请功能已恢复，您可以继续邀请用户获取奖励，祝您玩得愉快。"
        ),
    )

    return JSONResponse(content=jsonable_encoder({"message": "ok"}))


# 解封用户公开发布功能（可以公开发布提交审核）
@user_router.post("/remove_publish_role_black")
async def remove_publish_role_black(user_id: int):
    user = await user_service.get_user_by_id(user_id)
    if user == None:
        return response_util.error(400, "user not found")
    original_dict = json_util.convert_to_dict(user.publish_role_privilege)

    original_dict["reject_count"] = 0
    original_dict["ts"] = int(datetime.now(timezone.utc).timestamp())
    user.publish_role_privilege = original_dict
    await user_service.update_user(user)

    return response_util.ok()


@user_router.post("/admin/user/delete_account")
async def delete_user_account(user_id: int):
    user = await User.get_or_none(id=user_id)
    if not user:
        return response_util.def_error("用户不存在")
    logging.info(f"Deleting user account: {user_id}")
    await user.delete()
    tg_info = await user_service.get_tg_user_by_id(user_id)
    if tg_info:
        logging.info(
            f"Deleting Telegram info for user: {user_id}, tg_id: {tg_info.tg_id}"
        )
        await tg_info.delete()

    return response_util.ok(
        {"user_id": user_id, "tg_id": tg_info.tg_id if tg_info else None}
    )
