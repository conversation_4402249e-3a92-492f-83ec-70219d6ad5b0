# 渠道控制批量操作功能

## 概述

本功能为渠道控制管理系统提供了高性能的批量操作支持，允许在单个API调用中执行多种类型的操作，包括渠道控制配置的创建、更新、删除、启用、禁用以及白名单条目的管理。

## 主要特性

- **混合操作支持**: 在单个请求中同时处理渠道控制和白名单操作
- **高性能批量处理**: 使用数据库批量操作优化性能
- **事务管理**: 确保数据一致性，支持失败回滚
- **详细错误报告**: 提供每个操作的详细执行结果
- **灵活的失败处理**: 支持快速失败和继续执行两种模式

## API 端点

```
POST /admin/channel_control/batch
```

## 支持的操作类型

### 渠道控制操作

| 操作类型 | 描述 | 必需字段 |
|---------|------|----------|
| `CREATE` | 创建新的渠道控制配置 | `channel`, `pay_type` |
| `UPDATE` | 更新现有配置 | `control_id`, `channel`, `pay_type` |
| `DELETE` | 删除配置 | `control_id` |
| `ENABLE` | 启用配置 | `channel`, `pay_type` 或 `control_id` |
| `DISABLE` | 禁用配置 | `channel`, `pay_type` 或 `control_id` |
| `UPSERT` | 存在则更新，不存在则创建 | `channel`, `pay_type` |

### 白名单操作

| 操作类型 | 描述 | 必需字段 |
|---------|------|----------|
| `CREATE` | 创建白名单条目 | `tg_id`, `channel` |
| `DELETE` | 删除白名单条目 | `tg_id`, `channel` |

## 请求格式

```json
{
  "channel_controls": [
    {
      "operation_id": "unique_id_1",
      "operation_type": "CREATE",
      "channel": "TMPAY",
      "pay_type": "alipay",
      "min_amount": 100,
      "max_amount": 50000,
      "enabled": true,
      "ratio": 100,
      "remark": "批量创建测试"
    }
  ],
  "whitelists": [
    {
      "operation_id": "unique_id_2",
      "operation_type": "CREATE",
      "tg_id": 123456789,
      "channel": "TMPAY",
      "remark": "批量白名单测试"
    }
  ],
  "fail_fast": true
}
```

## 响应格式

```json
{
  "total_operations": 2,
  "successful_operations": 2,
  "failed_operations": 0,
  "execution_time_ms": 150,
  "results": [
    {
      "operation_id": "unique_id_1",
      "success": true,
      "data": {
        "id": 1,
        "channel": "TMPAY",
        "pay_type": "alipay",
        "created_at": "2024-01-01T00:00:00Z"
      }
    },
    {
      "operation_id": "unique_id_2",
      "success": true,
      "data": {
        "id": 1,
        "tg_id": 123456789,
        "channel": "TMPAY",
        "created_at": "2024-01-01T00:00:00Z"
      }
    }
  ]
}
```

## 字段说明

### 渠道控制字段

- `operation_id`: 操作唯一标识符（必填）
- `operation_type`: 操作类型（必填）
- `control_id`: 配置ID（UPDATE/DELETE操作必填）
- `channel`: 充值渠道枚举值（必填）
- `pay_type`: 支付类型（必填）
- `min_amount`: 最小金额（可选，默认0）
- `max_amount`: 最大金额（可选，默认999999999）
- `enabled`: 是否启用（可选，默认true）
- `ratio`: 比例（可选，默认100）
- `remark`: 备注信息（可选）

### 白名单字段

- `operation_id`: 操作唯一标识符（必填）
- `operation_type`: 操作类型（CREATE/DELETE）
- `tg_id`: Telegram用户ID（必填）
- `channel`: 充值渠道枚举值（必填）
- `remark`: 备注信息（可选）

### 请求参数

- `channel_controls`: 渠道控制操作列表（可选）
- `whitelists`: 白名单操作列表（可选）
- `fail_fast`: 遇到错误时是否立即停止（可选，默认true）

## 使用示例

### 1. 批量创建渠道控制配置

```python
import requests

data = {
    "channel_controls": [
        {
            "operation_id": "create_tmpay",
            "operation_type": "CREATE",
            "channel": "TMPAY",
            "pay_type": "alipay",
            "min_amount": 100,
            "max_amount": 50000,
            "enabled": True,
            "ratio": 100,
            "remark": "天猫支付宝渠道"
        },
        {
            "operation_id": "create_sjzf",
            "operation_type": "CREATE", 
            "channel": "SJZF",
            "pay_type": "wechat",
            "min_amount": 200,
            "max_amount": 100000,
            "enabled": True,
            "ratio": 80,
            "remark": "手机支付微信渠道"
        }
    ],
    "fail_fast": True
}

response = requests.post("/admin/channel_control/batch", json=data)
print(response.json())
```

### 2. 批量更新和启用/禁用操作

```python
data = {
    "channel_controls": [
        {
            "operation_id": "update_1",
            "operation_type": "UPDATE",
            "control_id": 1,
            "channel": "TMPAY",
            "pay_type": "alipay",
            "min_amount": 150,
            "max_amount": 60000,
            "ratio": 110,
            "remark": "更新后的配置"
        },
        {
            "operation_id": "enable_sjzf",
            "operation_type": "ENABLE",
            "channel": "SJZF",
            "pay_type": "wechat"
        },
        {
            "operation_id": "disable_old",
            "operation_type": "DISABLE",
            "control_id": 5
        }
    ],
    "fail_fast": False
}

response = requests.post("/admin/channel_control/batch", json=data)
```

### 3. 批量管理白名单

```python
data = {
    "whitelists": [
        {
            "operation_id": "add_user_1",
            "operation_type": "CREATE",
            "tg_id": 123456789,
            "channel": "TMPAY",
            "remark": "VIP用户"
        },
        {
            "operation_id": "add_user_2",
            "operation_type": "CREATE",
            "tg_id": 987654321,
            "channel": "SJZF",
            "remark": "测试用户"
        },
        {
            "operation_id": "remove_user",
            "operation_type": "DELETE",
            "tg_id": 111111111,
            "channel": "TMPAY"
        }
    ],
    "fail_fast": True
}

response = requests.post("/admin/channel_control/batch", json=data)
```

### 4. 混合操作示例

```python
data = {
    "channel_controls": [
        {
            "operation_id": "upsert_config",
            "operation_type": "UPSERT",
            "channel": "JLBZF",
            "pay_type": "alipay",
            "min_amount": 50,
            "max_amount": 30000,
            "enabled": True,
            "ratio": 120
        }
    ],
    "whitelists": [
        {
            "operation_id": "add_whitelist",
            "operation_type": "CREATE",
            "tg_id": 555555555,
            "channel": "JLBZF",
            "remark": "新增白名单用户"
        }
    ],
    "fail_fast": False
}

response = requests.post("/admin/channel_control/batch", json=data)
```

## 错误处理

### 常见错误类型

1. **验证错误**: 请求数据格式不正确或缺少必需字段
2. **业务逻辑错误**: 如尝试更新不存在的配置
3. **数据库错误**: 如违反唯一约束
4. **系统错误**: 如数据库连接失败

### 错误响应示例

```json
{
  "total_operations": 3,
  "successful_operations": 1,
  "failed_operations": 2,
  "execution_time_ms": 200,
  "results": [
    {
      "operation_id": "success_op",
      "success": true,
      "data": {"id": 1, "channel": "TMPAY"}
    },
    {
      "operation_id": "validation_error",
      "success": false,
      "error_message": "无效的渠道类型: INVALID_CHANNEL"
    },
    {
      "operation_id": "not_found_error",
      "success": false,
      "error_message": "未找到ID为999的渠道控制配置"
    }
  ]
}
```

## 性能优化

1. **批量数据库操作**: 使用`bulk_create`、`bulk_update`等方法减少数据库往返
2. **事务管理**: 使用数据库事务确保操作原子性
3. **分组处理**: 按操作类型分组处理，提高效率
4. **内存优化**: 避免一次性加载大量数据到内存

## 最佳实践

1. **合理的批次大小**: 建议单次操作不超过1000条记录
2. **使用唯一的operation_id**: 便于追踪和调试
3. **适当的fail_fast设置**: 
   - 数据导入时使用`fail_fast: false`继续处理其他操作
   - 关键操作时使用`fail_fast: true`确保数据一致性
4. **错误处理**: 检查响应中的错误信息并进行相应处理
5. **监控和日志**: 记录批量操作的执行时间和结果用于性能分析

## 测试

运行测试脚本：

```bash
python test_batch_operations.py
```

该脚本包含了各种操作类型的示例数据和API使用说明。

## 注意事项

1. 所有操作都在数据库事务中执行，确保数据一致性
2. `operation_id`必须在单次请求中唯一
3. 渠道和支付类型必须是有效的枚举值
4. 更新和删除操作需要提供正确的`control_id`
5. 白名单操作的`tg_id`和`channel`组合必须唯一