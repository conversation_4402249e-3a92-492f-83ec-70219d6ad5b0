from aiogram import <PERSON><PERSON>, Dispatcher, types

# 你的 Telegram 机器人 Token
TOKEN = '**********************************************'
# 目标群组的 chat_id
TARGET_CHAT_ID = -1002410058975

async def fetch_and_send():
    # 公开消息链接
    message_link = 'https://t.me/playai666/636394'
    
    # 从链接中提取 chat_id 和 message_id
    parts = message_link.split('/')
    chat_id = parts[-2]
    message_id = int(parts[-1])
    print(f'chat_id: {chat_id}, message_id: {message_id}')
    
    # 获取消息内容
    bot = Bot(token=TOKEN)
    await bot.send_message(chat_id=TARGET_CHAT_ID, text='正在获取消息内容...')
    
    await bot.send_message(chat_id=TARGET_CHAT_ID, text=message_link)
    # message = await bot.forward_message(chat_id=TARGET_CHAT_ID, from_chat_id=chat_id, message_id=message_id)
    
    await bot.send_photo(chat_id=TARGET_CHAT_ID, photo='https://www.fas.scot/wp-content/uploads/2017/09/texel_shearling_tup.jpg', caption='这是一张图片')
    
    await bot.send_photo(chat_id=TARGET_CHAT_ID, photo=message_link, caption='这是一张link图片')
    
    # print(f'已发送消息：{message}')


 

import asyncio

if __name__ == '__main__':
    asyncio.run(fetch_and_send())