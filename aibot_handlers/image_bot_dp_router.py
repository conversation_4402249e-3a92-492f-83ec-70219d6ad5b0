import asyncio
from datetime import datetime
import time
import uuid
import json
import logging
from pathlib import Path
import os
from litellm import (
    ChatCompletionAssistantMessage,
    ChatCompletionSystemMessage,
    ChatCompletionUserMessage,
    CustomStreamWrapper,
    LiteLLM,
    acompletion,
    completion,
)


from aiogram import Bot, Router, types, F
from aiogram.fsm.context import FSMContext

# from aiogram import LoggingMiddleware
from aiogram.types import (
    CallbackQuery,
    InlineKeyboardButton,
    BufferedInputFile,
    InlineKeyboardMarkup,
    PreCheckoutQuery,
)
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram import Router
from aiogram.filters import Command, and_f, CommandStart, or_f
from aiogram.filters.callback_data import CallbackData


from dotenv import load_dotenv
from common.bot_common import RechargeNavCallback, RechargeProductCallback
from controllers.bot_hooks.charge_chat import PayMethodCallback
from services import product_service
from services.account_service import AccountService
from utils import cos_util, exception_util

from common import loggers
from aibot_handlers.boilerplate import API
from novelai_api.ImagePreset import ImageModel, ImagePreset, UCPreset, ImageResolution
from common.common_constant import (
    CosPrefix,
    Env,
    ProductType,
    S3Bucket,
    S3BucketUrlPrefix,
)

from services.img_service import ImageBotService
from persistence.models.models_bot_image import ImgGenStatus

from controllers.bot_hooks.role_bot import command_recharge

from controllers.bot_hooks.charge_chat import (
    handle_voucher_cancel,
    handle_recharge_product,
    handle_pay_method,
    handle_successful_payment,
    pre_checkout_query_handler,
)

from services.user_service import user_service
from aibot_handlers.image_server import (
    generate_image,
    image_style_dict,
    image_resolution_dict,
)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler(), loggers.local_bot_help_handler],
)

log = logging.getLogger(__name__)
# 加载环境变量
load_dotenv()

IMAGE_GROUP_ID = os.getenv("IMAGE_GROUP_ID")
IMAG_BOT_NAME = os.getenv("IMAG_BOT_NAME", "hmimage_bot")

image_bot_start_msg = """
官方群：https://t.me/+j9DlTjHJdpwyODQ1
🔞【必讀】作圖玩法說明
（1）只需提供圖片主體，會為您完成圖片創作、補全圖片細節。
（2）可中文輸入，但部分用詞英文更準確。
（3）如需其他風格，請在 /help 中切換。
（4）禁止未成年、涉政、血腥內容。

❤️ 請輸入做圖描述：

自慰，手指插入小穴，雙腿張開，潮吹，少婦，撕破的衣服，巨乳，高潮臉
"""


def get_help_message(
    image_resolution: str = "标清", style: str = "", privacy: str = ""
):
    help_message = (
        "@hmimage_bot ✍️小能手\n\n" "清晰度:{image_res}\n\n" "风格:{style}\n\n"
    )
    image_res_str = image_resolution_dict.get(image_resolution, "标清")
    style_str = image_style_dict.get(style, "")
    privacy_str = "公开" if privacy == "public" else "私密"
    return help_message.format(image_res=image_res_str, style=style_str)


def create_copy_prompt_button(
    bot_username: str, copy_prompt_str: str
) -> InlineKeyboardMarkup:
    log.info(f"create_copy_prompt_button: {bot_username},{copy_prompt_str}")
    # 创建键盘并添加按钮
    copy_prompt_keyboard = InlineKeyboardBuilder()
    # 创建一个跳转按钮
    button = InlineKeyboardButton(
        text="偷走咒语",
        url=f"https://t.me/{bot_username}?start={copy_prompt_str}",  # 指定目标 Bot 的链接
    )
    copy_prompt_keyboard.add(button)
    return copy_prompt_keyboard.as_markup()


def get_help_reply_markup() -> InlineKeyboardMarkup:
    # 生成一个帮助键盘
    help_key_board = InlineKeyboardBuilder()

    # 生成一个风格键盘
    style_key_board = InlineKeyboardBuilder()
    style_key_board.add(
        InlineKeyboardButton(text="--风格设置--", callback_data="set_style")
    )
    for key, value in image_style_dict.items():
        style_key_board.add(InlineKeyboardButton(text=value, callback_data=key))

    style_key_board.adjust(1, 2)

    # 生成一个清晰度键盘
    resolution_key_board = InlineKeyboardBuilder()
    resolution_key_board.add(
        InlineKeyboardButton(text="--清晰度设置--", callback_data="set_resolution")
    )

    for key, value in image_resolution_dict.items():
        resolution_key_board.add(InlineKeyboardButton(text=value, callback_data=key))

    resolution_key_board.adjust(1, 3)

    help_key_board.attach(style_key_board).attach(resolution_key_board)

    return help_key_board.as_markup()


def get_resolution(resolution: str):
    return


def check_image_prompt_args(prompt: str):
    return True


async def send_photo_to_share_group(
    bot: Bot, photo_url: str, style: str, replay_markup: InlineKeyboardMarkup
):
    log.info(f"send_photo_to_share_group: {photo_url},{style}")
    try:
        group_id, topic_id = await ImageBotService.get_share_group_topic(bot.id, style)
        sent_msg = await bot.send_photo(
            chat_id=group_id,
            message_thread_id=topic_id,
            photo=photo_url,
            reply_markup=replay_markup,
        )
    except Exception as e:
        log.error(f"send_image error: {e},{group_id},{topic_id}", exc_info=True)


async def del_msg_delay(bot: Bot, chat_id: int, message_id: int, delay: int = 60):
    log.info(f"del_msg_delay: {chat_id},{message_id},{delay}")
    await asyncio.sleep(delay)
    try:
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
    except Exception as e:
        log.error(f"del_msg_delay error: {e}", exc_info=True)


def create_image_bot_router() -> Router:
    image_bot_router = Router()

    async def gen_and_send_image(
        tg_id: int,
        prompt: str,
        message: types.Message,
        del_pre_msg_id: int = 0,
        user_id: int = 0,
        photo_product=None,
    ):

        log.info(
            f"gen_and_send_image:{tg_id},{prompt},{message.text},user_id:{user_id}"
        )

        basic_profile = await ImageBotService.get_basic_profile(tg_id)
        style = basic_profile.style
        resolution = basic_profile.resolution

        resolution_v = "normal_portrait"

        if resolution:
            if resolution == "img_resolution_low":
                resolution_v = "small_portrait"
            elif resolution == "img_resolution_medium":
                resolution_v = "normal_portrait"
            elif resolution == "img_resolution_high":
                resolution_v = "large_portrait"

        # check 有无生成任务
        is_have_progress = await ImageBotService.check_in_progress_img_gen_task(tg_id)
        if is_have_progress:
            await message.reply("上一个图片正在创作中,请等待上一个任务完成...")
            return
        # 生成图片
        log.info(
            f"gen_and_send_image:{tg_id},{prompt},{basic_profile},{style},{resolution_v}"
        )

        gen_task = await ImageBotService.add_img_gen_task(tg_id, prompt, basic_profile)

        image_result = await generate_image(
            req_id=gen_task.id, prompt=prompt, style=style, resolution=resolution_v
        )
        #     # 返回json格式字符串
        # dict = {
        #     "image_url": "https://www.baidu.com/img/flexible/logo/pc/result.png",
        #     "prompt": prompt,
        #     "request_id": gen_task.id,
        #     "code": 200,
        #     "msg": "success",
        # }

        # image_result = dict

        log.info(f"gen_and_send_image:{image_result}")

        await ImageBotService.update_img_gen_task(
            gen_task.id, ImgGenStatus.COMPLETED, image_result
        )
        try:
            await AccountService.create_pay_order(user_id, photo_product)  # type: ignore
        except Exception as e:
            log.error(f"create_pay_order error: {user_id},{e}", exc_info=True)

        # 发送图片
        image_default = "https://www.baidu.com/img/flexible/logo/pc/result.png"
        if image_result and image_result.get("code") == 200:
            image_url = image_result.get("image_url", image_default)
            # del pre_msg
            if del_pre_msg_id:
                try:
                    await message.bot.delete_message(
                        chat_id=message.chat.id, message_id=del_pre_msg_id
                    )
                except Exception as e:
                    log.error(f"del pre_msg error: {e}", exc_info=True)

            await message.reply_photo(photo=image_url)

            # 同时转发图片到群里

            if basic_profile.privacy == "public":
                try:
                    bot = message.bot
                    # await bot.send_photo(chat_id=-1002410058975,photo=image_url,reply_markup=create_copy_prompt_button(bot_username=IMAG_BOT_NAME,copy_prompt_str="copy_prompt_"+str(gen_task.id)))

                    asyncio.create_task(
                        send_photo_to_share_group(
                            bot=bot,  # type: ignore
                            photo_url=image_url,
                            style=style,
                            replay_markup=create_copy_prompt_button(
                                bot_username=IMAG_BOT_NAME,
                                copy_prompt_str="copy_prompt_" + str(gen_task.id),
                            ),
                        )
                    )
                except Exception as e:
                    log.error(f"send_image error: {e}", exc_info=True)
            else:
                log.info(f"用户隐私设置为私密,不转发图片到群里")
        else:
            log.error(f"gen_and_send_image error: {image_result}")

    @image_bot_router.message(Command("start"))
    async def handle_start_message(message: types.Message, bot: Bot):
        # 获取 /start 后的参数,/start copy_prompt_123
        command, *args = message.text.split(" ")
        log.info(f"handle_start_message:{args}")

        if len(args) == 1 and args[0].startswith("copy_prompt_"):
            # 处理复制提示词的逻辑
            copy_prompt_id = args[0].split("_")[-1]
            log.info(f"copy_prompt_id:{copy_prompt_id}")

            # 从数据库中获取提示词
            img_task = await ImageBotService.get_img_gen_task_by_id(int(copy_prompt_id))

            if img_task:
                prompt = img_task.prompt
                log.info(f"copy_prompt_id:{copy_prompt_id},prompt:{prompt}")
                # 发送提示词
                prompt_text = f"{prompt}\n\n稍后将会自动销毁"
                photo_url = img_task.gen_result.get("image_url")  # type: ignore
                sent_msg = await message.reply_photo(photo=photo_url, caption=prompt_text, allow_sending_without_reply=True)  # type: ignore
                # 删除消息
                asyncio.create_task(
                    del_msg_delay(bot=bot, chat_id=message.chat.id, message_id=sent_msg.message_id, delay=60)  # type: ignore
                )
            else:
                log.info(f"copy_prompt_id:{copy_prompt_id},not found")
        else:
            start_msg = await ImageBotService.get_img_bot_start_msg(bot_id=bot.id)
            await message.reply(
                start_msg, parse_mode="html", allow_sending_without_reply=True
            )

    @image_bot_router.message(and_f(Command("help"), (F.chat.type == "private")))
    async def send_image_help(message: types.Message):

        log.info(f"send_image_help:{message.text}")

        basic_img_profile = await ImageBotService.get_basic_profile(
            message.from_user.id
        )
        help_message = get_help_message(
            basic_img_profile.resolution, basic_img_profile.style
        )
        help_reply_markup = get_help_reply_markup()

        try:
            bot = message.bot
            sent_msg = await bot.send_message(
                message.from_user.id,
                help_message,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"send_image_help error: {e}", exc_info=True)

    @image_bot_router.message(
        and_f(
            Command("help"), ((F.chat.type == "group") | (F.chat.type == "supergroup"))
        )
    )
    async def send_image_help_group(message: types.Message):

        log.info(f"send_image_help:{message.text}")

        try:
            await message.reply("请在bot内使用该功能")
        except Exception as e:
            log.error(f"send_image_help error: {e}", exc_info=True)

    # 通过私聊bot的text作为prompt生成图片
    @image_bot_router.message(F.text & (F.chat.type == "private"))
    async def gen_image_by_text_promt(
        message: types.Message, bot: Bot, state: FSMContext
    ):

        log.info(f"gen_image_by_text_promt:{message.text}")
        prompt = message.text if message.text else ""
        tg_id = message.from_user.id
        try:

            user = await user_service.get_user_by_tg_id(tg_id)
            if not user:
                user = await user_service.register_by_tg(
                    tg_user_id=tg_id,
                    first_name=message.from_user.first_name,
                    last_name=message.from_user.last_name,
                    user_name=message.from_user.username,
                )
            balance = await AccountService.get_total_balance(user.id)
            # todo: replace with new_photo_product
            basic_profile = await ImageBotService.get_basic_profile(tg_id)
            photo_product = await product_service.get_online_photo_bot_product(
                basic_profile.resolution
            )
            if not photo_product:
                log.error("Photo Product Not Found")
                await message.reply("Photo Product Not Found")

            if balance < photo_product.price:
                # todo: 余额不足
                log.error(f"余额不足,当前余额:{balance},最低充值:{photo_product.price}")
                await message.reply(
                    f"余额不足,当前余额:{balance},请充值之后再试～",
                    allow_sending_without_reply=True,
                )
                await command_recharge(message=message, bot=bot, state=state)
            else:
                sent_msg = await message.reply("正在创作中,请稍等...")
                asyncio.create_task(
                    gen_and_send_image(
                        tg_id,
                        prompt,
                        message=message,
                        del_pre_msg_id=sent_msg.message_id,
                        user_id=user.id,
                        photo_product=photo_product,
                    )
                )

        except Exception as e:
            log.error(f"send_image_promt error: {e}", exc_info=True)

    @image_bot_router.callback_query(lambda c: c.data.startswith("img_resolution_"))
    async def set_resolution(call: types.CallbackQuery):

        log.info(f"set_resolution:{call.data}")

        image_resolution = (
            call.data if call.data else list(image_resolution_dict.keys())[0]
        )
        b_profile = await ImageBotService.update_b_profile_resolution(
            call.from_user.id, image_resolution
        )
        help_message = get_help_message(
            image_resolution=b_profile.resolution, style=b_profile.style
        )
        help_reply_markup = get_help_reply_markup()

        try:
            bot = call.bot
            msg_id = call.message.message_id
            await bot.edit_message_text(
                text=help_message,
                chat_id=call.from_user.id,
                message_id=msg_id,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"set_resolution error: {e}", exc_info=True)
        finally:
            await call.answer()

    @image_bot_router.callback_query(lambda c: c.data.startswith("image_style_"))
    async def set_imag_style(call: types.CallbackQuery):

        log.info(f"set_imag_style:{call.data}")

        image_style = call.data if call.data else ""
        img_b_profile = await ImageBotService.update_b_profile_style(
            call.from_user.id, image_style
        )
        help_message = get_help_message(
            style=img_b_profile.style, image_resolution=img_b_profile.resolution
        )
        help_reply_markup = get_help_reply_markup()

        try:
            bot = call.bot
            msg_id = call.message.message_id
            await bot.edit_message_text(
                text=help_message,
                chat_id=call.from_user.id,
                message_id=msg_id,
                reply_markup=help_reply_markup,
                parse_mode="HTML",
            )
        except Exception as e:
            log.error(f"set_style error: {e}", exc_info=True)
        finally:
            await call.answer()

    # 通过 /image 命令生成图片
    @image_bot_router.message(Command("image"))
    async def image_by_prompt(message: types.Message):

        log.info(f"image_by_prompt:{message.text}")

        # 获取用户输入的参数
        command, *args = message.text.split()
        tg_id = message.from_user.id

        prompt = " ".join(args)
        if not check_image_prompt_args(prompt):
            await message.reply(
                "参数错误,请检查,正确格式为:/image promptxxx,例如:/image litter girl"
            )
            return

        await message.reply("正在创作中,请稍等")

        asyncio.create_task(gen_and_send_image(tg_id, prompt, message=message))

    return image_bot_router


def create_img_recharge_bot_router() -> Router:
    img_recharge_bot_router = Router()

    # 1.返回充值方式菜单
    @img_recharge_bot_router.message(Command("recharge"))
    async def handle_recharge_message(
        message: types.Message, bot: Bot, state: FSMContext
    ):
        log.info(f"handle_recharge_message:{message.text}")
        await command_recharge(message=message, bot=bot, state=state)

    # 2. 选择具体方式的某个产品
    @img_recharge_bot_router.callback_query(RechargeNavCallback.filter())
    async def handle_voucher_cancel_adp(
        query: CallbackQuery,
        callback_data: RechargeNavCallback,
        bot: Bot,
        state: FSMContext,
    ):
        state_data = await state.get_data()
        log.info(
            f"handle_voucher_cancel_adp:{query.data},{callback_data},state_data:{state_data}"
        )
        await handle_voucher_cancel(query, callback_data, bot, state)

    @img_recharge_bot_router.callback_query(RechargeProductCallback.filter())
    async def handle_recharge_product_adp(
        query: CallbackQuery,
        bot: Bot,
        callback_data: RechargeProductCallback,
        state: FSMContext,
    ):
        state_data = await state.get_data()
        log.info(
            f"handle_recharge_product_adp:{query.data},RechargeProductCallback:{callback_data},state:{state_data}"
        )
        await handle_recharge_product(query, bot, callback_data, state)

    @img_recharge_bot_router.callback_query(PayMethodCallback.filter())
    async def handle_pay_method(
        query: CallbackQuery,
        bot: Bot,
        callback_data: PayMethodCallback,
        state: FSMContext,
    ):
        # await on_product_selected(query, bot, callback_data, state)
        ...

    @img_recharge_bot_router.pre_checkout_query()
    async def pre_checkout_query_handler_adp(
        query: PreCheckoutQuery, bot: Bot, state: FSMContext
    ):
        await pre_checkout_query_handler(query, bot, state)

    return img_recharge_bot_router
