
from datetime import datetime
import logging
import os



from aiogram import Router, types
# from aiogram import LoggingMiddleware
from aiogram.types import InlineKeyboardButton, CallbackQuery
from aiogram import Router
from aiogram.filters import Command
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.fsm.context import FSMContext

from dotenv import load_dotenv



from common import loggers


from services.bot_group.group_user_op_service import group_user_op_service
from common.models.ai_bot_admin.user_card_admin_bean import OpAction,OpActionType


from services.bot_group.group_msg_service import GroupUserCardInfoService 
from services.bot_group.bot_group_service import AutoReplyRuleService
from services.bot_group.group_msg_stat_service import GroupMsgStatService

from persistence.models.models_bot_group import BotGroupFeatureConfig




logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",handlers=[logging.StreamHandler(),loggers.local_bot_help_handler])

log = logging.getLogger(__name__)
# 加载环境变量
load_dotenv()

BACK_STATS_MENU = InlineKeyboardButton(text="🏘返回", callback_data='stats')

def is_digit(value):
    if value.startswith('-'):
        return value[1:].isdigit()
    return value.isdigit()

def check_tg_id_args(args):
    if len(args) != 1 or not args[0].isdigit():
        return False
    return True

def check_msg_cx_h_args(args):
    if len(args) != 2 or not is_digit(args[0]): 
        return False
    try:
        datetime.strptime(args[1], "%Y-%m-%dT%H")
    except ValueError:
        return False
    return True

def check_msg_cx_d_args(args):
    log.info(f"check_msg_cx_d_args:{args}")
    if len(args) != 2 or not is_digit(args[0]):
        return False
    try:
        datetime.strptime(args[1], "%Y-%m-%d")
    except ValueError:
        return False
    return True

def check_inv_cx_d_args(args):
    if len(args) != 1:
        return False
    try:
        datetime.strptime(args[0], "%Y-%m-%d")
    except ValueError:
        return False
    return True

#todo: 加权限和group验证

def create_staff_admin_router() -> Router:
    staff_admin_router = Router()

    @staff_admin_router.message(Command("help"))
    async def send_help(message: types.Message):
        help_str_html = """
        <b>指令说明</b>
        <b>/userinfo tg_id:</b> 查询用户信息
        <b>/unmute tg_id:</b> 解除禁言
        <b>/unban tg_id:</b> 解封
        <b>/msg_cx_h group_id YYYY-MM-DDTHH:</b> 查询群组在某个小时的发言统计，例如：
        <code>/msg_cx_h -1002463227172 2023-10-05T14</code> 代表查询群id为-1002463227172 2023-10-05日14～15时内的发言统计
        <b>/msg_cx_d group_id YYYY-MM-DD:</b> 查询群组在某一天的发言统计，例如：
        <code>/msg_cx_d -1002463227172 2023-10-05</code> 代表查询群id为-1002463227172 2023-10-05当天的发言
        <b>/inv_cx_d YYYY-MM-DD:</b> 查询某一天的邀请统计，例如：
        <code>/inv_cx_d 2023-10-05</code> 代表查询2023-10-05当天的邀请
        <b>/share_cx_d YYYY-MM-DD:</b> 查询某一天的分享统计，例如：
        <code>/share_cx_d 2023-10-05</code> 代表查询2023-10-05当天的分享
        <b>/auto_author_send user_name msg_link:</b> 设置自动回复规则，例如：
        <code>/auto_author_send walker https://t.me/c/123456</code> 代表设置walker用户的自动回复规则为链接为https://t.me/c/123456的消息
        <code>/group_join_check_reg on/off:</code> 开启或关闭新用户入群检测注册功能，例如：
        <code>/group_join_check_reg on</code> 开启新用户入群检测注册功能
        <code>/group_join_check_reg off</code> 关闭新用户入群检测注册功能
        <b>群id：</b>
        @playai666 -1002357871790
        @playai888 -1002223050046
        """
        await message.answer(help_str_html,parse_mode="HTML")
    @staff_admin_router.message(Command("userinfo"))
    async def send_user_info(message: types.Message):

        log.info(f"get_user_info:{message.text}")
         # 获取用户输入的参数
        # 获取用户输入的参数
        command, *args = message.text.split()
        # 打印所有参数
        log.info(f"Command: {command}, Args: {args}")
        
        if  len(args) != 1 or not args[0].isdigit():
            await message.reply("参数错误,请检查,正确格式为:/userinfo tg_id,例如:/userinfo 123456")
            return
        tg_id = int(args[0])
        user_info_message,mark_up = await GroupUserCardInfoService.get_user_card_info_and_key_markup(tg_id)

        await message.reply(user_info_message, reply_markup=mark_up)


    @staff_admin_router.message(Command("unmute"))
    async def umute_user(message: types.Message):
        # 获取用户输入的参数
        command, *args = message.text.split()
        if not check_tg_id_args(args):
            await message.reply("参数错误,请检查,正确格式为:/unmute tg_id,例如:/numute 123456")
            return

        tg_id = int(args[0])
        op_action = OpAction(operator_nickname=message.from_user.full_name,operator_id=message.from_user.id,tg_id=tg_id,action_type=OpActionType.UNMUTE)
        await group_user_op_service.exec_op_action(op_action)

    @staff_admin_router.message(Command("unban"))
    async def unban_user(message: types.Message):
        # 获取用户输入的参数
        command, *args = message.text.split()
        if not check_tg_id_args(args):
            await message.reply("参数错误,请检查,正确格式为:/unban tg_id,例如:/unban 123456")
            return

        tg_id = int(args[0])
        op_action = OpAction(operator_nickname=message.from_user.full_name,operator_id=message.from_user.id,tg_id=tg_id,action_type=OpActionType.UNBAN)
        await group_user_op_service.exec_op_action(op_action)

    @staff_admin_router.message(Command("msg_cx_h"))
    async def user_msg_cx_hours(message: types.Message):
        # 获取用户输入的参数
        command, *args = message.text.split()
        if not check_msg_cx_h_args(args):
            await message.reply("参数错误,请检查,正确格式为:/msg_cx_h group_id YYYY-MM-DDTHH,例如:/msg_cx_h -1002463227172 2023-10-05T14 代表查询群id为-1002463227172 2023-10-05日14～15时内的发言统计")
            return
        group_id = int(args[0])
        cx_date = datetime.strptime(args[1], "%Y-%m-%dT%H")
        msg_str = await GroupMsgStatService.cx_group_msg_status(group_id=group_id,cx_date=cx_date.date(),cx_hour=cx_date.hour)
        
        await message.answer(msg_str,parse_mode="HTML")
    @staff_admin_router.message(Command("msg_cx_d"))
    async def user_msg_cx_days(message: types.Message):
        # 获取用户输入的参数
        command, *args = message.text.split()
        if not check_msg_cx_d_args(args):
            await message.reply("参数错误,请检查,正确格式为:/msg_cx_d group_id YYYY-MM-DD,例如:/msg_cx_d -1002463227172 2023-10-05 代表查询群id为-1002463227172 2023-10-05当天的发言")
            return
        group_id = int(args[0])
        cx_date = datetime.strptime(args[1], "%Y-%m-%d")
        msg_str = await GroupMsgStatService.cx_group_msg_status(group_id=group_id,cx_date=cx_date.date())
        
        log.info(f"cx_group_msg_status:{msg_str}")
        
        await message.answer(msg_str,parse_mode="HTML")
    
    @staff_admin_router.message(Command("inv_cx_d"))
    async def user_invite_cx_days(message: types.Message):
        
        command, *args = message.text.split()
        if not check_inv_cx_d_args(args):
            await message.reply("参数错误,请检查,正确格式为:/inv_cx_d YYYY-MM-DD,例如:/inv_cx_d 2023-10-05 代表查询2023-10-05当天的邀请")
            return
        cx_date = datetime.strptime(args[0], "%Y-%m-%d")
        msg_str = await GroupMsgStatService.cx_user_invite_share_stat(cx_date=cx_date.date())
        
        await message.answer(msg_str,parse_mode="HTML")
    
    @staff_admin_router.message(Command("share_cx_d"))
    async def user_share_cx_days(message: types.Message):
        
        command, *args = message.text.split()
        if not check_inv_cx_d_args(args):
            await message.reply("参数错误,请检查,正确格式为:/share_cx_d YYYY-MM-DD,例如:/share_cx_d 2023-10-05 代表查询2023-10-05当天的分享")
            return
        cx_date = datetime.strptime(args[0], "%Y-%m-%d")
        msg_str = await GroupMsgStatService.cx_user_share_stat(cx_date=cx_date.date())
        await message.answer(msg_str,parse_mode="HTML")
    
    @staff_admin_router.message(Command("auto_author_send"))
    async def auto_author_send(message: types.Message):
        # 获取用户输入的参数
        command, *args = message.text.split()
        if len(args) != 2:
            await message.reply("参数错误,请检查,正确格式为:/auto_author_send user_name msg_link,例如:/auto_author_send walker https://t.me/c/123456")
            return
        author_name = args[0]
        msg_link = args[1]
        
        result = await AutoReplyRuleService.add_author_auto_reply_rule_by_msg_link(msg_link,author_name)
        await message.answer(result)
    
    @staff_admin_router.message(Command("group_join_check_reg"))
    async def group_join_check_reg(message: types.Message, state: FSMContext):
        """
        检查群组是否开启了新用户加入检测的功能
        """
        command, *args = message.text.split()
        if len(args) != 1 or args[0] not in ['on', 'off']:
            await message.reply("参数错误,请检查,正确格式为:/group_join_check_reg on/off,例如:/group_join_check on")
            return
        switch = args[0]
        if switch == 'on':
            is_enabled = True
        else:
            is_enabled = False
        
        cfg_list = await BotGroupFeatureConfig.filter(key_name="group_join_check_reg")
        
        for cfg in cfg_list:
            cfg.enabled = is_enabled
            await cfg.save()
        
        
        if is_enabled:
            await message.answer(f"群组已开启用户入群3小时检测注册功能")
        else:
            await message.answer(f"群组已关闭用户入群3小时检测注册功能")
    
        
    return staff_admin_router
def create_user_card_router() -> Router:
    user_card_router = Router()

    # @user_card_router.message(Command("userinfo"))
    # async def send_user_info(message: types.Message):

    #     log.info(f"get_user_info:{message.text}")
    #      # 获取用户输入的参数
    #     # 获取用户输入的参数
    #     command, *args = message.text.split()
    #     # 打印所有参数
    #     log.info(f"Command: {command}, Args: {args}")
        
    #     if  len(args) != 1 or not args[0].isdigit():
    #         await message.reply("参数错误,请检查,正确格式为:/userinfo tg_id,例如:/userinfo 123456")
    #         return
    #     tg_id = int(args[0])
    #     user_info_message,mark_up = await GroupUserCardInfoService.get_user_card_info_and_key_markup(tg_id)

    #     await message.reply(user_info_message, reply_markup=mark_up)


    # @user_card_router.message(Command("unmute"))
    # async def umute_user(message: types.Message):
    #     # 获取用户输入的参数
    #     command, *args = message.text.split()
    #     if not check_tg_id_args(args):
    #         await message.reply("参数错误,请检查,正确格式为:/unmute tg_id,例如:/numute 123456")
    #         return

    #     tg_id = int(args[0])
    #     op_action = OpAction(operator_nickname=message.from_user.full_name,operator_id=message.from_user.id,tg_id=tg_id,action_type=OpActionType.UNMUTE)
    #     await group_user_op_service.exec_op_action(op_action)

    # @user_card_router.message(Command("unban"))
    # async def unban_user(message: types.Message):
    #     # 获取用户输入的参数
    #     command, *args = message.text.split()
    #     if not check_tg_id_args(args):
    #         await message.reply("参数错误,请检查,正确格式为:/unban tg_id,例如:/unban 123456")
    #         return

    #     tg_id = int(args[0])
    #     op_action = OpAction(operator_nickname=message.from_user.full_name,operator_id=message.from_user.id,tg_id=tg_id,action_type=OpActionType.UNBAN)
    #     await group_user_op_service.exec_op_action(op_action)

    
    # 查询用户卡片的信息
    @user_card_router.callback_query(lambda c: c.data.endswith('user_card_info'))
    async def process_callback_user_info(callback_query: CallbackQuery):
        bot = callback_query.bot
        tg_id = callback_query.data.split(":")[0]

        user_info_message,mark_up = await GroupUserCardInfoService.get_user_card_info_and_key_markup(int(tg_id))

        await bot.edit_message_text(user_info_message, chat_id=callback_query.message.chat.id, message_id=callback_query.message.message_id, reply_markup=mark_up)

    # 查询用户的发言信息
    @user_card_router.callback_query(lambda c: c.data.endswith('_msg_cx'))
    async def process_callback_user_msg_cx(callback_query: CallbackQuery, state: FSMContext):
        bot = callback_query.bot
        tg_id = callback_query.data.split(':')[0]
        cx_type = callback_query.data.split(":")[1]
        ori_msg = callback_query.message.text # type: ignore

        desc =  ori_msg
        if cx_type == 'today_msg_cx':
            desc = f"{desc}\n+今日发言查询"

            msg_str = await GroupUserCardInfoService.get_u_recent_msg_str(int(tg_id),0)
        elif cx_type == '7day_msg_cx':
            desc = f"{desc}\n + 7日发言查询"
            msg_str = await GroupUserCardInfoService.get_u_recent_msg_str(int(tg_id),7)

        state_msg = f"{desc}:\n" + msg_str

        
        if len(state_msg) > 4096:
            state_msg = state_msg[:4096]
        back_markup = InlineKeyboardBuilder()
        back_markup.add(
            InlineKeyboardButton(text="🏘返回", callback_data=f'{tg_id}:user_card_info')
        )

        await bot.edit_message_text(state_msg, chat_id=callback_query.message.chat.id, message_id=callback_query.message.message_id, reply_markup=back_markup.as_markup())

    #信息删除
    @user_card_router.callback_query(lambda c: c.data.endswith('_msg_del')) 
    async def process_callback_msg_delete(callback_query: CallbackQuery):
        bot = callback_query.bot
        tg_id = callback_query.data.split(':')[0]
        del_type = callback_query.data.split(":")[1]
        ori_msg = callback_query.message.text # type: ignore

        from_user_nickname = callback_query.from_user.full_name


        op_action_type = OpActionType.from_code(del_type)
        op_action = OpAction(operator_nickname=from_user_nickname,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=op_action_type)

        await group_user_op_service.exec_op_action(op_action)

        if del_type == 'all_msg_del':
            # 立即删除所有消息的逻辑
            await callback_query.answer(f"{tg_id}所有消息已删除:")
        elif del_type == 'all_img_msg_del':
            # 立即删除所有图片消息的逻辑
            await callback_query.answer( f"{tg_id}所有图片消息已删除")
        elif del_type == '10_min_img_msg_del':
            # 10分钟后删除所有图片消息的逻辑
            await callback_query.answer(f"{tg_id}10分钟后将删除所有图片消息")
        elif del_type == '30_min_img_msg_del':
            # 30分钟后删除所有图片消息的逻辑
            await callback_query.answer(f"{tg_id}30分钟后将删除所有图片消息")
        else:
            # 未知的删除类型
            await callback_query.answer(f"{tg_id}错误未知的删除类型")



    # 信息禁言
    @user_card_router.callback_query(lambda c: c.data.endswith('_msg_forbid'))
    async def process_callback_msg_forbid(callback_query: CallbackQuery):

        tg_id = callback_query.data.split(':')[0]
        forbid_time = callback_query.data.split(":")[1]

        op_action_type = OpActionType.from_code(forbid_time)
        op_action = OpAction(operator_nickname=callback_query.from_user.full_name,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=op_action_type)

        await group_user_op_service.exec_op_action(op_action)

        if forbid_time == '5min_msg_forbid':
            # 5分钟禁言
            await callback_query.answer(f"{tg_id},5分钟禁言")
        elif forbid_time == '10min_msg_forbid':
            # 10分钟禁言
            await callback_query.answer(f"{tg_id},10分钟禁言")
        elif forbid_time == '30min_msg_forbid':
            # 30分钟禁言
            await callback_query.answer(f"{tg_id},30分钟禁言")
        elif forbid_time == '1hour_msg_forbid':
            # 1小时禁言
            await callback_query.answer(f"{tg_id},1小时禁言")


    #kick and ban
    @user_card_router.callback_query(lambda c: c.data.endswith('_kick'))
    async def process_callback_kick(callback_query: CallbackQuery):
        bot = callback_query.bot
        tg_id = callback_query.data.split(':')[0]
        kick_type = callback_query.data.split(":")[1]

        op_action_type = OpActionType.from_code(kick_type)
        op_action = OpAction(operator_nickname=callback_query.from_user.full_name,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=op_action_type)

        await group_user_op_service.exec_op_action(op_action)
        if kick_type == 'now_kick':
            # 立即踢出
            await callback_query.answer(f"{tg_id}:立即踢出")

    #ban
    @user_card_router.callback_query(lambda c: c.data.endswith('_ban'))
    async def process_callback_ban(callback_query: CallbackQuery):

        tg_id = callback_query.data.split(':')[0]
        ban_type = callback_query.data.split(":")[1]

        op_action_type = OpActionType.from_code(ban_type)
        op_action = OpAction(operator_nickname=callback_query.from_user.full_name,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=op_action_type)

        await group_user_op_service.exec_op_action(op_action)
        if ban_type == 'forever_ban':
            # 永久封禁
            await callback_query.answer(f"{tg_id}:永久封禁")


    #unmute
    @user_card_router.callback_query(lambda c: c.data.endswith(':unmute'))
    async def process_callback_unmute(callback_query: CallbackQuery):
        tg_id = callback_query.data.split(':')[0]
        op_action = OpAction(operator_nickname=callback_query.from_user.full_name,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=OpActionType.UNMUTE)
        await group_user_op_service.exec_op_action(op_action)
        await callback_query.answer(f"{tg_id}:解除禁言")

    #unban
    @user_card_router.callback_query(lambda c: c.data.endswith(':unban'))
    async def process_callback_unban(callback_query: CallbackQuery):
        tg_id = callback_query.data.split(':')[0]
        op_action = OpAction(operator_nickname=callback_query.from_user.full_name,operator_id=callback_query.from_user.id,tg_id=int(tg_id),action_type=OpActionType.UNBAN)
        await group_user_op_service.exec_op_action(op_action)
        await callback_query.answer(f"{tg_id}:解封")
    
    return user_card_router