from datetime import UTC, datetime, timedelta
from itertools import groupby
import json
import logging
import os
from decimal import *
from fastapi import APIRouter, Depends, Header
from fastapi.encoders import jsonable_encoder
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field
from common.common_constant import SKIP_QUEUE_RECHARGE_PRODUCT_ID
from common.entity import MsgResponse, OkxDeposit, RechargeSetting
from controllers.user_check import get_current_user
from persistence.models.models import (
    ExpirableAward,
    Product,
    RechargeChannelConfig,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    UserChatBenefit,
)
from services import gift_award_service, product_service, recharge_channel_service, user_service
from services.account_service import AccountService
import services.recharge_service as recharge_service
from services.user import user_benefit_chat_queue_service
from utils import descriptions, file_util
from utils.translate_util import _t, _tl


recharge_router = APIRouter()

log = logging.getLogger(__name__)

okx_recipient = os.getenv("OKX_RECIPiENT")
alchemy_recipient = os.getenv("ALCHEMY_RECIPIENT_ADDRESS")
alchemy_chain = os.getenv("ALCHEMY_CHAIN")


class RechargeOrderResponse(BaseModel):
    order_id: str
    user_id: int
    amount: str
    balance: int
    pay_fee: str
    created_at: int
    expire_at: int | None
    desc: str = Field(..., description="充值描述")

    @staticmethod
    def from_model(
        order: RechargeOrder | None, exp_order: ExpirableAward, language: str, skip_queue_benefit: UserChatBenefit | None
    ) -> "RechargeOrderResponse | None":
        if order is None:
            return None
        total_amount = order.amount
        balance = total_amount
        if exp_order is not None:
            total_amount = exp_order.total_amount
            balance = exp_order.balance
        desc = descriptions.format_charge_channel(order, language)
    
        if exp_order.expires_at.year > 2100:
            expire = None
            pay_fee = str(order.pay_fee)
        else:
            expire = int(exp_order.expires_at.timestamp())
            pay_fee = "0"
        if skip_queue_benefit:
            expire = int(skip_queue_benefit.valid_end_at.timestamp())
        if exp_order.from_type != "PAYED":
            desc += f' {_t("金币", language)}'
        return RechargeOrderResponse(
            order_id=str(order.recharge_order_id),
            user_id=order.user_id,
            amount=str(total_amount),
            balance=str(balance),
            pay_fee=pay_fee,
            expire_at=expire,
            created_at=int(order.created_at.timestamp()),
            desc=desc,
        )


class RechargeProductResponse(BaseModel):
    recharge_product_id: str
    title: str
    desc: str
    price: int
    display_price: str
    corner_title: str
    corner_tip: str
    original_price_desc: str
    promotion_price_desc: str
    feature_list: list[str]
    amount: int
    reward_amount: int
    total_amount: int
    max_charge_times: int
    expire_desc: str
    promotion_desc: str
    reward_desc: str
    remarks: str
    button_text: str = ""
    recharged_times: int = 0
    can_charge_more: bool = False
    order: int = 0
    supported_pay_types: list[str] = []

    @staticmethod
    def from_model(product: RechargeProduct) -> "RechargeProductResponse":
        display_price = str(Decimal(product.price) / 100 / 1000)
        display_price = f"${display_price}"
        price_desc = display_price
        if product.original_price_desc:
            price_desc = product.original_price_desc
            display_price = product.original_price_desc
        can_charge_more = True
        return RechargeProductResponse(
            recharge_product_id=str(product.recharge_product_id),
            title=product.title,
            desc=product.desc,
            price=product.price,
            display_price=display_price,
            corner_title=product.corner_title,
            corner_tip=product.corner_tip,
            original_price_desc=price_desc,
            promotion_price_desc=product.promotion_price_desc,
            feature_list=product.feature_list,
            amount=product.amount,
            reward_amount=product.reward_amount,
            total_amount=product.amount + product.reward_amount,
            max_charge_times=product.max_charge_times,
            expire_desc=product.expire_desc,
            promotion_desc=product.promotion_desc,
            reward_desc=product.reward_desc,
            button_text=product.button_text if can_charge_more else "已达上限",
            remarks=product.remarks,
            can_charge_more=can_charge_more,
            order=product.order,
        )


class RechargeDataRequest(BaseModel):
    recharge_id: str = Field(..., description="充值档位id", min_length=1)


class UsdtRechargeDataRequest(BaseModel):
    recharge_id: str = Field(..., description="充值档位id", min_length=1)
    chain: str = "EVM"


class RechargeProcessReponse(MsgResponse):
    checkout_url: str = Field(..., description="支付页url")


class RechargeSettingResponse(MsgResponse):
    products: list[RechargeProductResponse] = Field(..., description="充值档位列表")


class ExpirableBalance(BaseModel):
    balance: int
    expires_at: int


class UserBalanceReponse(MsgResponse):
    balance: int = Field(..., description="钻石余额")
    recharge_orders: list[RechargeOrderResponse] = Field(..., description="充值记录")
    expirable_balance: ExpirableBalance | None = Field(
        None, description="会过期的赠送钻石"
    )
    payed_balance: int = 0
    reward_balance: int = 0


class USDTRechargeResponse(BaseModel):
    recipient: str
    display_fee: str
    expired_at: int
    chain: str
    order_id: str


# 返回充值档位
@recharge_router.get("/recharge/list")
async def recharge_list(
    current_language: str = Header(None), user_id: int = Depends(get_current_user)
) -> RechargeSettingResponse:
    # is_pay_user = await user_service.is_payed_user(user_id)
    recharge_products = await recharge_service.list_user_products(user_id)
    products: list[RechargeProductResponse] = []
    for p in recharge_products:
        products.append(RechargeProductResponse.from_model(p))
    configs = await recharge_channel_service.get_configs()
    config_dict = {str(config.recharge_product_id): config for config in configs}
    products.sort(key=lambda x: x.order)
    for product in products:
        product.title = _tl(product.title, current_language,RechargeProduct.__name__)
        product.desc = _tl(product.desc, current_language,RechargeProduct.__name__)
        product.promotion_desc = _tl(product.promotion_desc, current_language,RechargeProduct.__name__)
        product.reward_desc = _tl(product.reward_desc, current_language,RechargeProduct.__name__)
        product.remarks = _tl(product.remarks, current_language,RechargeProduct.__name__)
        product.corner_title = _tl(product.corner_title, current_language,RechargeProduct.__name__)
        product.corner_tip = _tl(product.corner_tip, current_language,RechargeProduct.__name__)
        product.expire_desc = _tl(product.expire_desc, current_language,RechargeProduct.__name__)
        product.button_text = _tl(product.button_text, current_language,RechargeProduct.__name__)
        channel_config = config_dict.get(product.recharge_product_id)
        if channel_config is not None:
            product.supported_pay_types = [type for type in channel_config.supported_pay_types() if type != "CLOSED"]
        else:
            product.supported_pay_types = RechargeChannelConfig.all_pay_types()

    return RechargeSettingResponse(products=products)


# 返回用户的钻石余额
@recharge_router.get("/recharge/balance")
async def recharge_balance(
    user_id: int = Depends(get_current_user), current_language: str = Header(None)
) -> UserBalanceReponse:
    balance = await AccountService.get_balance(user_id)

    now = datetime.now(UTC)
    expirable_balances = await gift_award_service.get_award_balance_with_expired(
        user_id
    )
    balance += sum([exp.balance for exp in expirable_balances if exp.expires_at > now])
    exp_dict = {balance.out_order_id: balance for balance in expirable_balances}

    payed_balance = await AccountService.get_payed_total_balance(user_id)
    reward_balance = await AccountService.get_reward_total_balance(user_id)

    recharge_order_ids = [balance.out_order_id for balance in expirable_balances]
    recharge_orders = await recharge_service.get_recharge_orders_by_id(
        user_id, recharge_order_ids
    )
    recharge_orders_dict = {
        str(order.recharge_order_id): order for order in recharge_orders
    }

    skip_queue_benefits = await user_benefit_chat_queue_service.get_all_skip_queue_benefit(user_id)
    recharge_order_id_skip_queue_benefit = {benefit.recharge_order_id: benefit for benefit in skip_queue_benefits}
    orders = [
        RechargeOrderResponse.from_model(
            recharge_orders_dict.get(str(exp.out_order_id)), exp, current_language, recharge_order_id_skip_queue_benefit.get(str(exp.out_order_id))
        )
        for exp in expirable_balances
        if exp.out_order_id != "G1"
    ]
    orders = [order for order in orders if order is not None]
    expirable = None
    gift = await gift_award_service.get_award_by_user_gift(user_id, "G1")
    if gift is not None:
        orders.insert(
            0,
            RechargeOrderResponse(
                order_id="G1",
                user_id=user_id,
                amount=str(gift.total_amount),
                balance=gift.balance,
                pay_fee="0",
                created_at=int(gift.claim_at.timestamp()),
                expire_at=int(gift.expires_at.timestamp()),
                desc=_t("新用户-限时礼包", current_language),
            ),
        )
        expirable = ExpirableBalance(
            balance=gift.balance, expires_at=int(gift.expires_at.timestamp())
        )
    orders.sort(key=lambda x: x.created_at, reverse=True)
    return UserBalanceReponse(
        balance=balance,
        recharge_orders=orders,
        expirable_balance=expirable,
        payed_balance=payed_balance,
        reward_balance=reward_balance,
    )


@recharge_router.post("/recharge/usdt")
async def recharge_usdt(
    request: UsdtRechargeDataRequest, user_id: int = Depends(get_current_user)
) -> USDTRechargeResponse:
    log.debug(f"recharge request: {request}")
    try:
        usdt_order = await recharge_service.create_usdt_recharge_order(
            alchemy_recipient, user_id, request.recharge_id, alchemy_chain
        )
        display_fee = str(Decimal(usdt_order.final_fee) / 1000 / 100)
        return JSONResponse(
            content=jsonable_encoder(
                USDTRechargeResponse(
                    recipient=usdt_order.recipient_address,
                    display_fee=display_fee,
                    expired_at=int(usdt_order.expired_at.timestamp()),
                    chain=alchemy_chain,
                    order_id=str(usdt_order.order_id),
                )
            )
        )
    except ValueError as ve:
        log.error(f"recharge usdt error: {ve}")
        return JSONResponse(
            content={"status": "error", "message": ve.args[0]}, status_code=410
        )


@recharge_router.get("/recharge/usdt/success")
async def recharge_usdt_success():
    j = json.loads("{}")
    deposit = OkxDeposit(**j)
    await recharge_service.usdt_recharge_success(deposit)


@recharge_router.get("/recharge/usdt_order_status")
async def recharge_order_status(
    usdt_order_id: str, user_id: int = Depends(get_current_user)
):
    order = await recharge_service.get_usdt_order(usdt_order_id)
    if order is None:
        return JSONResponse(
            content={"status": "error", "message": "order not found"}, status_code=404
        )
    if order.user_id != user_id:
        return JSONResponse(
            content={"status": "error", "message": "not your order"}, status_code=403
        )
    return JSONResponse(content={"status": order.status, "order_id": usdt_order_id})
