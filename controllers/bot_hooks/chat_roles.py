import asyncio
import json
import logging
import math
from aiogram import Bot, types
from aiogram.enums import ParseMode
from aiogram.filters import CommandObject
from aiogram.types import (
    CallbackQuery,
    Message,
)
from aiogram.utils.deep_linking import create_start_link
from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder
from aiogram.fsm.context import FSMContext
from common.common_constant import Language, RoleTag
from common.role_model import RoleRes
from persistence.models.models import TgBotConfig
from services import (
    activity_diamond_season_service,
    role_config_service,
    tg_config_service,
    user_diamond_season_service,
    user_service,
)
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from utils import role_util, str_util, user_growth_constants
from utils.translate_util import _tl
from .bot_setting import (
    ActivityDiamondSeasonEnrollCallback,
    ActivityDiamondSeasonSlideCallback,
    RoleSelectCallback,
    router,
    NewRoleSlideCallback,
)
from controllers.bot_hooks import bot_setting


async def send_role(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    if not command.args:
        await message.reply("请重新选择一个角色")
        return
    role_id = int(command.args)
    role = await RoleConfigService.get_role_config(role_id)
    builder = InlineKeyboardBuilder()
    link = await create_start_link(bot, json.dumps({"role_id": role_id}), encode=True)
    builder.button(text="开始聊天", url=link)
    intro = str_util.format_char(role.introduction, role.role_name)
    content = f"""<b>{role.role_name}</b>\n{intro}"""
    await bot.send_photo(
        message.chat.id,
        photo=str_util.format_avatar(role.role_avatar),
        caption=content,
        reply_markup=builder.as_markup(),
    )


async def send_role_card(
    message: types.Message,
    bot: Bot,
    state: FSMContext,
    page: int,
    refresh: bool = False,
    tag=RoleTag.CHOSEN.value,
    language:str = Language.ZH.value
):
    bot_config: TgBotConfig = await tg_config_service.get_bot_config_by_id(bot.id)  # type: ignore
    tg_user_id = message.from_user.id
    nsfw = bot_config.bot_nsfw  # mask all images # not is_sfw_bot(bot)
    role = await role_config_service.role_bot_next(nsfw, page - 1, language)
    builder = InlineKeyboardBuilder()
    builder.button(text=_tl("开始陪聊", language), callback_data=RoleSelectCallback(role_id=role.id))
    button_size = 0
    if page > 1:
        builder.button(
            text=_tl("上一页", language), callback_data=NewRoleSlideCallback(page=page - 1, tag=tag)
        )
        button_size += 1
    builder.button(
        text=_tl("下一页", language), callback_data=NewRoleSlideCallback(page=page + 1, tag=tag)
    )
    button_size += 1
    if language != Language.EN.value:
        builder.button(text=_tl("查看更多", language), url=user_growth_constants.ROLE_CHANNEL_LINK)
        url = await bot_setting.create_tg_search_url(tg_user_id, bot.id,language)
        builder.button(text=_tl("搜索", language), url=url)
    builder.adjust(1, button_size, 1)
    intro = str_util.tg_caption_cut(role.introduction)
    content = f"""<b>{role.role_name}</b>\n{intro}"""
    bot_config: TgBotConfig = await tg_config_service.get_bot_config_by_id(bot.id) # type: ignore
    avatar = (
        str_util.handle_spoiler_avatar(role.role_avatar)
        if role.image_nsfw and not (bot_config.bot_nsfw and bot_config.show_nsfw_image)
        else str_util.format_avatar(role.role_avatar)
    )
    if refresh:
        await bot.edit_message_media(
            chat_id=message.chat.id,
            message_id=message.message_id,
            media=types.InputMediaPhoto(media=avatar, caption=content),
            reply_markup=builder.as_markup(),
        )
    else:
        await bot.send_photo(
            chat_id=message.chat.id,
            photo=avatar,
            caption=content,
            reply_markup=builder.as_markup(),
        )
    await state.set_state(None)


# 聊天返钻活动
async def send_activity_role_card(
    message: types.Message,
    bot: Bot,
    state: FSMContext,
    page: int,
    role_ids_str: str,
    refresh: bool = False,
    task_id: str = "",
):
    role_str_ids = role_ids_str.split(",")
    role_ids = [int(role_id) for role_id in role_str_ids]
    role_list = await role_loader_service.list_by_ids(role_ids)
    role_map = {role.id: role for role in role_list}
    valid_role_ids = []
    for role_id in role_ids:
        if role_id in role_map:
            role = role_map[role_id]
            if not role or not role.status or not role.privacy:
                continue
            else:
                valid_role_ids.append(role_id)

    if page < 0 or page >= len(valid_role_ids):
        page = 0
    current_role_id = valid_role_ids[page]
    role_config = role_map[current_role_id]
    role_config = role_util.format_role_config(role_config, "")
    role = RoleRes.from_model(role_config, False)
    builder = InlineKeyboardBuilder()
    builder.button(
        text="开始陪聊", callback_data=RoleSelectCallback(role_id=current_role_id)
    )
    msize = 0
    if page > 0:
        builder.button(
            text="上一页",
            callback_data=ActivityDiamondSeasonSlideCallback(
                page=page - 1, task_id=task_id
            ),
        )
        msize += 1
    if page < len(valid_role_ids) - 1:
        builder.button(
            text="下一页",
            callback_data=ActivityDiamondSeasonSlideCallback(
                page=page + 1, task_id=task_id
            ),
        )
        msize += 1
    builder.adjust(1, msize)
    intro = role.introduction
    content = f"""<b>{role.role_name}</b>\n{intro}"""
    nsfw_bot = await tg_config_service.nsfw_bot(bot.id)
    spoiler_avatar = bool(role.image_nsfw and not nsfw_bot)
    avatar = (
        str_util.handle_spoiler_avatar(role.role_avatar)
        if spoiler_avatar
        else str_util.format_avatar(role.role_avatar)
    )
    if refresh:
        await bot.edit_message_media(
            chat_id=message.chat.id,
            message_id=message.message_id,
            media=types.InputMediaPhoto(media=avatar, caption=content),
            reply_markup=builder.as_markup(),
        )
    else:
        await bot.send_photo(
            message.chat.id,
            photo=avatar,
            caption=content,
            reply_markup=builder.as_markup(),
        )
    await state.set_state(None)


# 聊天返钻活动
@router.callback_query(ActivityDiamondSeasonSlideCallback.filter())
async def handle_activity_diamond_season_page(
    query: CallbackQuery,
    callback_data: ActivityDiamondSeasonSlideCallback,
    bot: Bot,
    state: FSMContext,
):
    task_id = callback_data.task_id
    task = await activity_diamond_season_service.get_task_by_task_id(task_id)
    if task is None:
        return
    role_ids = task.role_ids
    role_ids_str = ",".join([str(role_id) for role_id in role_ids])
    await send_activity_role_card(
        query.message, bot, state, callback_data.page, role_ids_str, True, task_id
    )


# 聊天过程中收到报名通知，点击callback按钮的报名
@router.callback_query(ActivityDiamondSeasonEnrollCallback.filter())
async def handle_activity_diamond_season_enroll(
    query: CallbackQuery,
    callback_data: ActivityDiamondSeasonEnrollCallback,
    bot: Bot,
    state: FSMContext,
):
    task_id = callback_data.task_id
    tg_id = query.from_user.id
    user = await user_service.get_user_by_tg_id(tg_id)
    if user is None:
        return
    role_ids, msg = await user_diamond_season_service.enroll_diamond_season_activity(
        user.id, task_id
    )
    if role_ids:
        basic_msg = "报名成功，当前可继续输入文字和角色卡聊天"
        # -1 表示支持所有角色卡
        if -1 in role_ids:
            await bot.send_message(
                query.message.chat.id, basic_msg, parse_mode=ParseMode.HTML
            )
        else:
            extra_msg = basic_msg + "\n\n" + "该时间段参与消耗💎活动的角色卡如下👇"
            await bot.send_message(
                query.message.chat.id, extra_msg, parse_mode=ParseMode.HTML
            )
            role_ids_str = ",".join([str(role_id) for role_id in role_ids])
            await send_activity_role_card(
                query.message, bot, state, 0, role_ids_str, False, task_id
            )
    else:
        await bot.send_message(query.message.chat.id, msg, parse_mode=ParseMode.HTML)
    await state.set_state(None)


async def get_role_list(message: Message, bot: Bot, state: FSMContext, language: str):
    return await send_role_card(message, bot, state, 1, language=language)
