import asyncio
from datetime import <PERSON><PERSON><PERSON>
import logging
import random
import re
import os
import uuid
from aiogram import <PERSON><PERSON>, Di<PERSON>atcher, Router, types, F
from aiogram.enums import ParseMode
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.storage.mongo import MongoStorage
from aiogram.filters.callback_data import CallbackData
from common.bot_common import Button
from common.common_constant import (
    ChatModeType,
    ChatPlatform,
    Language,
    RoleTag,
)
from common.models.chat_model import HistoryRequest
from common.models.chat_model import ChatBotStateData
from services import (
    role_access_service,
    tg_config_service,
    tg_message_service,
    user_service,
)
from services.chat import chat_message_service, chat_model_switch_history
from services.role import role_loader_service
from persistence.chat_history import chat_history_persistence
from persistence.models.models import TgBotConfig, User
from utils import message_utils, role_util, str_util
from utils.translate_util import _t, _tl

log = logging.getLogger(__name__)


class VoucherStateGroup(StatesGroup):
    voucher = State()


class VoiceCallback(CallbackData, prefix="voice"):
    message_id: str
    version: int


class RegenerateCallback(CallbackData, prefix="regenerate"):
    message_id: str
    version: int


class RoleSelectCallback(CallbackData, prefix="role"):
    role_id: int


class RoleListPageCallback(CallbackData, prefix="role_page"):
    page: int


class InlineCheckInCallback(CallbackData, prefix="inline_check_in"):
    c: int = 0


class RechargeCallback(CallbackData, prefix="recharge"):
    amount: int = 0


class NewRoleSlideCallback(CallbackData, prefix="new_role_slide"):
    page: int = 0
    tag: str = RoleTag.CHOSEN.value


class ActivityDiamondSeasonSlideCallback(CallbackData, prefix="dss"):
    page: int = 0
    # role_ids: str # comma separated role ids
    task_id: str = ""


class ActivityDiamondSeasonEnrollCallback(CallbackData, prefix="adse"):
    task_id: str = ""


class ArchivedRoleSelectCallback(CallbackData, prefix="archive_role"):
    role_id: int


class ArchivedRoleSlideCallback(CallbackData, prefix="archive_slide"):
    offset: int
    limit: int


storage = MongoStorage(chat_history_persistence.client, db_name=os.environ["MONGO_DB"])
router = Router()
router.message.filter(F.chat.type.in_({"private"}))

dp = Dispatcher(storage=storage)
dp.include_router(router)


async def safe_clear_message(bot: Bot, chat_id: int, mids: list[int]):
    try:
        await bot.delete_messages(chat_id, mids)
    except:
        pass


async def safe_clear_markup(bot: Bot, chat_id: int, mids: list[int]):
    try:
        await asyncio.gather(
            *[
                bot.edit_message_reply_markup(
                    chat_id=chat_id, message_id=mid, reply_markup=None
                )
                for mid in mids
            ]
        )
    except:
        pass


async def start_new_chat(
    chat_id: int, bot: Bot, state: FSMContext, user: User, language: str
):
    state_data = await state.get_data()
    role_id = state_data.get("role_id")
    log.info(
        f"start_new_chat:  user_id={user.id},chat_id={chat_id},state_data={state_data},language={language}"
    )
    if not role_id:
        log.warning(f"Role id not found in state data,user_id={user.id}")
        ids = await role_loader_service.list_ids_by_user_index(nsfw=True)
        role_id = ids[random.randint(0, len(ids) - 1)]
    role = await role_loader_service.load_translated_role(
        role_id, language, user.nickname
    )
    if not role:
        log.error(f"Role not found: {role_id}")
        return await bot.send_message(chat_id, _tl("角色不存在, 请重新选择", language))
    bot_config: TgBotConfig = await tg_config_service.get_bot_config_by_id(bot.id)  # type: ignore
    content = str_util.tg_caption_cut(role.introduction)
    content = str_util.format_tg_html_text(content)
    if (not role.image_nsfw) or (bot_config.bot_nsfw and bot_config.show_nsfw_image):
        await bot.send_photo(chat_id, photo=role.role_avatar, caption=content)
    else:
        await bot.send_message(chat_id, content)
    history_req = HistoryRequest(
        mode_type=ChatModeType.SINGLE.value,
        role_id=role_id,
        new_start=1,
        language=language,
    )
    history_req = await chat_message_service.init_first_history_message(
        user, history_req, platform=ChatPlatform.CHAT_BOT.value
    )
    history_response = await chat_message_service.list_user_history(user, history_req)
    chat_list = history_response.chat_list
    scenario = role_util.get_role_scenario(role)
    fm_content = message_utils.bot_message_display_format(
        role.role_name, chat_list[0].content, user.status_block_switch
    )
    conversation_id = history_response.conversation_id
    fm_content = f"{scenario}\n\n{fm_content}"
    await bot.send_message(chat_id, fm_content, parse_mode=ParseMode.HTML)
    await state.update_data({"conversation_id": conversation_id, "role_id": role.id})
    # 检查当前用户选择的聊天模型是否支持该角色(不支持，就发对应消息)
    await role_access_service.handle_bot_chat_role_support_model_check(
        chat_id, bot, user, role, conversation_id, chat_list[0].message_id, language
    )


class BotSetting(CallbackData, prefix="bot_setting"):
    # model,status_block,chat_channel
    type: str
    select: str

    @staticmethod
    def build_button(text: str = "", type: str = "", select: str = "") -> Button:
        return Button(text=text, callback_data=BotSetting(type=type, select=select))


async def send_login_link(chat_id: int, bot: Bot, language: str = Language.ZH.value):
    url = await user_service.create_tg_login_link(chat_id, language)
    content = (
        f"手機端點擊下方鏈接複製到瀏覽器打開\n\n`{url}`\n\n[電腦端點擊這裡]({url})"
    )
    message = await bot.send_message(chat_id, content, parse_mode=ParseMode.MARKDOWN_V2)
    await tg_message_service.add_deleted_message(
        message, expire_delta=timedelta(minutes=5), bot_id=bot.id
    )


async def create_tg_search_url(
    tg_user_id: int, bot_id: int, language: str = Language.ZH.value
):
    url = await user_service.create_tg_login_link(tg_user_id, language)
    config = await tg_config_service.get_bot_config_by_id(bot_id)
    if config:
        return f"{url}&bot_username={config.username}"
    return url


async def get_state(state: FSMContext) -> ChatBotStateData:
    bot_state_data = await state.get_data()
    return ChatBotStateData(**bot_state_data) if bot_state_data else ChatBotStateData()


async def update_state_new_user_guide(state: FSMContext, new_user_guide: bool) -> None:
    bot_state_data = await get_state(state)
    bot_state_data.new_user_guide = new_user_guide
    await state.update_data({"new_user_guide": new_user_guide})


async def update_state_alone(
    state: FSMContext, key: str, value: int | str | bool
) -> None:
    await state.update_data({key: value})
