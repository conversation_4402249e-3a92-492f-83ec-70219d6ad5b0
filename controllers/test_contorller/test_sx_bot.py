import io
import logging
from PIL import Image
from fastapi.encoders import jsonable_encoder
from fastapi import Form, Query, Request, Depends, APIRouter, Response, UploadFile
from fastapi.responses import JSONResponse

from tasks import amount_expire_notify, go_tma_daily_notify, go_tma_new_user_notify, recall_message, tg_message_task
from tasks.sx_bot_f_msg_task import  forward_msg, forward_msg_queue
from tasks.statistic_vip_job import cal_vip_job_daily, run_cal_vip_job_daily

from common.models.forward_msg import ForwardMsg
from services.sx_bot_service import MessageSender



message_sender = MessageSender(bot_token='7357790700:AAHt_84ueO_t6bN3Pw-ewANthYIW8I4cO_k',queue=forward_msg_queue,name='test')


from dotenv import load_dotenv


load_dotenv()

test_sx_router = APIRouter()

log = logging.getLogger(__name__)




@test_sx_router.get("/test_sx/f_msg")
async def t_forward_msg() :
    f_msg  = ForwardMsg(tg_id=1,user_id=1,channel_id=1,msg_type='text',text='test')
    await forward_msg(f_msg)
    return "forward_msg"




@test_sx_router.get("/test_sx/f_msg_q")
async def t_forward_msg_que() :
    f_msg  = ForwardMsg(tg_id=6913571035,user_id=188,channel_id=1,msg_type='text',text='test forward msg')
    await forward_msg(f_msg)
    return "forward_msg"


@test_sx_router.get("/test_sx/f_msg_c")
async def t_forward_msg_que_consume() :
    await message_sender.process_queue()
    return "forward_msg"

@test_sx_router.get("/test_sx/cal_vip_job_by_date")
async def t_cal_vip_job_daily(create_date: str = Query(..., description="Date in YYYY-MM-DD format")) :
    await cal_vip_job_daily(create_date)
    return "cal_vip_job_daily"

@test_sx_router.post("/test_sx/run_vip_job_daily")
async def t_run_cal_vip_job_daily() :
    await run_cal_vip_job_daily()
    return "run_cal_vip_job_daily"
    

@test_sx_router.post("/test_sx/recall_message")
async def test_send_recall():
    await recall_message.recall_message()

@test_sx_router.post("/test_sx/recall_expire")
async def test_send_expire():
    await amount_expire_notify.recall_expires()

@test_sx_router.post("/test_sx/del_message")
async def test_delete_message():
    await tg_message_task.delete_tg_message()

@test_sx_router.post("/test_task/tma_daily_notify")
async def test_go_tma_notify():
    await go_tma_daily_notify.tma_daily_notify()

@test_sx_router.post("/test_task/tma_new_user_notify")
async def test_go_tma_new_user_notify():
    await go_tma_new_user_notify.tma_new_user_notify()
