import io
import logging
from PIL import Image
from fastapi.encoders import jsonable_encoder
from fastapi import Form, Header, Request, Depends, APIRouter, Response, UploadFile
from fastapi.responses import JSONResponse
from common.common_constant import ERROR_CODE, Error<PERSON>ey, Language
from common.role_card import CharacterBook
from common.role_model import RoleDataConfig
from persistence import char_book_dao
from persistence.models.models import RoleConfig
from services.role_config_service import RoleConfigService
from dotenv import load_dotenv

from utils import response_util
from utils.image_util import load_new_image_info, load_original_image_info
from utils.translate_util import _t

load_dotenv()

char_book_router = APIRouter()

log = logging.getLogger(__name__)


UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


@char_book_router.get("/role_book/list")
async def book_list() -> list[CharacterBook]:
    books = await char_book_dao.list_all()
    return books


@char_book_router.post("/role_card/analysis/img")
async def analysis_card_img(image: UploadFile):
    try:
        analysis_ret = await RoleConfigService.analysis_card_img(image)
    except Exception as e:
        log.warning(f"Error in analysis_card_img: {e}")
        return JSONResponse(content={"message": "卡图片格式有问题"}, status_code=400)
    return JSONResponse(content=analysis_ret)


@char_book_router.post("/role_card/analysis/img/v1")
async def analysis_card_img_v1(
    image: UploadFile,
    current_language: str = Header(default=Language.ZH.value),
):
    try:
        analysis_ret = await RoleConfigService.analysis_card_img(image)
    except Exception as e:
        log.warning(f"Error in analysis_card_img: {e}")
        error = _t(ErrorKey.TAVERN_IMAGE_ERROR.value, current_language)
        return response_util.error(ERROR_CODE.PARAM_ERROR.value, error)
    return response_util.ok(analysis_ret)


@char_book_router.post("/role_card/analysis/img_original_info")
async def img_original_info(image: UploadFile):
    try:
        img_bytes = image.file.read()
        content_type = image.content_type

        image = Image.open(io.BytesIO(img_bytes))
        tavern_card = load_original_image_info(image)
        return JSONResponse(content=tavern_card)
    except Exception as e:
        log.warning(f"Error in analysis_card_img: {e}")
        return JSONResponse(content={"message": "No card detected"}, status_code=400)
