import json
import logging
from typing import Optional
import urllib.parse
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from common.common_constant import RechargeRouteStrategy, RpDisplayFilter
from persistence.models.models import RechargeChannelConfig, RechargeChannelEnum, RechargeOrder, RechargeStatusEnum
from services import ff_recharge_service, jlbzf_recharge_service, qszf_recharge_service, recharge_service, sdfkw_recharge_service, sjzf_recharge_service, tmpay_recharge_service, ttzf_recharge_service, out_recharge_common, recharge_channel_service, xjtzf_recharge_service
from services.out_recharge_common import RechargeRequest, create_recharge_order

from controllers.user_check import user_service
from utils import response_util, tg_util
from persistence.redis_client import redis_client
from utils.translate_util import _tl

monitor_chat_id = '-1002550423097'

class PaymentResponse(BaseModel):
    success: bool
    message: str
    status_code: Optional[int] = None
    pay_url: Optional[str] = None
    raw: Optional[str] = None
    out_order_id: Optional[str] = None

class RechargeHandleResult(BaseModel):
    success: bool = False
    status_code: int
    message: str
    pay_url: Optional[str] = None

class RechargeChannelHandler:
    def __init__(self, channel_enum: RechargeChannelEnum):
        self.channel_enum = channel_enum
        
    async def create_order(self, user_id: int, req: RechargeRequest, client_ip: str, is_whitelist: bool = False) -> PaymentResponse:
        recharge_id = req.recharge_id
        bot_id = req.bot_id
        type_name = req.type
        order = await create_recharge_order(
            user_id, recharge_id, self.channel_enum,
            type_name, bot_id, is_whitelist, req.platform)
        if order is None:
            return PaymentResponse(success=False, message="找不到套餐", status_code=404, out_order_id="")
        channel_name = self.channel_enum.description
        try:
            result = await self._process_payment(order, type_name, client_ip)
            if result.success:
                return result

            logging.warning(f"充值渠道 {channel_name} 处理失败: {result.raw}")
            await self.update_order_status(str(order.recharge_order_id), RechargeStatusEnum.FAILED)
            await tg_util.sm({
                '充值失败': f'渠道: {channel_name}', 
                '支付方式': type_name,
                'user_id': f'`{user_id}`',
                '套餐': f'`{recharge_id}`', 
                '返回': result.raw or ''
            }, monitor_chat_id)
            return PaymentResponse(
                success=False,
                message="充值渠道发生异常，请稍后再试",
                status_code=400,
                raw=str(result.raw),
                out_order_id=str(order.recharge_order_id)
            )
        except Exception as e:
            logging.error(f"充值渠道 {channel_name} 发生异常: {str(e)}")
            await self.update_order_status(str(order.recharge_order_id), RechargeStatusEnum.FAILED)
            await tg_util.sm({
                '充值异常': f'渠道: {channel_name}',
                '支付方式': type_name,
                'user_id': f'`{user_id}`',
                '套餐': f'`{recharge_id}`', 
                '异常': str(e)
            }, monitor_chat_id)
            return PaymentResponse(
                success=False,
                message="充值渠道发生异常，请稍后再试",
                status_code=500,
                raw=str(e),
                out_order_id=str(order.recharge_order_id)
            )

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        raise NotImplementedError("implement this method in subclasses")

    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        raise NotImplementedError("implement this method in subclasses")
    
    async def update_order_status(self, order_id: str, status: RechargeStatusEnum) -> None:
        await out_recharge_common.update_order_status(order_id, status)

class TmpayHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.TMPAY)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        data = tmpay_recharge_service.create_tmpay_order(order, type_name, client_ip)
        logging.info(f'tmpay_recharge_response: {data}')
        if data['status'] != 200:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        if not dry_run:
            await self.update_out_order_id(str(order.recharge_order_id), data['data']['tradeNo'], data['data']['payUrl'])
        return PaymentResponse(success=True, message="success", pay_url=data['data']['payUrl'])

    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await tmpay_recharge_service.update_out_order_id(order_id, out_order_id, pay_url)


class QszfHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.QSZF)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        data = qszf_recharge_service.create_qszf_order(order, type_name, client_ip)
        logging.info(f'qszf_recharge_response: {data}')
        if data['status'] != 200:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        if not dry_run:
            await self.update_out_order_id(str(order.recharge_order_id), data['data']['tradeNo'], data['data']['payUrl'])
        return PaymentResponse(success=True, message="success", pay_url=data['data']['payUrl'])
    
    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await qszf_recharge_service.update_out_order_id(order_id, out_order_id, pay_url)


class SdfkwHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.SDFKW_API)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        data = sdfkw_recharge_service.create_sdfkw_order(order, type_name, client_ip)
        logging.info(f'sdfkw_recharge_response: {data}')
        if data['code'] != 1:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        pay_url = data['data']['payurl']
        parsed_url = urllib.parse.urlparse(pay_url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        trade_no = query_params.get('trade_no', [None])[0]
        if not dry_run:
            await self.update_out_order_id(str(order.recharge_order_id), trade_no, pay_url)
        return PaymentResponse(success=True, message="success", pay_url=pay_url)
    
    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await sdfkw_recharge_service.update_out_order_id(order_id, out_order_id, pay_url)

class FFPayHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.FFPAY)

    async def _try_create_ffpay_order(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> tuple[bool, dict, str]:
        data = ff_recharge_service.create_ffpay_order(order, type_name, client_ip)
        logging.info(f'ffpay_recharge_response: {data}')
        if data['code'] != 1:
            return False, data, json.dumps(data, ensure_ascii=False)

        pay_url = data.get('payurl', data.get('qrcode'))
        if not dry_run:
            await self.update_out_order_id(str(order.recharge_order_id), data['trade_no'], pay_url)
        return True, data, pay_url

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        # Try up to 3 times
        for attempt in range(1, 4):
            success, data, result = await self._try_create_ffpay_order(order, type_name, client_ip, dry_run)

            if success:
                return PaymentResponse(success=True, message="success", pay_url=result)

            if attempt < 3:
                logging.warning(f"FFPay attempt {attempt} failed, retrying... Error: {result}")

        return PaymentResponse(
            success=False,
            message="支付失败",
            status_code=400,
            out_order_id=str(order.recharge_order_id)
        )

    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await ff_recharge_service.update_out_order_id(order_id, out_order_id, pay_url)

class SJZFHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.SJZF)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        response = sjzf_recharge_service.create_sjzf_order(order, type_name, client_ip)
        logging.info(f'sjzf_recharge_response: {response}')
        if response['status'] != 200:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(response, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        data = response['data']
        out_order_id = data['tradeNo']
        pay_url = data.get('payUrl')
        if not dry_run:
            await self.update_out_order_id(str(order.recharge_order_id), out_order_id, pay_url)
        return PaymentResponse(success=True, message="success", pay_url=pay_url)

    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await out_recharge_common.update_out_order_id(order_id, out_order_id, pay_url)

class JLBZFHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.JLBZF)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        data = jlbzf_recharge_service.create_jlbzf_order(order, type_name, client_ip)
        logging.info(f'jlbzf_recharge_response: {data}')
        if data['status'] != '1':
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        pay_url = data.get('h5_url', data.get('pay_url', data.get('sdk_url')))
        out_order_id = self._extract_out_order_id(pay_url)
        if not out_order_id:
            out_order_id = str(order.recharge_order_id)
        if not dry_run:
            await self.update_out_order_id(str(order.recharge_order_id), out_order_id, pay_url)
        return PaymentResponse(success=True, message="success", pay_url=pay_url)

    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await out_recharge_common.update_out_order_id(order_id, out_order_id, pay_url)

    def _extract_out_order_id(self, url: str) -> str:
        parsed_url = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        if 'key' in query_params and query_params['key']:
            out_order_id = query_params['key']
            if out_order_id:
                return out_order_id[0]

        if 'a' in query_params and query_params['a']:
            out_order_id = query_params['d']
            if out_order_id:
                return out_order_id[0]
        return ""

class XJTZFHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.XJTZF)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        data = xjtzf_recharge_service.create_xjtzf_order(order, type_name, client_ip)
        logging.info(f'xjtzf_recharge_response: {data}')
        if data['code'] != 0:
            return PaymentResponse(
                success=False,
                message="支付失败",
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )
        pay_url = data['data']['payData']
        out_order_id = data['data']['payOrderId']
        if not dry_run:
            await self.update_out_order_id(str(order.recharge_order_id), out_order_id, pay_url)
        return PaymentResponse(success=True, message="success", pay_url=pay_url)

    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await xjtzf_recharge_service.update_out_order_id(order_id, out_order_id, pay_url)

class TTZFHandler(RechargeChannelHandler):
    def __init__(self):
        super().__init__(RechargeChannelEnum.TTZF)

    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str, dry_run: bool = False) -> PaymentResponse:
        data = ttzf_recharge_service.create_ttzf_order(order, type_name, client_ip)
        logging.info(f'ttzf_recharge_response: {data}')
        
        pay_data = data.get('data', {})
        if data.get('code') == 0 and pay_data and pay_data.get('payUrl'):
            pay_url = pay_data['payUrl']
            out_order_id = pay_data.get('tradeNo', str(order.recharge_order_id))
            if not dry_run:
                await self.update_out_order_id(str(order.recharge_order_id), out_order_id, pay_url)
            return PaymentResponse(success=True, message="success", pay_url=pay_url)
        else:
            return PaymentResponse(
                success=False,
                message=data.get('msg', "支付失败"),
                status_code=400,
                raw=json.dumps(data, ensure_ascii=False),
                out_order_id=str(order.recharge_order_id)
            )

    async def update_out_order_id(self, order_id: str, out_order_id: str, pay_url: str) -> None:
        await ttzf_recharge_service.update_out_order_id(order_id, out_order_id, pay_url)

class RechargeHandlerFactory:
    @staticmethod
    def get_handler(channel_name: str) -> RechargeChannelHandler:
        handlers = {
            'TMPAY': TmpayHandler(),
            'QSZF': QszfHandler(),
            'SDFKW_API': SdfkwHandler(),
            'FFPAY': FFPayHandler(),
            'SJZF': SJZFHandler(),
            'JLBZF': JLBZFHandler(),
            'XJTZF': XJTZFHandler(),
            'TTZF': TTZFHandler(),
        }
        return handlers.get(channel_name, TmpayHandler())  # 默认使用TmpayHandler

class RechargeChannelManager:
    @staticmethod
    async def process_with_whitelist(user_id: int, req: RechargeRequest, client_ip: str, channel: str) -> PaymentResponse:

        handler = RechargeHandlerFactory.get_handler(channel)
        logging.info(f"白名单：尝试使用充值渠道: {channel}")
        result = await handler.create_order(user_id, req, client_ip, True)
        if result.success:
            logging.info(f"白名单：充值渠道 {channel} 处理成功")
            return result
        else:
            logging.info(f"白名单：充值渠道 {channel} 不可用: {result.message}， 尝试下一个渠道")

        return PaymentResponse(success=False, message="白名单渠道暂时不可用", status_code=503)

    @staticmethod
    async def process_with_failover(user_id: int, req: RechargeRequest, client_ip: str,
                                   primary_channel: str, secondary_channels: list[str]) -> PaymentResponse:
        available_channels = []
        channels_to_try = recharge_channel_service.get_all_can_use_channels()
        if primary_channel in channels_to_try:
            available_channels.append(primary_channel)

        for ch in secondary_channels:
            if ch in channels_to_try:
                available_channels.append(ch)

        if not available_channels:
            logging.error("所有充值渠道均不可用")
            return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

        # 依次尝试所有可用渠道
        for channel_name in available_channels:
            handler = RechargeHandlerFactory.get_handler(channel_name)
            logging.info(f"尝试使用充值渠道: {channel_name}")
            result = await handler.create_order(user_id, req, client_ip)
            if result.success:
                logging.info(f"充值渠道 {channel_name} 处理成功")
                return result
            else:
                logging.info(f"充值渠道 {channel_name} 不可用: {result.message}， 尝试下一个渠道")
                continue

        return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

    @staticmethod
    async def process_with_manual_routing(user_id: int, req: RechargeRequest, client_ip: str) -> PaymentResponse:
        # 获取充值产品信息以获取金额
        recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
        if not recharge_product:
            return PaymentResponse(success=False, message="找不到充值套餐", status_code=404)

        pay_type = 'wechat' if req.type == 'wxpay' else req.type
        recent_orders = await recharge_service.get_recent_init_orders(user_id)
        same_type_orders = [o for o in recent_orders if o.pay_type == pay_type]
        last_channel = None
        if len(same_type_orders) > 0:
            last_channel = same_type_orders[-1].recharge_channel.value
        amount = recharge_product.cny_price
        user_controls = await recharge_channel_service.get_manual_strategy_channels(pay_type, amount)
        all_channels = [c.channel.value for c in user_controls]
        logging.info(f"{user_id}-{req.recharge_id}-手动路由：支付类型={pay_type}, 金额={recharge_product.cny_price}, 渠道={all_channels}, 上次渠道={last_channel}")
        channel = await recharge_channel_service.get_channel_queue_item(pay_type)
        logging.info(f"{user_id}-{req.recharge_id}-手动路由：当前渠道={channel}")
        if last_channel and last_channel == channel:
            channel = next((c for c in all_channels if c != last_channel), None)
            logging.info(f"{user_id}-{req.recharge_id}-手动路由：上次使用的渠道与当前相同，跳过 {last_channel}, 使用下一个可用渠道 {channel}")
            if not channel:
                logging.info(f"{user_id}-{req.recharge_id}-手动路由：没有其他可用渠道，使用上次渠道 {last_channel}")
                channel = last_channel
        if channel and channel in all_channels:
            all_channels.remove(channel)
            all_channels.insert(0, channel)

        logging.info(f"{user_id}-{req.recharge_id}-手动路由：最终渠道列表={all_channels}")
        for channel_name in all_channels:
            handler = RechargeHandlerFactory.get_handler(channel_name)
            logging.info(f"尝试使用充值渠道: {channel_name}")
            result = await handler.create_order(user_id, req, client_ip)
            if result.success:
                logging.info(f"充值渠道 {channel_name} 处理成功")
                return result
            else:
                logging.info(f"充值渠道 {channel_name} 不可用: {result.message}， 尝试下一个渠道")
                continue

        return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

    @staticmethod
    async def process_with_smart_routing(user_id: int, req: RechargeRequest, client_ip: str) -> PaymentResponse:
        # 获取充值产品信息以获取金额
        recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
        if not recharge_product:
            return PaymentResponse(success=False, message="找不到充值套餐", status_code=404)

        # 确定用户类型，这里简化为根据支付类型判断
        queue_type = 'type1' if req.channel == 'channel1' else 'type2'
        pay_type = 'wechat' if req.type == 'wxpay' else req.type
        amount = recharge_product.cny_price

        # 获取智能路由队列，传递支付类型和金额
        primary_queue, secondary_queue = await recharge_channel_service.get_channel_queues_for_routing(queue_type, pay_type, amount)

        # 根据用户历史过滤渠道
        primary_queue = await recharge_channel_service.filter_channels_by_user_history(primary_queue, user_id)

        all_channels = primary_queue

        logging.info(f"智能路由：渠道类型={queue_type}, 支付类型={pay_type}, 金额={recharge_product.cny_price}")
        logging.info(f"智能路由：主队列={primary_queue}, 备用队列={secondary_queue}")

        rkey = 'recharge_channels_used'
        used_channels = redis_client.lrange(rkey, -5, -1) # type: ignore

        used_channels: list[str] = [c.decode() for c in used_channels]
        used_with_order = {c:i for i, c in enumerate(used_channels)}
        all_channels.sort(key = lambda x: used_with_order.get(x, -1))

        if not all_channels:
            logging.info("智能路由：所有充值渠道均不可用，fallback 所有可用渠道")
            all_channels = await recharge_channel_service.get_enabled_channels_for_request(pay_type, amount)
            if not all_channels:
                return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

        # 依次尝试所有可用渠道
        for channel_name in all_channels:
            handler = RechargeHandlerFactory.get_handler(channel_name)
            logging.info(f"智能路由：尝试使用充值渠道: {channel_name}")
            result = await handler.create_order(user_id, req, client_ip)
            if result.success:
                logging.info(f"智能路由：充值渠道 {channel_name} 处理成功")
                redis_client.rpush(rkey, channel_name)
                return result
            else:
                logging.info(f"智能路由：充值渠道 {channel_name} 不可用: {result.message}， 尝试下一个渠道")
                continue

        return PaymentResponse(success=False, message="所有支付渠道暂时不可用，请稍后再试", status_code=503)

async def handle_recharge_request_base(user_id: int, req: RechargeRequest, client_ip: str,
                                       channel_config: RechargeChannelConfig,
                                       current_language: str) -> RechargeHandleResult:
    recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
    if recharge_product is None:
        return RechargeHandleResult(status_code=404, message=_tl("找不到充值套餐", current_language))

    is_pay_user = await user_service.is_payed_user(user_id)
    if (is_pay_user and recharge_product.display_filters and
            (RpDisplayFilter.DISABLED_AFTER_PURCHASE in recharge_product.display_filters or RpDisplayFilter.NEW_USER_ONLY in recharge_product.display_filters)
        ):
        return RechargeHandleResult(status_code=403,
                                    message=_tl(recharge_product.disabled_message, current_language))

    result = None
    tg_user = await user_service.get_tg_user_by_id(user_id)
    if tg_user:
        white_channel = await recharge_channel_service.get_user_whitelist_channel(tg_user.tg_id)
        if white_channel:
            result = await RechargeChannelManager.process_with_whitelist(user_id, req, client_ip, white_channel.channel.value)

    if result is None:
        route_strategy = await recharge_channel_service.get_current_mode()
        if route_strategy == RechargeRouteStrategy.MANUAL:
            result = await RechargeChannelManager.process_with_manual_routing(user_id, req, client_ip)
        else:
            result = await RechargeChannelManager.process_with_smart_routing(user_id, req, client_ip)

    if not result.success:
        return RechargeHandleResult(message=_tl(result.message, current_language), status_code=result.status_code or 400)

    return RechargeHandleResult(success=True, status_code=200, message=result.message, pay_url=result.pay_url)

async def handle_recharge_request(user_id: int, req: RechargeRequest, client_ip: str,
                                 channel_config: RechargeChannelConfig, current_language: str) -> JSONResponse:
    result = await handle_recharge_request_base(user_id, req, client_ip, channel_config, current_language)
    if result.success:
        return JSONResponse(content={
            'message': result.message,
            'pay_url': result.pay_url,
        })
    else:
        return response_util.error(result.status_code, _tl(result.message, current_language))
