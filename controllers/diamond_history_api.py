from datetime import UTC, datetime, timedelta
import logging
from fastapi import APIRouter, Depends, Header
from pydantic import BaseModel
from common.common_constant import Language
from controllers.user_check import get_current_user
from persistence.models.models import Product
from services import product_service
from services.account_service import AccountService
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from services.user import user_benefit_service
from utils import response_util
from utils.translate_util import _tl


diamond_hisotry_router = APIRouter()

log = logging.getLogger(__name__)


class ConsumptionInfo(BaseModel):
    created_at: float = 0
    type: str = "其他"
    role_id: int = 0
    role_name: str = ""
    chat_desc: str = ""
    amount: int = 0


# 查询用户的消费记录(最近3个月,起始时间从2025年1月9日开始)
@diamond_hisotry_router.get("/user/diamond_consumption_history")
async def get_diamond_consumption_history(
    offset: int = 0,
    limit: int = 10,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    limit = 100 if limit > 100 else (10 if limit <= 0 else limit)
    offset = 0 if offset < 0 else offset
    # 今天的日期
    today = datetime.now(UTC)
    # 3个月以前
    three_month_ago = today - timedelta(days=90)
    # 默认的起始时间(不支持查询之前的记录)
    default_start_date_time = datetime(2025, 1, 9, tzinfo=UTC)
    if three_month_ago < default_start_date_time:
        three_month_ago = default_start_date_time
    start_date_time = datetime(
        three_month_ago.year, three_month_ago.month, three_month_ago.day
    )

    pay_orders, total = await AccountService.get_pay_orders_paging(
        user_id, offset, limit, start_date_time, today.replace(tzinfo=None)
    )
    product_map = await product_service.map_by_product_id()
    product_short_name_map = {
        str(product.product_id): _tl(
            product.short_name, current_language, Product.__name__
        )
        for product in product_map.values()
    }
    product_name_map = {
        str(product.product_id): _tl(product.name, current_language, Product.__name__)
        for product in product_map.values()
    }
    role_ids = list(set([pay_order.role_id for pay_order in pay_orders]))
    role_configs = await role_loader_service.load_translated_roles(role_ids, current_language,"")
    card_name_map = {role_config.id: role_config.card_name for role_config in role_configs}
    role_name_map = {role_config.id: role_config.role_name for role_config in role_configs}

    # card_name_map = await role_loader_service.map_card_name_by_ids(role_ids)
    # role_name_map = await role_loader_service.map_role_name_by_ids(role_ids)

    history_list = []
    for pay_order in pay_orders:
        history = ConsumptionInfo()
        history.created_at = pay_order.created_at.timestamp()
        history.amount = pay_order.total_fee
        product = product_map.get(str(pay_order.product_id))
        if product:
            history.type = product_name_map.get(str(pay_order.product_id), "")
            history.chat_desc = product_short_name_map.get(
                str(pay_order.product_id), ""
            )
        history.role_id = pay_order.role_id
        role_name = role_name_map.get(pay_order.role_id, "")
        card_name = card_name_map.get(pay_order.role_id, "")
        history.role_name = card_name if card_name else role_name
        history_list.append(history)
    return response_util.ok({"list": history_list, "total": total})


# 查询用户的消费记录(最近3个月,起始时间从2025年1月9日开始)
@diamond_hisotry_router.get("/user/benefit_consumption_history")
async def get_benefit_consumption_history(
    offset: int = 0,
    limit: int = 10,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    limit = 100 if limit > 100 else (10 if limit <= 0 else limit)
    # 今天的日期
    today = datetime.now(UTC)
    # 3个月以前
    three_month_ago = today - timedelta(days=90)
    # 默认的起始时间(不支持查询之前的记录)
    start_date_time = datetime(
        three_month_ago.year, three_month_ago.month, three_month_ago.day
    )
    total, records = await user_benefit_service.list_consume(
        user_id, offset, limit, start_date_time, today.replace(tzinfo=None)
    )
    product_map = await product_service.map_by_product_id()
    role_ids = list(set([record.role_id for record in records]))
    role_configs = await role_loader_service.load_translated_roles(role_ids, current_language,"")
    card_name_map = {role_config.id: role_config.card_name for role_config in role_configs}
    role_name_map = {role_config.id: role_config.role_name for role_config in role_configs}
    # card_name_map = await role_loader_service.map_card_name_by_ids(role_ids)
    # role_name_map = await role_loader_service.map_role_name_by_ids(role_ids)

    history_list = []
    for record in records:
        history = ConsumptionInfo()
        history.created_at = record.created_at.timestamp()
        history.amount = 1
        product = product_map.get(str(record.product_id))
        if product:
            history.type = _tl(product.name, current_language, Product.__name__)
            history.chat_desc = _tl(
                product.short_name, current_language, Product.__name__
            )
        role_name = role_name_map.get(record.role_id, "")
        card_name = card_name_map.get(record.role_id, "")
        history.role_id = record.role_id
        history.role_name = card_name if card_name else role_name
        history_list.append(history)
    return response_util.ok({"list": history_list, "total": total})


# 查询用户的钻石过期记录(最近3个月,起始时间从2025年1月9日开始)
@diamond_hisotry_router.get("/user/diamond_expired_history")
async def get_diamond_expired_history(
    offset: int = 0, limit: int = 10, user_id: int = Depends(get_current_user)
):
    if limit > 100:
        limit = 100
    if limit <= 0:
        limit = 10
    if offset < 0:
        offset = 0
    # 今天的日期
    today = datetime.now(UTC)
    # 3个月以前
    three_month_ago = today - timedelta(days=90)
    # 默认的起始时间(不支持查询之前的记录)
    default_start_date_time = datetime(2025, 1, 9, tzinfo=UTC)
    if three_month_ago < default_start_date_time:
        three_month_ago = default_start_date_time

    start_date_time = datetime(
        three_month_ago.year, three_month_ago.month, three_month_ago.day
    )

    expired_diamonds, total = await AccountService.get_expired_diamonds_history_paging(
        user_id, offset, limit, start_date_time, today.replace(tzinfo=None)
    )

    history_list = []
    for expired_diamond in expired_diamonds:
        history = ConsumptionInfo()
        history.created_at = expired_diamond.expires_at.timestamp()
        history.amount = expired_diamond.balance
        history.type = "赠送金币过期"
        history_list.append(history)
    return response_util.ok({"list": history_list, "total": total})
