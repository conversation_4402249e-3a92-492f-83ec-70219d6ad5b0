import logging
import os
import hashlib
from typing import Annotated
import urllib.parse
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import APIRouter, Depends, Header, Request
from pydantic import BaseModel
from common.common_constant import Language
from common.copywriting_templates import over_limit_tip
from controllers.recharge_handler import handle_recharge_request
from controllers.user_check import get_current_user
from persistence.models.models import RechargeChannelConfig, RechargeProduct
from services import andada_recharge_service, bot_services, re_purchase_service, recharge_service, star_payment_service, tg_config_service, user_growth_service, recharge_channel_service
from services.andada_recharge_service import andada_recharge_app_key
from services.out_recharge_common import RechargeRequest

from controllers.user_check import user_service
from utils import response_util
from utils.bucket_factory import UserBasedBucketFactory
from pyrate_limiter import Limiter, Duration, Rate
from persistence.redis_client import redis_client
from utils.translate_util import _tl

recharge_order_rate_limit = Rate(5, Duration.MINUTE)
bucket_factory = UserBasedBucketFactory('recharge_order_bucket', redis_client, [recharge_order_rate_limit])
limiter = Limiter(bucket_factory)

cwd = os.path.dirname(os.path.realpath(__file__))

with open(os.path.join(cwd, 'success.html'), 'r') as f:
    success_html = f.read()

andada_recharge_router = APIRouter()

class AndadaNotify(BaseModel):
    pid: int
    trade_no: str
    out_trade_no: str
    type: str
    name: str
    money: str
    trade_status: str
    sign: str
    sign_type: str
    params: str = None

    def verify_sign(self, app_key: str) -> bool:
        params = self.model_dump(exclude_unset=True, exclude={'sign', 'sign_type'})
        params = [(k, str(v)) for k, v in params.items() if v is not None and v != '']
        sorted_params = sorted(params, key=lambda x: x[0])
        sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + app_key
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign.lower() == self.sign.strip()

async def handle_result(notify_result: AndadaNotify):
    if not notify_result.verify_sign(andada_recharge_app_key):
        return 'verify sign failed'
    if notify_result.trade_status != 'TRADE_SUCCESS':
        return 'FAIL'
    order = await andada_recharge_service.pay_success(notify_result.out_trade_no, notify_result.trade_no, notify_result.model_dump_json())
    user = await user_service.get_user_by_id(order.user_id)
    await user_growth_service.add_fc_reward(user)
    await re_purchase_service.after_recharge(user, order)
    return 'success'

@andada_recharge_router.get('/andada_recharge/result')
async def andada_recharge_return(req: Request):
    return HTMLResponse(content=success_html)

@andada_recharge_router.get('/andada_recharge/notify')
async def andada_recharge_notify(req: Request):
    data = req.query_params
    logging.info(f'andada_recharge_notify: {data}')
    notify_result = AndadaNotify(**data)
    return await handle_result(notify_result)

@andada_recharge_router.post('/andada_recharge/notify')
async def andada_recharge_notify_p(req: Request):
    data = await req.form()
    logging.info(f'andada_recharge_notify: {data}')
    notify_result = AndadaNotify(**data)
    return await handle_result(notify_result)

async def star_recharge(req: RechargeRequest, tg_bot_id: str, user_id: int):
    recent_orders = await recharge_service.get_recent_init_orders(user_id)
    if len(recent_orders) >= 5:
        return response_util.error(429, over_limit_tip)
    bot_config = await tg_config_service.get_bot_config_by_tma_bot_id(tg_bot_id)
    if bot_config is None:
        return response_util.error(404, "Bot not found")
    bot = bot_services.get_bot_by_bot_id(bot_config.bot_id)
    if bot is None:
        return response_util.error(404, "Bot not found")
    tg_user: TelegramUser = await user_service.get_tg_info_by_user_id(user_id) # type: ignore

    star_payment, prices = await star_payment_service.create_star_payment_order(
        user_id, tg_user.tg_id, req.recharge_id, bot_config)
    if star_payment is None:
        return response_util.error(422, "Failed to create star payment order")

    link = await bot.create_invoice_link(
        '幻梦AI', '幻梦AI充值套餐', str(star_payment.invoice_id), 'XTR', prices)

    return response_util.success({'message': 'success', 'pay_url': link})

async def handle_stripe_payment(user_id: int, recharge_product: RechargeProduct, return_host: str):
    checkout_url = await recharge_service.create_checkout_session(user_id, recharge_product, return_host)
    return JSONResponse(content={'message': 'success', 'pay_url': checkout_url})

@andada_recharge_router.post('/andada_recharge/recharge')
async def andada_recharge_recharge(req: RechargeRequest, request: Request, 
                                   tg_bot_id: Annotated[str, Header()] = None,
                                   user_id=Depends(get_current_user),
                                   current_language: str = Header(default=Language.ZH.value)) -> JSONResponse:
    recent_orders = await recharge_service.get_recent_init_orders(user_id)
    if len(recent_orders) >= 3:
        return response_util.error(429, _tl(over_limit_tip, current_language))

    user = await user_service.get_user_by_id(user_id)
    await user_growth_service.notify_recharge_channels(user)

    recharge_product = await recharge_service.get_recharge_product(req.recharge_id)
    if recharge_product is None:
        return response_util.error(404, "找不到充值套餐")

    if req.type == 'star':
        return await star_recharge(req, tg_bot_id, user_id)

    if req.type == 'stripe':
        refer = request.headers.get('Referer')
        if refer:
            r = urllib.parse.urlparse(refer)
            return_host = f'{r.scheme}://{r.netloc}'
        else:
            return_host = os.environ['WEB_APP_URL'].rstrip('/')
        return await handle_stripe_payment(user_id, recharge_product, return_host)

    if req.type == 'wxpay':
        req.type = 'wechat'

    bot_id = -1
    if tg_bot_id:
        bot_config = await tg_config_service.get_bot_config_by_tma_bot_id(tg_bot_id)
        if bot_config is not None:
            bot_id = bot_config.bot_id

    req.bot_id = bot_id
    channel_config = await recharge_channel_service.get_by_product_id(req.recharge_id)
    if channel_config is None:
        channel_config = RechargeChannelConfig.default_config()
        
    return await handle_recharge_request(user_id, req, request.client.host, channel_config, current_language)