import asyncio
import base64
from datetime import datetime
from itertools import groupby
import itertools
import logging
import re
import os, sys, json
from time import sleep
from typing import Annotated, Iterator
import uuid
from fastapi.encoders import jsonable_encoder
from langchain_core.messages import BaseMessageChunk
from fastapi import Form, Header, Request, Depends, APIRouter, Response
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
from fastapi.responses import JSONResponse
from aioitertools import itertools as aioitertools
from ai import new_chat_bot
from ai import voice
from ai.voice import get_voice, upload_voice
from common.models.chat_model import ChatHistoryStatus, ChatHistoryType, HistoryRequest
from common.models.chat_request import (
    PresetConfig,
)
from common.role_model import RoleDataConfig, RoleRes
from controllers import user_check
from controllers.user_check import get_current_user, user_service
from persistence import chat_history_dao
from persistence.chat_history import chat_history_persistence
from persistence.models.models import RoleOperationConfig
from persistence.presets import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, presetsPersistence
from common.common_constant import (
    ChatModeType,
    Language,
)
from presets import role_fill_preset
from services import (
    role_access_service,
)
from services.account_service import AccountService
from services.chat import chat_message_service
from services.role import role_loader_service
from services.role_config_service import RoleConfigService
from dotenv import load_dotenv
from utils import (
    json_util,
    message_utils,
    response_util,
    role_util,
)
from utils.translate_util import _t

load_dotenv()

chat_router = APIRouter()

log = logging.getLogger(__name__)

home_dir = os.path.expanduser("~")
failed_dir = os.path.join(home_dir, "asr_failed")

UNAUTHORIZED = JSONResponse(status_code=401, content={"message": "Unauthorized"})


@chat_router.post("/presets_v2")
async def presets(preset_config: PresetConfig):
    await presetsPersistence.save_preset_v2(
        preset_config.presets,
        preset_config.model,
        preset_config.nsfw,
        preset_config.scenario,
    )
    return "OK"


# fancyou依然在使用中
@chat_router.get("/history")
async def history(
    role_id: int,
    conversation_id: str,
    new_start: int = 0,
    user_id: int = Depends(get_current_user),
    current_language: str = Header(default=Language.ZH.value),
):
    history_req = HistoryRequest(
        mode_type=ChatModeType.SINGLE.value,
        group_id=0,
        role_id=role_id,
        conversation_id=conversation_id,
        new_start=new_start,
        language=current_language,
    )
    log.info(f"history user_id:{user_id},request: {history_req}")
    if not history_req.verify_param():
        return response_util.json_param_error("param error")
    user = await user_service.get_user_by_id(user_id)
    await role_access_service.verify_history_request(history_req, user)
    if history_req.new_start == 0 and not history_req.conversation_id:
        history_req.conversation_id = await chat_message_service.latest_cov_id(
            user_id, history_req
        )
    if not history_req.conversation_id or new_start == 1:
        history_req = await chat_message_service.init_first_history_message(
            user, history_req
        )
    return await chat_message_service.list_user_history(user, history_req)


@chat_router.get("/tts")
async def tts(message_id: str, version: int, user_id: int = Depends(get_current_user)):
    user = await user_service.get_user_by_id(user_id)
    voice_url = await voice.generate_voice_new(user, message_id, version)
    return JSONResponse(content={"voice_url": voice_url, "message_id": message_id})
