from datetime import timedelta
import logging
from fastapi.encoders import jsonable_encoder
from fastapi import Depends, APIRouter, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from common.entity import GiftAward
from common.common_constant import (
    ApiSource,
    BotReplace,
    ChatPlatform,
    Language,
    PopupPosition,
    PopupType,
)
from controllers.request_depends import (
    dep_api_source,
    dep_language,
    limit_requests_by_tg,
)
from controllers.user_check import get_chat_platform, get_current_user
from common.operation_model import Popup, PopupResponse
from persistence import chat_history_dao
from persistence.models.models import PopupRecord, User, UserTaskRecord
from services import bot_message_service, gift_award_service
from services.user_service import UserService
from utils import response_util
from utils.translate_util import _t
from controllers.telegram_webhook import bot
from services import operation_service

gifted_tips = f"""福利来了！赶紧进群领取吧，更有免费金币每天拿！

幻梦AI官方福利群（每天薅羊毛送金币领福利）：
https://t.me/{BotReplace.MAIN_GROUP.value}

幻梦AI美女橱窗（第一时间更新美女帅哥）：

https://t.me/{BotReplace.MAIN_CHANNEL.value}"""


class GiftRewardResponse(BaseModel):
    gift_id: str
    isGift: bool
    title: str
    desc: str

    @staticmethod
    def of_model(gift: GiftAward, gifted: bool = False) -> "GiftRewardResponse":
        return GiftRewardResponse(
            gift_id=gift.id, isGift=gifted, title=gift.title, desc=gift.desc
        )


class GiftClaimRequest(BaseModel):
    gift_id: str


gift_router = APIRouter()

user_service = UserService()


# TODO support more gift types
@gift_router.get("/gift")
async def get_gift(
    user_id: int = Depends(get_current_user),
    language: str = Depends(dep_language),
):
    user: User = await user_service.get_user_by_id(user_id)
    gift = await gift_award_service.get_award_by_user_gift(user_id, "G1")
    if gift:
        return JSONResponse(content=[])
    gift = await gift_award_service.add_award_by_user_gift(
        user_id, gift_award_service.get_gift_award(user.register_source)
    )
    award = gift_award_service.get_gift_award(user.register_source)
    gift_reward = GiftRewardResponse.of_model(award, False)
    gift_reward.title = _t(gift_reward.title, language)
    gift_reward.desc = _t(gift_reward.desc, language)
    user_tg = await user_service.get_tg_info_by_user_id(user_id)
    if not user_tg or language == Language.EN.value:
        return JSONResponse(content=jsonable_encoder([gift_reward]))
    try:
        tips = await bot_message_service.format_content_replace(gifted_tips)
        await bot.send_message(user_tg.tg_id, tips)
    except Exception as e:
        logging.warning(f"send bot message failed, {e}")
    return JSONResponse(content=jsonable_encoder([gift_reward]))


@gift_router.post("/gift/claim")
async def claim_gift(
    request: GiftClaimRequest,
    user_id: int = Depends(get_current_user),
):
    user: User = await user_service.get_user_by_id(user_id)
    award = gift_award_service.get_gift_award(user.register_source)
    gift = await gift_award_service.get_award_by_user_gift(user_id, "G1")
    if gift is not None:
        return JSONResponse(
            content=jsonable_encoder(GiftRewardResponse.of_model(award, True))
        )
    gift = await gift_award_service.add_award_by_user_gift(user_id, award)
    return JSONResponse(
        content=jsonable_encoder(GiftRewardResponse.of_model(award, True))
    )


@gift_router.get("/operation/popup")
async def pop_up(
    request: Request,
    popup_type: PopupType,
    popup_position: str = "",
    user_id: int = Depends(get_current_user),
    current_language: str = Depends(dep_language),
    chat_platform: ChatPlatform = Depends(get_chat_platform),
    api_resource: str = Depends(dep_api_source),
):
    user = await user_service.get_user_by_id(user_id)
    if popup_type == PopupType.NEW_USER_BENEFIT:
        popup = await operation_service.new_user_benefit_popup(user, current_language)
        return PopupResponse.of_model(popup=popup)
    if popup_type != PopupType.COMMON:
        popup = await operation_service.pull_popup_message(user_id, popup_type)
        return PopupResponse.of_model(popup=popup)
    if api_resource == ApiSource.OVERSEAS_WEB:
        return PopupResponse.of_model(popup=None)
    popup = await operation_service.pull_common_popup_message_by_language(
        user, popup_type, chat_platform, PopupPosition(popup_position), current_language
    )
    return PopupResponse.of_model(popup=Popup.from_model(popup_type, popup))
