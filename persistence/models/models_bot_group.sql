

# 2025-04-14 增加一个字段来表示优先级
ALTER TABLE tavern.bg_auto_reply_rules ADD pripority INT DEFAULT 1 NOT NULL COMMENT '值越大优先级越高';


# 2024-12-17 增加一个字段来表示机器人类型
ALTER TABLE bg_bot_group_config
ADD COLUMN bot_type VARCHAR(50) DEFAULT 'group_help';


# bg的入群检测注册
INSERT INTO bg_feature_config (name, group_id, key_name, config, enabled, start_time, end_time, description)
VALUES (
    '入群检测注册',
    0,
    'group_join_check_reg',
    '{}',
    TRUE,
    NULL,
    NULL,
    '入群检测功能开关'
);

#本地测试群
INSERT INTO tavern.bg_feature_config (name, group_id, key_name, config, enabled, start_time, end_time, description)
VALUES (
    '入群检测注册',
    -1002411997003,
    'group_join_check_reg',
    '{}',
    TRUE,
    NULL,
    NULL,
    '入群检测功能开关'
);

# 线上群