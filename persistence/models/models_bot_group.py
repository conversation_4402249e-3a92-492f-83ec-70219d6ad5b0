from datetime import datetime, timezone
from enum import IntEnum
from tortoise.models import Model
from tortoise import fields

def default_time():
    return datetime(1970, 1, 1, tzinfo=timezone.utc)



class BaseIdModel(Model):
    id = fields.BigIntField(pk=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    class Meta:
        abstract = True

class BastTimeModel(Model):
    created_at = fields.DatetimeField(auto_now_add=True,index=True)
    updated_at = fields.DatetimeField(auto_now=True,index=True)
    class Meta:
        abstract = True



class BotGroupConfig(BaseIdModel):
    bot_name = fields.CharField(max_length=255, unique=True, description="Bot name,用于webhook")
    bot_token = fields.CharField(max_length=255, index =True,description="Bot token")
    bot_group_id =fields.BigIntField(index=True,description="bot群组id")
    g_white_users = fields.JSONField(default=list,description="群全局白名单用户")
    deleted = fields.BooleanField(default=False)
    bot_type = fields.CharField(max_length=50, default='group_help')


    class Meta:
        table = "bg_bot_group_config"

class BotGroupMap(BastTimeModel):
    bot_id = fields.BigIntField(index=True, description="Bot id ")
    bot_token = fields.CharField(max_length=255, index =True,description="Bot token")
    group_id =fields.BigIntField(index=True,description="bot群组id")
    g_white_users = fields.JSONField(default=list,description="群全局白名单用户")
    deleted = fields.BooleanField(default=False)
    bot_type = fields.CharField(max_length=50, default='group_help')

    class Meta:
        table = "bg_bot_group_map"
        table_description = "bot和群组的映射表"
        unique_together = (('bot_id', 'group_id'),)


class SpamProtectionRule(BastTimeModel):
    rule_id = fields.BigIntField(pk=True)
    rubbish_type = fields.IntField(description="垃圾类型")
    rubbish_desc = fields.CharField(max_length=128,description="反垃圾类型说明")
    rubbish_condition = fields.TextField(description="垃圾条件和type有关")
    rubbish_action = fields.JSONField(description="处理动作（JSON格式）")
    rubbish_sort = fields.IntField(default=100,description="排序,值越小越靠前")
    is_enable = fields.BooleanField(default=True,description="是否启用")
    is_master = fields.BooleanField(default=False,description="是否为主规则")
    group_id = fields.BigIntField(index=True,description="Telegram群组ID")
    delete_memo = fields.IntField(max_length=10, description="删除提示，0表示没有提示")
    check_user_type = fields.CharField(max_length=20,default="all", description="检查用户类型")
    white_link_list = fields.JSONField(default=list,description="白名单link")
    white_user_list = fields.JSONField(default=list,description="白名单用户列表")

    class Meta:
        table = "bg_spam_protection_rules"
        table_description = "垃圾信息保护规则表"

    def __str__(self):
        return f"SpamProtectionRule {self.rule_id}: {self.rubbish_type}, {self.rubbish_desc}, {self.rubbish_condition}, {self.rubbish_action}, {self.rubbish_sort}, {self.is_enable}, {self.is_master}, {self.group_id}, {self.delete_memo}, {self.check_user_type}, {self.white_user_list}"


class UserSpamBehavior(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    user_id = fields.BigIntField(index=True, description="用户tgID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    rubbish_type = fields.IntField(description="垃圾类型,1链接检测，2昵称检测,6违禁词词检测,100起自动回复相关的，101是发卡作者,200是图片多媒体检测,201是photo&stick、animation范图片检测")
    rubbbish_cnt = fields.IntField(default=0, description="违禁类型的次数")

    class Meta:
        table = "bg_user_spam_behaviors"
        table_description = "用户违规行为表"
        unique_together = (('user_id', 'group_id', 'rubbish_type'),)
    def __str__(self):
        return f"UserSpamBehavior {self.id}: {self.user_id}, {self.group_id}, {self.rubbish_type}, {self.rubbbish_cnt}"

class UserAutoMessageHistory(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    user_id = fields.BigIntField(index=True, description="用户ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    topic_id = fields.BigIntField(index=True, description="话题ID")
    message_id = fields.BigIntField(index=True, description="消息ID")
    replay_type = fields.IntField(description="回复类型id")

    class Meta:
        table = "bg_user_auto_message_history"
        table_description = "用户自动消息表"
    def __str__(self):
        return f"UserAutoMessageHistory {self.id}: {self.user_id}, {self.group_id}, {self.replay_type}"

class AutoReplyKeywordType:
    # 关键词类型
    EQUALS = 1
    CONTAINS = 2
    # REGEX = 3

    # 关键词类型扩展,tg username和tg id 匹配 20～29
    USER_NAME_EQUALS = 21
    TG_ID_EQUALS = 22
class AutoReplyRule(BastTimeModel):
    rule_id = fields.BigIntField(pk=True)
    group_id = fields.BigIntField(index=True,description="群组号")
    keyword_type = fields.IntField(description="关键词类型")
    keyword_list = fields.TextField(description="关键词列表:word1,word2")
    msg_type = fields.IntField(description="消息类型")
    msg_content = fields.JSONField(description="消息内容（JSON格式）")
    del_bot_msg_delay = fields.IntField(default=0, description="删除机器人消息延迟，单位秒，默认0表示不删除")
    del_src_msg_delay = fields.IntField(default=0, description="删除源消息延迟，单位秒，默认0表示不删除")
    rule_desc = fields.CharField(max_length=1024, default="", description="备注")
    skip_admin = fields.BooleanField(default=True, description="跳过管理员")
    is_quote = fields.BooleanField(default=False, description="是否引用源消息")
    is_enable = fields.BooleanField(default=True, description="是否启用")
    pripority = fields.IntField(default=1,index =True, description="优先级，值越大越高")

    class Meta:
        table = "bg_auto_reply_rules"
        table_description = "自动回复规则表"
    def __str__(self):
        return f"AutoReplyRule {self.rule_id}: {self.group_id},, {self.keyword_type}, {self.keyword_list}, {self.msg_type}, {self.msg_content}"
    



class BotWelcomeConfigModel(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(index=True, description="群组号")
    message_thread_id = fields.IntField(default=0, description="消息线程ID")
    msg_type = fields.IntField(description="消息类型")
    msg_content = fields.JSONField(description="消息内容（JSON格式）")
    delete_memo = fields.IntField(default=0, description="删除消息延迟，单位秒，默认0表示不删除")
    is_enable = fields.BooleanField(default=True, description="是否启用")

    class Meta:
        table = "bot_welcome_config"
        table_description = "欢迎配置表"

    def __str__(self):
        return f"BotWelcomeConfigModel {self.id}: {self.group_id}, {self.msg_type}, {self.msg_content}, {self.delete_memo}, {self.is_enable}"
    

class BotMessageTypeEnum(IntEnum):
    MESSAGE = 100
    TEXT = 101
    PHOTO = 102
    AUDIO = 103
    DOCUMENT = 104
    VIDEO = 105
    VOICE = 106
    STICKER = 107
    ANIMATION = 108
    
    MEMBER_JOIN = 201
    MEMBER_LEVE = 202

class TgGroupMessage(BastTimeModel):
    id = fields.BigIntField(pk=True)
    update_id = fields.BigIntField(unique=True, description="update id ")
    message_id = fields.BigIntField(index=True, description="消息ID")
    user_id = fields.BigIntField(index=True, description="用户tgID")
    bot_id = fields.BigIntField(index=True, description="机器人bot id")
    group_id = fields.BigIntField(index=True, description="群组ID")
    group_user_name = fields.CharField(max_length=255,default="", description="群组用户名称")
    message_thread_id = fields.BigIntField(index=True,default=0, description="消息线程ID")
    full_name = fields.CharField(max_length=512, null=True)
    user_name = fields.CharField(index=True,max_length=255, null=True)
    message_type = fields.IntEnumField(BotMessageTypeEnum, description="消息类型")
    message_text = fields.TextField(null=True,description="消息text内容")
    raw_data = fields.TextField(description="原始数据")
    
    is_deleted = fields.BooleanField(default=False, description="是否删除")

    class Meta:
        table = "bg_group_msg"

        

class TgGroupUserMap(BastTimeModel):
    id = fields.BigIntField(pk=True)
    tg_id = fields.BigIntField(index=True, description="用户tgID")
    user_id = fields.BigIntField(index=True, default =0,description="用户uid")
    tg_nickname = fields.CharField(max_length=512,null=True,description="用户tg昵称")
    tg_username = fields.CharField(max_length=255,null=True,description="用户tg用户名")
    f_bot_id = fields.BigIntField(index=True, description="来源机器人bot_id")
    f_group_id = fields.BigIntField(index=True, description="来源群组ID")
    to_group_id = fields.BigIntField(index=True,default=0,description="目的转发群组ID")
    to_topic_id = fields.BigIntField(index=True,default=0, description="目的转发topic的message_thread_id")
    to_message_id = fields.BigIntField(index=True,default=0, description="消息ID,代表用户的卡片信息")
    is_deleted = fields.BooleanField(default=False, description="是否删除")

    class Meta:
        table = "bg_group_user_map"
        table_description = "群组的消息id和用户对应表"
        unique_together = (('tg_id', 'f_group_id'),)


class TgGroupMsgSentMap(BastTimeModel):
    id = fields.BigIntField(pk=True)
    tg_id = fields.BigIntField(index=True, description="用户tgID")
    user_id = fields.BigIntField(index=True, default =0,description="用户uid")
    to_bot_id = fields.BigIntField(index=True, description="ff器人bot_id")
    to_group_id = fields.BigIntField(index=True,default=0,description="目的转发群组ID")
    to_topic_id = fields.BigIntField(index=True,default=0, description="目的转发topic的message_thread_id")
    to_message_id = fields.BigIntField(index=True,default=0, description="消息ID,代表用户的卡片信息")
    is_deleted = fields.BooleanField(default=False, description="是否删除")

    class Meta:
        table = "bg_group_use_msg_sent_map"
        table_description = "群组发送的消息id和用户对应表"
        unique_together = (('to_topic_id', 'to_message_id'),)


class OpLog(BastTimeModel):
    id = fields.IntField(pk=True)
    tg_id = fields.BigIntField(index=True, default =0,description="用户tgID")
    group_id = fields.BigIntField(index=True,default =0, description="群组ID")
    op_type = fields.IntField(default=0, description="操作类型0,表示bot操作，1表示群里用户操作")
    op_action = fields.TextField(description="操作内容")
    op_action_type = fields.CharField(max_length=255,description="操作类型")
    action_ack = fields.IntField(default=0,description="操作回执")
    class Meta:
        table = "bg_op_log"

    def __str__(self):
        return f"<OpLog id={self.id} action={self.op_action} created_at={self.created_at}>"

class HoursUserMessageStats(BastTimeModel):
    id = fields.BigIntField(pk=True)
    user_id = fields.BigIntField(index=True, description="用户tgID")
    tg_nick_name = fields.CharField(max_length=512,default="",null =True, description="用户tg昵称")
    tg_username = fields.CharField(max_length=512,default="",null =True, description="用户tg的username")
    group_id = fields.BigIntField(index=True, description="群组ID")
    message_count = fields.IntField(default=0, description="消息数量")
    st_hour = fields.IntField(index=True, description="统计小时开始时间0~23")
    st_date = fields.DateField(index=True, description="统计日期,格式为YYYY-MM-DD")
    class Meta:
        table = "bg_hours_user_msg_stats"
        table_description = "每日用户消息统计表"
        unique_together = (('user_id', 'group_id','st_hour', 'st_date'),)
    def __str__(self):
        return f"HoursUserMessageStats {self.id}: {self.user_id}, {self.group_id}, {self.message_count}, {self.st_date}, {self.st_hour}"
class DailyUserMessageStats(BastTimeModel):
    id = fields.BigIntField(pk=True)
    user_id = fields.BigIntField(index=True, description="用户tgID")
    tg_nick_name = fields.CharField(max_length=512,default="",null =True, description="用户tg昵称")
    tg_username = fields.CharField(max_length=512,default="", null =True, description="用户tg的username")
    group_id = fields.BigIntField(index=True, description="群组ID")
    message_count = fields.IntField(default=0, description="消息数量")
    user_cnt = fields.IntField(default=1, description="用户数量")
    date = fields.DateField(index=True, description="统计日期")
    class Meta:
        table = "bg_daily_user_msg_stats"
        table_description = "每日用户消息统计表"
        unique_together = (('user_id', 'group_id', 'date'),)
    def __str__(self):
        return f"DailyUserMessageStats {self.id}: {self.user_id}, {self.group_id}, {self.message_count}, {self.date}"

class DailyInviteUserStats(BastTimeModel):
    id = fields.BigIntField(pk=True)
    user_id = fields.BigIntField(index=True, description="用户ID")
    tg_nick_name = fields.CharField(max_length=255, description="用户昵称")
    tg_username = fields.CharField(max_length=255, description="用户username")
    invite_count = fields.IntField(default=0, description="邀请数量")
    date = fields.DateField(index=True, description="统计日期")
    class Meta:
        table = "bg_daily_invite_user_stats"
        table_description = "每日用户邀请统计表"
        unique_together = (('user_id',  'date'),)
    def __str__(self):
        return f"DailyInviteUserStats {self.id}: {self.user_id} {self.invite_count}, {self.date}"

class DailyShareUserStats(BastTimeModel):
    id = fields.BigIntField(pk=True)
    user_id = fields.BigIntField(index=True, description="用户ID")
    tg_nick_name = fields.CharField(max_length=255, description="用户昵称")
    tg_username = fields.CharField(max_length=255, description="用户username")
    share_count = fields.IntField(default=0, description="分享数量")
    date = fields.DateField(index=True, description="统计日期")
    class Meta:
        table = "bg_daily_share_user_stats"
        table_description = "每日用户分享统计表"
        unique_together = (('user_id',  'date'),)
    def __str__(self):
        return f"DailyShareUserStats {self.id}: {self.user_id} {self.share_count}, {self.date}"
class WeeklyUserMessageStats(BastTimeModel):
    id = fields.BigIntField(pk=True)
    user_id = fields.BigIntField(index=True, description="用户ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    message_count = fields.IntField(default=0, description="消息数量")
    week_start_date = fields.DateField(index=True, description="统计周开始日期")
    week_end_date = fields.DateField(index=True, description="统计周结束日期")
    class Meta:
        table = "bg_weekly_user_message_stats"
        table_description = "每周用户消息统计表"
        unique_together = (('user_id', 'group_id', 'week_start_date'))
    def __str__(self):
        return f"WeeklyUserMessageStats {self.id}: {self.user_id}, {self.group_id}, {self.message_count}, {self.week_start_date} - {self.week_end_date}"



class AutoSendMessageConfig(BastTimeModel):
    
    id = fields.BigIntField(pk=True, description="主键ID")
    title = fields.CharField(max_length=255,default="发送消息", description="任务标题")
    to_group_id = fields.BigIntField(index=True, description="群组ID")
    to_thread_id = fields.BigIntField(default = 0,index=True, description="消息线程ID")
    msg_type = fields.IntField(default =0,description="消息类型,0表示文本消息，1表示图片消息")
    content = fields.TextField(default ="",description="消息原始内容")
    msg_content = fields.JSONField(description="消息内容")
    interval = fields.IntField(default=0, description="间隔时间，单位s")
    cron_expression = fields.CharField(max_length=255, default="0 0 0 * * *", description="cron表达式")
    start_time = fields.DatetimeField(description="消息发送的开始时间")
    end_time = fields.DatetimeField(description="消息发送的结束时间")
    send_time = fields.DatetimeField(description="一次性任务的消息发送的时间")
    repeat_times = fields.IntField(default=-1, description="消息重复发送次数, -1表示无限次")
    job_type = fields.CharField(max_length=255, description="任务类型，Date,Interval,Threshold")
    message_threshold = fields.IntField(default=0, description="消息类型Threshold时，设置发送的数量阈值")
    delete_previous = fields.BooleanField(default=True, description="发送后是否自动删除上一条消息")
    msg_comment = fields.CharField(max_length=255, default="", description="消息备注")
    enabled = fields.BooleanField(default=False, description="是否启用")


    class Meta:
        table = "bg_auto_send_msg_cfg"
        table_description = "自动发送消息配置表"

    def __str__(self):
        return f"AutoSendMessageConfig {self.id}: {self.msg_content}, {self.cron_expression} minutes, {self.start_time} - {self.end_time}, delete_previous={self.delete_previous}"

class AutoSendMsgHistory(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    auto_send_id = fields.BigIntField(index=True, description="自动发送消息配置ID")
    group_id = fields.BigIntField(index=True,default=0,description="群组ID")
    message_id = fields.BigIntField(index=True,default=0, description="消息ID")
    message = fields.TextField(default ="",description="要发送的文本消息")
    send_result = fields.TextField(default ="",description="发送结果")
    send_ack = fields.BooleanField(default=False, description="发送成功标志")
    class Meta:
        table = "bg_auto_send_msg_history"
        table_description = "自动发送消息历史表"

    def __str__(self):
        return f"AutoSendMsgHistory {self.id}: {self.auto_send_id}, {self.group_id}, {self.message_id}, {self.group_id}"

class AutoSendGroupMessageCount(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    auto_send_id = fields.BigIntField(index=True, description="自动发送消息配置ID")
    message_thread_id = fields.BigIntField(index=True,default=0, description="消息线程ID")
    message_count = fields.IntField(default=0, description="消息数量")
    cal_time = fields.DatetimeField(description="计数时间")

    class Meta:
        table = "bg_auto_message_count"
        table_description = "群组消息数量表"
        unique_together = (('group_id','auto_send_id'),)

    def __str__(self):
        return f"GroupMessageCount {self.id}: {self.group_id}, {self.auto_send_id},{self.message_count}"

class VideoModerationTask(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    msg_id = fields.BigIntField(index=True, description="消息ID")
    msg_dict = fields.JSONField(default=dict,description="msg_dict的原始信息")
    task_id = fields.CharField(index=True,default="",max_length=255, description="任务ID")
    suggestion = fields.CharField(max_length=255,default="", description
    ="审核建议")
    check_label = fields.CharField(max_length=512,default="", description
    ="审核标签")
    request_id = fields.CharField(max_length=255,default="", description="请求ID")
    task_result = fields.TextField(default="", description="任务结果")
    
    status = fields.CharField(index=True,max_length=255,default = "INIT", description="任务状态:取值：INIT(任务提交)、FINISH（任务已完成）、PENDING （任务等待中）、RUNNING （任务进行中）、ERROR （任务出错）、CANCELLED （任务已取消）")
    task_cnt = fields.IntField(default=0, description="任务重试次数")

    class Meta:
        table = "bg_v_moderation_task"
        table_description = "视频审核任务表"
        unique_together = (('group_id', 'msg_id'),)

    def __str__(self):
        return f"VideoModerationTask {self.id}: {self.group_id}, {self.msg_id}, {self.task_id}, {self.request_id}"


class MediaModerationTask(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    tg_id = fields.BigIntField(index=True, default = 0,description="用户ID")
    msg_id = fields.BigIntField(index=True, description="消息ID")
    file_type = fields.CharField(max_length=255,default="", description="文件图片类型,photo,animation,sticker")
    image_url = fields.CharField(max_length=255,default="", description="图片原始的URL")
    image_key = fields.CharField(max_length=255,default="", description="图片的s3key")
    msg_dict = fields.JSONField(default=dict,description="msg_dict的原始信息")
    file_unique_id = fields.CharField(index=True,default="",max_length=255, description="唯一ID")
    suggestion = fields.CharField(max_length=255,default="", description
    ="审核建议")
    check_label = fields.CharField(max_length=512,default="", description
    ="审核标签")
    request_id = fields.CharField(max_length=255,default="", description="请求ID")
    need_del = fields.IntField(default=0, description="是否删除,0表示不需要删除，1表示需要删除")
    del_ack = fields.IntField(default=0, description="删除回执")

    class Meta:
        table = "bg_m_moderation_task"
        table_description = "多媒体审核任务表"
        unique_together = (('group_id', 'msg_id'),)

    def __str__(self):
        return f"MediaModerationTask {self.id}: {self.group_id}, {self.msg_id}, {self.file_unique_id}, {self.request_id},{self.suggestion},{self.check_label}"

class DeleteGroupMsgImageCfg(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(unique_index=True, description="群组ID")
    delete_time = fields.IntField(default=0, description="删除时间，单位分钟，默认0表示不删除")
    is_enable = fields.BooleanField(index=True,default=False, description="是否启用")
    skip_tg_ids = fields.JSONField(default=list,description="跳过的用户ID列表")
    class Meta:
        table = "bg_delete_g_msg_image_cfg"
        table_description = "删除群消息图片配置表"
    def __str__(self):
        return f"DeleteGroupMessageImageConfig {self.id}: {self.group_id}, {self.delete_time}, {self.is_enable}, {self.skip_tg_ids}"
   
class DeleteGroupMsgDetail(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    msg_id = fields.BigIntField(index=True, description="消息ID")
    delete_time = fields.DatetimeField(index=True,description="删除时间")
    delete_result = fields.BooleanField(default =False,description="删除结果")
    delete_reason = fields.CharField(max_length=255, default ="",description="删除原因")
    exe_cnt = fields.IntField(index=True,default=1, description="执行次数")
    class Meta:
        table = "bg_delete_group_msg_detail"
        table_description = "删除群消息明细表"
        uq_delete_group_msg = (('group_id', 'msg_id'),)
        
    def __str__(self):
        return f"DeleteGroupMsgDetail {self.id}: {self.group_id}, {self.msg_id}, {self.delete_time}, {self.delete_reason}, {self.exe_cnt}"
class BotHelpWhiteListUser(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    bot_id = fields.BigIntField(index=True, description="机器人ID")
    user_id = fields.BigIntField(index=True, description="用户ID,tgid,bot的id")
    group_id = fields.BigIntField(index=True, description="群组ID")
    is_enable = fields.BooleanField(default=True, description="是否启用")
    class Meta:
        table = "bg_bot_help_white_user"
        table_description = "bot-help白名单用户表"
    def __str__(self):
        return f"BotHelpWhiteListUser {self.id}: {self.bot_id}, {self.user_id}, {self.group_id}, {self.is_enable}"

class GroupImageForwardCfg(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(unique_index=True, description="群组ID")
    forward_id = fields.BigIntField(index=True, description="转发的群或者频道ID")
    is_enable = fields.BooleanField(index=True,default=False, description="是否启用")
    desc = fields.CharField(max_length=255, default="",description="描述")

    class Meta:
        table = "bg_img_forward_cfg"
        table_description = "群组图片转发配置表"
    def __str__(self):
        return f"GroupImageForwardCfg {self.id}: {self.group_id}, {self.forward_id}, {self.is_enable}, {self.desc}"

class JoinGroupUserProcess(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    tg_id = fields.BigIntField(index=True, description="Telegram用户ID")
    bot_id = fields.BigIntField(index=True, description="机器人ID")
    process_status = fields.CharField(index=True,max_length=64, default="init", description="处理状态,fail,success,init,processing,ignore")

    class Meta:
        table = "bg_group_join_user_log"
        table_description = "群组用户进入记录"

    def __str__(self):
        return f"GroupUserProcessStatus {self.id}: group_id={self.group_id}, tg_id={self.tg_id}, status={self.process_status}"

class JoinGroupUserProcessHistory(BastTimeModel):
    id = fields.BigIntField(pk=True, description="主键ID")
    join_log_id = fields.BigIntField(index=True, default=0,description="关联的bg_group_join_user_log ID")
    group_id = fields.BigIntField(index=True, description="群组ID")
    tg_id = fields.BigIntField(index=True, description="Telegram用户ID")
    join_cnt = fields.IntField(default=1, description="入群次数")
    process_status = fields.CharField(index=True,max_length=64, default="init", description="处理状态,fail,success,init,processing,ignore")
    process_reason = fields.TextField(default="", description="处理原因")

    class Meta:
        table = "bg_group_new_user_process_history"
        table_description = "群组新用户处理历史表"

    def __str__(self):
        return f"GroupUserProcessHistory {self.id}: group_id={self.group_id}, tg_id={self.tg_id}, status={self.process_status}"

class BotGroupFeatureConfig(BastTimeModel):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=255, description="特性名称")
    group_id = fields.BigIntField(index=True,default = 0,description="群组ID")
    key_name = fields.CharField(index=True,max_length=100)
    config = fields.JSONField()
    enabled = fields.BooleanField(default=False)
    start_time = fields.DatetimeField(null=True)
    end_time = fields.DatetimeField(null=True)
    description = fields.CharField(max_length=500, null=True)

    class Meta:
        table = "bg_feature_config"
        unique_together = (('group_id', 'key_name'),)

    def __str__(self):
        return f"BotGroupFeatureConfig {self.id}: {self.name}, {self.key_name}, {self.enabled}"