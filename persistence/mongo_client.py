import os
from dotenv import load_dotenv
from motor.motor_asyncio import (
    AsyncIOMotorClient,
)

load_dotenv()

tavern_url = os.environ.get("MONGO_URL")
tavern_db = os.environ.get("MONGO_DB")
if not tavern_url or not tavern_db:
    raise ValueError("MONGO_URL or MONGO_DB is not set in env")

tavern = AsyncIOMotorClient(tavern_url)[tavern_db]

class TavernCollection:
    CARDS = tavern["cards"]
    CHARACTER_BOOK = tavern["character_book"]
    CHAT_HISTORY = tavern["chat_history"]
    CHAT_TIPS_HISTORY = tavern["chat_tips_history"]
    PRESETS = tavern["presets"]
    ROLE_PUBLISH_HISTORY = tavern["role_publish_history"]
    ROLE_BROADCAST_HISTORY = tavern["role_broadcast_history"]
    CHAT_ERROR_RECORD = tavern["chat_error_record"]