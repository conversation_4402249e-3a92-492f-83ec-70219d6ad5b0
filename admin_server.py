import logging
import os, json
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
import stripe
from typing import Union
from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from tortoise import <PERSON><PERSON><PERSON>, run_async
from tortoise.contrib.fastapi import register_tortoise
from dotenv import load_dotenv

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from services import translate_service

load_dotenv()

from admin import activity_diamond_api, audit_admin_api, auth, no_auth, open_api, operation_admin_api, regex_admin_api, stat_admin_api, tag_admin_api, tg_admin_api, tool_admin_api, translate_admin_api
from common import loggers
from common.common_constant import Env
from utils import env_util, response_util
import sentry_sdk
import uvicorn
from admin.role_admin_api import roles_router
from admin.presets_admin_api import presets_router
from admin.user import user_router
from admin import no_auth_admin_api
from admin.gift_admin_api import gift_router
from admin.global_config_admin_api import global_config_router
from admin.role_card_admin_api import char_book_router
from admin.cc_withdraw import cc_withdraw_router
from admin.ad_campaign_link import tg_link_router
# from controllers.test_contorller.test_sx_bot import test_sx_router
from controllers.discussion_webhook import bot_discussion_router
from admin.usdt_recharge import usdt_recharge_router
from admin.recharge import recharge_router
from admin.channel_control import channel_control_router
from utils.exception_util import ParamException
cors_origins = os.getenv("CORS_ORIGINS", "").split(",")
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=[
        "Conversation-Id",
        "Message-Id",
        "Message-Version",
        "Human-Message-Id",
    ],
)

app.include_router(roles_router)
app.include_router(presets_router)
app.include_router(user_router)
app.include_router(gift_router)
app.include_router(global_config_router)
app.include_router(char_book_router)
app.include_router(cc_withdraw_router)
app.include_router(translate_admin_api.translate_router)
app.include_router(tg_link_router)
# if env_util.get_current_env() != Env.PROD:
#     app.include_router(test_sx_router)
app.include_router(audit_admin_api.audit_router)
app.include_router(tag_admin_api.tag_router)
app.include_router(open_api.open_api_router)
app.include_router(usdt_recharge_router)
app.include_router(recharge_router)
app.include_router(tg_admin_api.tg_config_router)
app.include_router(activity_diamond_api.activity_diamond_router)
app.include_router(operation_admin_api.operation_router)
app.include_router(no_auth_admin_api.no_auth_router)
app.include_router(no_auth.no_auth_router)
app.include_router(tool_admin_api.tools_router)
app.include_router(channel_control_router)
app.include_router(regex_admin_api.regex_router)
app.include_router(stat_admin_api.stat_router)

if env_util.get_current_env() == Env.LOCAL:
    app.include_router(bot_discussion_router)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s (%(filename)s:%(lineno)d) [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()],
)
log = logging.getLogger(__name__)

dsn = os.getenv("SENTRY_DSN")
sentry_sdk.init(
    dsn=dsn,
    traces_sample_rate=1.0,
    profiles_sample_rate=1.0,
)

Tortoise.init_models(["persistence.models.models"], "models")
register_tortoise(
    app=app,
    db_url=os.environ["MYSQL_URL"],
    modules={"models": ["persistence.models.models"]},
    generate_schemas=True,
)


# 增加全局用户验证
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    origin = request.headers.get("Origin")
    if request.method == "OPTIONS":
        return await call_next(request)
    data = await auth.request_data(request)
    token_ret = await auth.verify_token(request)
    auth_ret = await auth.verify_auth(request)
    if token_ret and auth_ret:
        response = await call_next(request)
    if not token_ret:
        response =  response_util.auth_error("请登录",str(origin))
    if token_ret and not auth_ret:
        response = response_util.auth_error("无权限",str(origin))
    code = response.status_code
    await auth.add_operation_log_by_req(request,data,code)
    return response

@app.exception_handler(HTTPException)
async def global_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(content=exc.detail, status_code=exc.status_code)

@app.exception_handler(ParamException)
async def param_exception_handler(request: Request, exc: ParamException):
    log.error(f"ParamException: {exc.detail}")
    return JSONResponse(content=exc.detail, status_code=exc.status_code)



scheduler = AsyncIOScheduler()
@app.on_event("startup")
async def startup_event():
    await translate_service.refresh_all_cache()
    scheduler.add_job(translate_service.refresh_cache, "interval", seconds=60 * 5)
    scheduler.start()


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8900)
