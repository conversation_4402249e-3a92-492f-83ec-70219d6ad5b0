
from datetime import datetime, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
import logging
import pytz

from services.bot_group.g_msg_send_task_service import AutoSendMsgTaskService

from persistence.models.models_bot_group import BotGroupMap,AutoSendMessageConfig

from services.bot_group.group_msg_stat_service import GroupMsgStatService

CAL_GROUP_ID_LIST = [-1002380176119,-1002357871790, -1002411997003]

log = logging.getLogger(__name__)


def check_in_activity_time(start_date:datetime,end_date:datetime):
    # 时区转换
    # 当前时间
    now = datetime.now(pytz.utc)

    # 将 start_date 和 end_date 转换为带有时区信息的对象
    timezone = pytz.utc
# 检查并本地化 start_date 和 end_date
    if start_date.tzinfo is None:
        start_date = timezone.localize(start_date)
    if end_date.tzinfo is None:
        end_date = timezone.localize(end_date)
    if start_date <= now <= end_date:
        return True
    return False

#定时发送群消息
async def load_group_msg_task(schduler:AsyncIOScheduler):
    log.info("load_group_msg_task start")
    
    auto_cfg_list = await AutoSendMsgTaskService.get_auto_send_msg_config_all()
    
    jobs = schduler.get_jobs()
    for auto_send_msg_config in auto_cfg_list:
        
        check_time = check_in_activity_time(auto_send_msg_config.start_time,auto_send_msg_config.end_time)
        if not auto_send_msg_config.enabled or (check_time == False):
            log.info(f"auto_send_msg_config is not enabled: {auto_send_msg_config},{check_time}")
            job_id = str(auto_send_msg_config.id)
            for job in jobs:
                if job.id == job_id:
                    log.info(f"remove_job: {job_id}")
                    schduler.remove_job(job_id)
            continue
        cron_exp = auto_send_msg_config.cron_expression
        log.info("load_group_msg_task cron_exp: %s,%s", cron_exp,auto_send_msg_config)
        cron_trigger = CronTrigger.from_crontab(cron_exp)
        schduler.add_job(func=AutoSendMsgTaskService.send_group_msg_task, trigger=cron_trigger,args=[auto_send_msg_config],id=str(auto_send_msg_config.id),replace_existing=True)
    
    # 去掉已经删除的任务
    log.info("load_group_msg_task end")
    
    