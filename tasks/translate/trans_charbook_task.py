import json
import os
import re
from ai import inner_bot
from common.common_constant import Language
from common.translate_model import (
    TranslateBook,
    TranslateBookEntry,
    TranslateSubTag,
    TranslateTaskType,
)
from persistence import char_book_dao
from persistence.models.models import TranslateTask
from services import translate_service
from services.role import role_loader_service
from utils import date_util, json_util


async def init_role_book_task():
    # init role_book
    book_task_list = await translate_service.list_tasks_by_type(
        TranslateTaskType.CHAR_BOOK_KEYS.value
    )
    book_task_map = {x.task_key: x for x in book_task_list}
    role_configs = await role_loader_service.list_public()
    
    for role in role_configs:
        if not role.book_id:
            continue
        cb = await char_book_dao.get_by_book_id(role.book_id)
        if not cb:
            continue
        book_task = book_task_map.get(role.book_id, None)
        langs = [x for x in Language.fetch_all()]
        if not book_task:
            translate_book = TranslateBook.from_char_book(cb)

            await translate_service.upsert_task(
                TranslateTaskType.CHAR_BOOK_KEYS.value,
                str(cb.book_id),
                translate_book.model_dump(),
                langs,
            )
            continue
        if cb.updated_at <= date_util.datetime2timestamp(book_task.updated_at):
            continue
        translate_book = TranslateBook.from_char_book(cb)
        await translate_service.upsert_task(
            TranslateTaskType.CHAR_BOOK_KEYS.value,
            str(cb.book_id),
            translate_book.model_dump(),
            langs,
        )

async def run_char_book_keys_task(task: TranslateTask):
    target: dict = {}
    for lang in task.languages:
        mid_book = TranslateBook(**json_util.convert_to_dict(task.source))
        entries = []
        for entry in mid_book.entries:
            keys = [await inner_bot.translate(str(x), lang) for x in entry.keys]
            # secondary_keys = [
            #     inner_bot.translate(str(x), lang_desc) for x in entry.secondary_keys
            # ]
            entries.append(TranslateBookEntry(keys=keys))
        mid_ret = TranslateBook(entries=entries)
        target[lang] = mid_ret.model_dump()
    return target
