#!/usr/bin/env python3
"""
批量操作功能测试脚本
"""

import asyncio
import json
from typing import List, Dict, Any

# 模拟批量操作请求数据
def create_test_batch_request() -> Dict[str, Any]:
    """创建测试批量操作请求"""
    return {
        "channel_controls": [
            {
                "operation_id": "create_1",
                "operation_type": "CREATE",
                "channel": "TMPAY",
                "pay_type": "alipay",
                "min_amount": 100,
                "max_amount": 50000,
                "enabled": True,
                "ratio": 100,
                "remark": "测试创建1"
            },
            {
                "operation_id": "create_2", 
                "operation_type": "CREATE",
                "channel": "SJZF",
                "pay_type": "wechat",
                "min_amount": 200,
                "max_amount": 100000,
                "enabled": True,
                "ratio": 80,
                "remark": "测试创建2"
            },
            {
                "operation_id": "upsert_1",
                "operation_type": "UPSERT",
                "channel": "JLBZF",
                "pay_type": "alipay",
                "min_amount": 50,
                "max_amount": 30000,
                "enabled": True,
                "ratio": 120,
                "remark": "测试UPSERT"
            }
        ],
        "whitelists": [
            {
                "operation_id": "whitelist_1",
                "operation_type": "CREATE",
                "tg_id": 123456789,
                "channel": "TMPAY",
                "remark": "测试白名单1"
            },
            {
                "operation_id": "whitelist_2",
                "operation_type": "CREATE", 
                "tg_id": 987654321,
                "channel": "SJZF",
                "remark": "测试白名单2"
            }
        ],
        "fail_fast": True
    }

def create_update_batch_request() -> Dict[str, Any]:
    """创建更新操作的批量请求（需要先有数据）"""
    return {
        "channel_controls": [
            {
                "operation_id": "update_1",
                "operation_type": "UPDATE",
                "control_id": 1,  # 需要替换为实际的ID
                "channel": "TMPAY",
                "pay_type": "alipay",
                "min_amount": 150,
                "max_amount": 60000,
                "enabled": True,
                "ratio": 110,
                "remark": "测试更新1"
            },
            {
                "operation_id": "enable_1",
                "operation_type": "ENABLE",
                "channel": "SJZF",
                "pay_type": "wechat"
            },
            {
                "operation_id": "disable_1", 
                "operation_type": "DISABLE",
                "control_id": 2  # 需要替换为实际的ID
            }
        ],
        "whitelists": [
            {
                "operation_id": "delete_whitelist_1",
                "operation_type": "DELETE",
                "tg_id": 123456789,
                "channel": "TMPAY"
            }
        ],
        "fail_fast": False
    }

def print_batch_result(result: Dict[str, Any]):
    """打印批量操作结果"""
    print("=" * 60)
    print("批量操作结果:")
    print(f"总操作数: {result.get('total_operations', 0)}")
    print(f"成功操作数: {result.get('successful_operations', 0)}")
    print(f"失败操作数: {result.get('failed_operations', 0)}")
    print(f"执行时间: {result.get('execution_time_ms', 0)}ms")
    print()
    
    print("详细结果:")
    for i, op_result in enumerate(result.get('results', []), 1):
        print(f"{i}. 操作ID: {op_result.get('operation_id')}")
        print(f"   成功: {'是' if op_result.get('success') else '否'}")
        if op_result.get('error_message'):
            print(f"   错误: {op_result.get('error_message')}")
        if op_result.get('data'):
            print(f"   数据: {json.dumps(op_result.get('data'), ensure_ascii=False, indent=6)}")
        print()

async def test_batch_operations():
    """测试批量操作功能"""
    print("批量操作功能测试")
    print("=" * 60)
    
    # 测试创建操作
    print("1. 测试批量创建操作")
    create_request = create_test_batch_request()
    print("请求数据:")
    print(json.dumps(create_request, ensure_ascii=False, indent=2))
    print()
    
    # 这里应该调用实际的API，但由于是测试脚本，我们只是展示数据结构
    print("注意: 这是测试脚本，实际使用时需要调用 /admin/channel_control/batch API")
    print()
    
    # 模拟成功响应
    mock_response = {
        "total_operations": 5,
        "successful_operations": 4,
        "failed_operations": 1,
        "execution_time_ms": 150,
        "results": [
            {
                "operation_id": "create_1",
                "success": True,
                "data": {"id": 1, "channel": "TMPAY", "pay_type": "alipay"}
            },
            {
                "operation_id": "create_2", 
                "success": True,
                "data": {"id": 2, "channel": "SJZF", "pay_type": "wechat"}
            },
            {
                "operation_id": "upsert_1",
                "success": True,
                "data": {"id": 3, "channel": "JLBZF", "pay_type": "alipay", "operation": "created"}
            },
            {
                "operation_id": "whitelist_1",
                "success": True,
                "data": {"id": 1, "tg_id": 123456789, "channel": "TMPAY"}
            },
            {
                "operation_id": "whitelist_2",
                "success": False,
                "error_message": "无效的渠道: INVALID_CHANNEL"
            }
        ]
    }
    
    print_batch_result(mock_response)
    
    print("2. 测试批量更新操作")
    update_request = create_update_batch_request()
    print("请求数据:")
    print(json.dumps(update_request, ensure_ascii=False, indent=2))
    print()

def print_api_usage():
    """打印API使用说明"""
    print("API 使用说明")
    print("=" * 60)
    print("端点: POST /admin/channel_control/batch")
    print()
    print("支持的操作类型:")
    print("- CREATE: 创建新的渠道控制配置或白名单条目")
    print("- UPDATE: 更新现有的渠道控制配置（需要提供control_id）")
    print("- DELETE: 删除渠道控制配置（需要提供control_id）")
    print("- ENABLE: 启用渠道控制配置")
    print("- DISABLE: 禁用渠道控制配置")
    print("- UPSERT: 存在则更新，不存在则创建")
    print()
    print("渠道控制配置字段:")
    print("- operation_id: 操作唯一标识（必填）")
    print("- operation_type: 操作类型（必填）")
    print("- control_id: 配置ID（UPDATE/DELETE操作时必填）")
    print("- channel: 充值渠道（必填）")
    print("- pay_type: 支付类型（必填）")
    print("- min_amount: 最小金额（可选）")
    print("- max_amount: 最大金额（可选）")
    print("- enabled: 是否启用（可选）")
    print("- ratio: 比例（可选）")
    print("- remark: 备注（可选）")
    print()
    print("白名单条目字段:")
    print("- operation_id: 操作唯一标识（必填）")
    print("- operation_type: 操作类型（CREATE/DELETE）")
    print("- tg_id: Telegram用户ID（必填）")
    print("- channel: 充值渠道（必填）")
    print("- remark: 备注（可选）")
    print()
    print("请求参数:")
    print("- channel_controls: 渠道控制操作列表")
    print("- whitelists: 白名单操作列表")
    print("- fail_fast: 遇到错误时是否立即停止（默认true）")

if __name__ == "__main__":
    print_api_usage()
    print()
    asyncio.run(test_batch_operations())