import logging
from typing import Optional
from pydantic import BaseModel

from common.common_constant import Env
from utils import env_util, request_util

# API documentation: http://172.22.0.12:8140/docs


class GenerateImageResponse(BaseModel):
    image_url: Optional[str]  # 图像URL
    request_id: str  # 请求ID
    code: int  # 响应状态码
    message: str  # 响应消息
    spent: Optional[str]  # 耗时，单位秒
    width: Optional[int] = None  # 图像宽度
    height: Optional[int] = None  # 图像高度

    def success(self) -> bool:
        return self.code == 200 and bool(self.image_url)


def image_server_domain() -> str:
    if env_util.get_current_env() == Env.PROD:
        return "http://172.22.0.28:8140"
    return "http://172.22.0.12:8140"


# curl -X POST http://172.22.0.12:8140/card/ -H "Content-Type: application/json" -d '{"style": "1", "prompt": "a beautiful landscape", "resolution": "large_landscape", "request_id": "12345"}'
async def generate_image_request(
    style: str,
    prompt: str,
    resolution: str,
    request_id: str,
) -> GenerateImageResponse:
    request_body = {
        "style": style,
        "prompt": prompt,
        "resolution": resolution,
        "request_id": request_id,
    }
    url = f"{image_server_domain()}/bot/"
    ret = await request_util.post_json(url, post_data=request_body, timeout=30)
    logging.info(
        f"GenerateImageRequest: style={style}, prompt={prompt}, resolution={resolution}, request_id={request_id}, ret={ret}"
    )
    if ret:
        return GenerateImageResponse(**ret)
    return GenerateImageResponse(
        image_url=None,
        request_id=request_id,
        code=500,
        message="请求失败",
        spent=None,
    )
