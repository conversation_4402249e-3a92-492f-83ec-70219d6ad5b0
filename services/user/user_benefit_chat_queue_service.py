from datetime import UTC, datetime, timedelta, timezone
import logging
import math
import random

from common.common_constant import SKIP_QUEUE_BENEFIT_ID, SKIP_QUEUE_RECHARGE_PRODUCT_ID, ChatBenefitEnum, ChatChannel, ProductType, UserBenefitCategory, WelfareTaskType
from persistence.models.models import ChatBenefit, Product, User, UserChatBenefit, UserQueueRecord, UserTaskRecord, WelfareTask
from services.user import user_benefit_service


# 免费聊天模式触发排队的场景

log = logging.getLogger(__name__)

#经济-->极速， 极速-->魅惑
UPGRADE_MAP = {'m17':'m1', 'm1': 'm14'}
M17_MID = 'm17'
M1_MID = 'm1'
M14_MID = 'm14'

MAX_FREE_M17_TIMES = 15
MAX_FREE_M1_TIMES = 10
MAX_FREE_M14_TIMES = 10
MAX_TOTAL_FREE_TIMES = 15

MIN_PEOPLE_AHEAD = 1
MAX_PEOPLE_AHEAD = 2
MIN_WAIT_TIME_PER_PERSON = 2 # 秒
MAX_WAIT_TIME_PER_PERSON = 3 # 秒


async def add_skip_queue_benefit(user_id: int, recharge_order_id: str):
    await user_benefit_service.reward_chat_benefit_by_id(user_id, SKIP_QUEUE_BENEFIT_ID, ChatBenefitEnum.SKIP_QUEUE, recharge_order_id)

    # 购买免排队权益后，免费模式做升级
    # now_at = datetime.now(timezone.utc)
    # user_benefits = await UserChatBenefit.filter(user_id=user_id, benefit_type=ChatBenefitEnum.DAILY_FREE.value).all()
    # user_benefits = [
    #         x for x in user_benefits if x.valid_end_at.timestamp() >= now_at.timestamp()
    #     ]
    # if len(user_benefits) == 0:
    #     return
    
    # benefit_ids = [user_benefit.chat_benefit_id for user_benefit in user_benefits]
    # chat_benefit_config = await ChatBenefit.filter(id__in=benefit_ids).all()
    # mid_benefit_map = {}
    # chat_benefit_map = {}
    # for chat_benefit in chat_benefit_config:
    #     product_mid = chat_benefit.limit_chat_product_mid
    #     if not product_mid:
    #         continue
    #     mid_benefit_map[product_mid] = chat_benefit
    #     chat_benefit_map[chat_benefit.id] = chat_benefit
    
    # for user_benefit in user_benefits:
    #     if user_benefit.chat_benefit_id not in chat_benefit_map:
    #         continue
    #     chat_benefit = chat_benefit_map[user_benefit.chat_benefit_id]
    #     target_mid = UPGRADE_MAP.get(chat_benefit.limit_chat_product_mid)
    #     if not target_mid:
    #         continue
    #     target_benefit = mid_benefit_map.get(target_mid)
    #     if not target_benefit:
    #         continue
    #     user_benefit.chat_benefit_id = target_benefit.id
    #     await user_benefit.save()
            

async def get_latest_skip_queue_benefit(user_id: int) -> UserChatBenefit | None:
    return await UserChatBenefit.filter(user_id=user_id, benefit_type=ChatBenefitEnum.SKIP_QUEUE.value).order_by("-valid_end_at").first()

async def get_all_skip_queue_benefit(user_id: int) -> list[UserChatBenefit]:
    return await UserChatBenefit.filter(user_id=user_id, benefit_type=ChatBenefitEnum.SKIP_QUEUE.value).order_by("-valid_end_at").all()

async def is_in_skip_queue_benefit(user_id: int) -> bool:
    user_benefits = await UserChatBenefit.filter(user_id=user_id, benefit_type=ChatBenefitEnum.SKIP_QUEUE.value).order_by("-valid_end_at").first()
    if not user_benefits:
        return False
    if user_benefits.valid_end_at.timestamp() > datetime.now(timezone.utc).timestamp():
        return True
    return False

# 检查是否需要排队
async def _is_need_queue(user: User) -> bool:
    #检查免费聊天使用的次数
    benefit_list = await user_benefit_service.list_valid(user, UserBenefitCategory.FREE.value)
    total_used_times = 0
    for benefit in benefit_list:
        used_time = benefit.reward_times - benefit.remain_times
        total_used_times += used_time
        if total_used_times >= MAX_TOTAL_FREE_TIMES:
            return True
        match benefit.model_mid:
            case 'm17':
                if used_time >= MAX_FREE_M17_TIMES:
                    return True
            case 'm1':
                if used_time >= MAX_FREE_M1_TIMES:
                    return True
            case 'm14':
                if used_time >= MAX_FREE_M14_TIMES:
                    return True
    return False

async def _can_skip_queue_check(user: User) -> bool:
    # 注册72小时以内，可以免排队
    now = datetime.now(UTC)
    delta = now - user.created_at
    if delta <= timedelta(hours=72):
        return True
    
    user_id = user.id
    # 检查最新的2条, 免费福利领取小于等于1,不用排队
    tasks = await WelfareTask.filter(task_type=WelfareTaskType.DIRECT_RECEIVE.value, online=True).all()
    if not tasks:
        return True
    task_ids = [x.task_id for x in tasks]
    free_benefit_records = await UserTaskRecord.filter(user_id=user_id, task_id__in=task_ids).order_by("-finished_at").limit(2)
    if not free_benefit_records or len(free_benefit_records) < 2:
        return True
    # 检查是否有免排队福利
    has_skip_benefit = await is_in_skip_queue_benefit(user_id)
    if has_skip_benefit:
        return True
    return False


async def trigger_queue_check(user: User):
    can_skip_queue = await _can_skip_queue_check(user)
    if can_skip_queue:
        return 0, 0
    queue_record = await UserQueueRecord.filter(user_id=user.id).order_by("-end_ts").first()
    if queue_record:
        if queue_record.end_ts > int(datetime.now(timezone.utc).timestamp()):
            left_person_ahead, left_wait_time = _calculate_left_wait_time(queue_record)
            return left_person_ahead, left_wait_time
        elif queue_record.trigger_next_queue == 0:
            # 本次排队结束（本次聊天放行)
            # 会在本次聊天的后处理中更新 trigger_next_queue 字段
            return 0, 0
    if not await _is_need_queue(user):
        return 0, 0
    # 开启排队
    queue_record = await add_queue_record(user)
    return queue_record.person_ahead, queue_record.wait_time_per_person * queue_record.person_ahead


async def post_process_queue_check(user: User):
    if user.chat_channel != ChatChannel.FREE_BENEFIT.value:
        return
    queue_record = await UserQueueRecord.filter(user_id=user.id).order_by("-end_ts").first()
    if not queue_record:
        return
    if queue_record.end_ts > int(datetime.now(timezone.utc).timestamp()):
        return
    if queue_record.trigger_next_queue == 0:
        # 标示下次检查时就重新触发排队检查
        queue_record.trigger_next_queue = 1
        await queue_record.save()

async def add_queue_record(user: User):
    person_ahead = random.randint(MIN_PEOPLE_AHEAD, MAX_PEOPLE_AHEAD)
    wait_time_per_person = random.randint(MIN_WAIT_TIME_PER_PERSON, MAX_WAIT_TIME_PER_PERSON)
    wait_time = person_ahead * wait_time_per_person
    ts_now = int(datetime.now(timezone.utc).timestamp())
    queue_record = UserQueueRecord(user_id=user.id, start_ts=ts_now, end_ts=ts_now + wait_time, person_ahead=person_ahead, wait_time_per_person=wait_time_per_person)
    await queue_record.save()
    return queue_record

# 获取用户免费聊天的当前排队信息
async def get_user_queue(user_id: int):
    is_skip_queue = await is_in_skip_queue_benefit(user_id)
    if is_skip_queue:
        return {
            "skip_queue_benefit": True,
            "person_ahead": 0,
            "left_wait_time": 0,
            "recharge_product_id": SKIP_QUEUE_RECHARGE_PRODUCT_ID
        }
    queue_record = await UserQueueRecord.filter(user_id=user_id).order_by("-end_ts").first()
    if not queue_record:
        return {
            "skip_queue_benefit": False,
            "person_ahead": 0,
            "left_wait_time": 0,
            "recharge_product_id": SKIP_QUEUE_RECHARGE_PRODUCT_ID
        }
    left_person_ahead, left_wait_time = _calculate_left_wait_time(queue_record)
    return {
        "skip_queue_benefit": False,
        "person_ahead": left_person_ahead,
        "left_wait_time": left_wait_time,
        "recharge_product_id": SKIP_QUEUE_RECHARGE_PRODUCT_ID
    }

def _calculate_left_wait_time(queue_record: UserQueueRecord):
    ts_now = int(datetime.now(timezone.utc).timestamp())
    if ts_now >= queue_record.end_ts:
        return 0, 0
    left_wait_time = queue_record.end_ts - ts_now 
    left_person_ahead = math.ceil(left_wait_time / queue_record.wait_time_per_person)
    return left_person_ahead, left_wait_time


