import logging
import uuid
from datetime import datetime, timedelta, UTC
from urllib.parse import urlparse, parse_qs
from pydantic import BaseModel
from tortoise.transactions import in_transaction
from persistence.models.models import (
    ExpirableAward,
    ExpirableStatusEnum,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeStatusEnum,
)
from typing import Dict, Any

from services.reward_calculation_service import RewardCalculationService
from utils import tg_util

monitor_chat_id = '-1002550423097'

class RechargeRequest(BaseModel):
    recharge_id: str
    type: str = 'alipay'
    isIOS: bool = False
    channel: str = 'channel1'
    platform: str = ''
    bot_id: int = 0
    user_id: int = 0

class DirectRechargeRequest(BaseModel):
    recharge_id: str
    type: str = 'alipay'
    isIOS: bool = False
    channel: str = 'channel1'
    platform: str = ''
    bot_id: int = 0
    user_id: int = 0
    tg_bot_id: str = ''
    language: str = 'zh'

async def create_recharge_order(user_id: int, recharge_product_id: str, 
                                recharge_channel: RechargeChannelEnum, pay_type: str,
                                bot_id: int = 0, is_whitelist: bool = False,
                                platform: str = ''
                                ) -> RechargeOrder | None:
    recharge_product = await RechargeProduct.filter(recharge_product_id=recharge_product_id).first()
    if recharge_product is None:
        return None
    return await create_recharge_order_with_product(
        user_id, recharge_product, recharge_channel, pay_type = pay_type, 
        bot_id=bot_id, is_whitelist=is_whitelist, platform=platform)

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct,
                                             recharge_channel: RechargeChannelEnum, bot_id: int = 0, pay_type: str = '', is_whitelist: bool = False, platform: str = '') -> RechargeOrder | None:
    try:
        order = RechargeOrder(
            user_id=user_id,
            out_order_id=str(uuid.uuid4()),
            amount=recharge_product.amount + recharge_product.reward_amount,
            pay_fee=recharge_product.cny_price,
            pay_currency='CNY',
            recharge_channel=recharge_channel,
            status=RechargeStatusEnum.INIT,
            recharge_product_id=recharge_product.recharge_product_id,
            from_bot_id=bot_id,
            pay_type=pay_type,
            platform=platform
        )
        if is_whitelist:
            order.exclude_from_stat = True
            order.exclude_reason = '渠道白名单'
        await order.save()
        return order
    except Exception as e:
        logging.exception(e)
        return None

async def update_out_order_id(recharge_order_id: str, out_order_id: str,
                      pay_url: str | None = None):
    async with in_transaction():
        order = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order is None or order.status != RechargeStatusEnum.INIT:
            return
        order.out_order_id = out_order_id
        if pay_url:
            order.pay_url = pay_url
            try:
                parsed_url = urlparse(pay_url)
                query_params = parse_qs(parsed_url.query)
                for param_name, param_values in query_params.items():
                    if param_name == 'return_url' or param_name == 'notify_url':
                        continue
                    for param_value in param_values:
                        try:
                            parsed_param = urlparse(param_value)
                            if parsed_param.scheme and parsed_param.netloc:
                                logging.info(f"Found URL parameter '{param_name}': {param_value}")
                                order.pay_redirect_url = param_value
                                break
                        except Exception:
                            continue
            except Exception as e:
                logging.warning(f"Failed to parse pay_url: {e}")
        await order.save()

async def update_order_status(recharge_order_id: str, status: RechargeStatusEnum):
    async with in_transaction():
        order = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order is None:
            return
        order.status = status
        await order.save()

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str,
                      order_id: int | None = None) -> RechargeOrder:
    async with in_transaction():
        if order_id is not None:
            order: RechargeOrder = await RechargeOrder.get(id=order_id)
        else:
            order: RechargeOrder = await RechargeOrder.get(recharge_order_id=recharge_order_id)
        if order.status == RechargeStatusEnum.SUCCEED:
            return order
        recharge_product: RechargeProduct = await RechargeProduct.filter(recharge_product_id=order.recharge_product_id).first() # type: ignore

        history_orders = await RechargeOrder.filter(
            user_id=order.user_id, status=RechargeStatusEnum.SUCCEED, 
            pay_fee__gt=0).all()
        if len(history_orders) <= 0:
            recharge_product.reward_amount = await RewardCalculationService.calculate_reward_amount(order.user_id, recharge_product)

        now = datetime.now(UTC)
        order.out_order_id = out_order_id
        order.status = RechargeStatusEnum.SUCCEED
        order.finished_at = now
        order.raw_response = raw_data
        order.exclude_from_stat = False
        order.exclude_reason = ''
        await order.save()

        # payed amount
        payed_award = ExpirableAward(user_id=order.user_id,
                                        out_order_id = order.recharge_order_id,
                                        total_amount=recharge_product.amount,
                                        spend_amount=0,
                                        balance=recharge_product.amount,
                                        status=ExpirableStatusEnum.NORMAL,
                                        expires_at=datetime(2500, 1, 1),
                                        claim_at=now,
                                        from_type='PAYED')
        await payed_award.save()
        if recharge_product.reward_amount > 0:
            reward_award = ExpirableAward(user_id=order.user_id,
                                            out_order_id = order.recharge_order_id,
                                            total_amount=recharge_product.reward_amount,
                                            spend_amount=0,
                                            balance=recharge_product.reward_amount,
                                            status=ExpirableStatusEnum.NORMAL,
                                            expires_at=now + timedelta(days=recharge_product.charged_expire_delta),
                                            claim_at=now)
            await reward_award.save()

        # 停止营销推送
        try:
            from services.recharge_marketing_service import RechargeMarketingService
            await RechargeMarketingService.handle_user_recharged(order.user_id)
        except Exception as e:
            logging.warning(f"Failed to handle marketing notifications for user {order.user_id}: {e}")
        delta = now - order.created_at
        if delta.seconds / 60 > 5:
            logging.warning(f"Recharge order took too long to complete:  {order.recharge_order_id}, {delta.seconds / 60} minutes")
            await tg_util.sm({
                '回调时间过长': f'渠道: {order.recharge_channel.value}',
                '订单 ID': order.recharge_order_id,
                '外部订单 ID': order.out_order_id,
                '耗时(分钟)': delta.seconds / 60,
                '支付方式': order.pay_type,
                '用户 ID': order.user_id,
                '金额': order.amount / 100000
            }, monitor_chat_id)
        return order

async def get_recent_channel_orders(channel: str, limit: int) -> list[RechargeOrder]:
    dt = datetime.now(UTC) - timedelta(hours=1)
    return (
        await RechargeOrder.filter(recharge_channel=channel, created_at__gt=dt)
        .order_by("-id")
        .limit(limit)
        .all()
    )

class RechargeChannelHandler:
    """充值渠道处理基类"""
    
    def __init__(self, channel_enum: RechargeChannelEnum):
        self.channel_enum = channel_enum
        
    async def create_order(self, user_id: int, recharge_id: str, type_name: str, client_ip: str) -> Dict[str, Any]:
        """创建充值订单，返回包含pay_url的字典"""
        raise NotImplementedError()
    
    async def _process_payment(self, order: RechargeOrder, type_name: str, client_ip: str) -> Dict[str, Any]:
        """具体的支付处理逻辑，由子类实现"""
        raise NotImplementedError()
    
    async def update_out_order_id(self, order_id: str, out_order_id: str) -> None:
        """更新外部订单ID，由子类实现"""
        raise NotImplementedError()