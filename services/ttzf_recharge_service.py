from datetime import datetime
from decimal import Decimal
import hashlib
import logging
import requests
import os
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
)
from utils import env_const
from services import out_recharge_common

ttzf_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/ttzf/notify'
ttzf_recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/ttzf/result'

APP_PAY_ID = env_const.TTZF_APP_ID
APP_PAY_KEY = env_const.TTZF_APP_KEY
APP_PAY_HOST = env_const.TTZF_HOST

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct, bot_id: int = 0) -> RechargeOrder | None:
    return await out_recharge_common.create_recharge_order_with_product(user_id, recharge_product, RechargeChannelEnum.TTZF, bot_id)

async def update_out_order_id(recharge_order_id: str, out_order_id: str, pay_url: str | None = None):
    return await out_recharge_common.update_out_order_id(recharge_order_id, out_order_id, pay_url)

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    return await out_recharge_common.pay_success(recharge_order_id, out_order_id, raw_data)

def create_ttzf_order(order: RechargeOrder, type: str, client_ip: str):
    amount = int(order.pay_fee / 1000)
    if type == 'wechat':
        way_code = '100'
    else: # alipay
        way_code = '101'

    params = {
        'mchId': APP_PAY_ID,
        'wayCode': way_code,
        'subject': 'VIP充值',
        'outTradeNo': str(order.recharge_order_id),
        'amount': amount,
        'notifyUrl': ttzf_recharge_notify_url,
        'clientIp': client_ip,
        'reqTime': int(datetime.now().timestamp()) * 1000,
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={APP_PAY_KEY}'
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['sign'] = sign.upper()

    resp = requests.post(APP_PAY_HOST, 
                        json=params, 
                        proxies=env_const.RECHARGE_PROXY,
                        verify=False)

    logging.info(f'ttzf_recharge_response: {resp.text}')
    return resp.json()