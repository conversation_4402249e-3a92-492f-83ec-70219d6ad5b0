import asyncio
from datetime import UTC, datetime
import logging
from common.chat_bot_model import UserActiveBotDetail
from common.common_constant import BotCategory
from persistence import redis_client
from persistence.models.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, User, UserBotActive
from services import config_service, tg_config_service
from utils import tg_util

log = logging.getLogger(__name__)


REGISTER_SOURCE_MAP = {
    "CHAT_BOT": 7023712364,
    "CHAT_BOT_1": 7794866380,
    "CHAT_BOT_2": 7124316437,
    "TMA": 7428622943,
    "TMA_1": 7622818306,
    "TMA_2": 7733073744,
    "TMA_10086": 8196109543,
    "TMA_666": 7982407404,
}


async def list_active_bots_detail(user: User) -> list[UserActiveBotDetail]:
    active_list = await list_active_bots(user)
    user_active_list = await UserBotActive.filter(user_id=user.id).all()
    last_active_at_map = {x.bot_id: x.last_active_at for x in user_active_list}
    ret = []
    for bot in active_list:
        ret.append(
            UserActiveBotDetail(
                bot_id=bot.bot_id,
                bot_username=bot.username,
                bot_first_name=bot.first_name,
                user_last_active_at=last_active_at_map.get(
                    bot.bot_id,
                    datetime.now(UTC),
                ),
            )
        )
    return ret


async def list_active_bots(user: User):
    user_active_list = await UserBotActive.filter(user_id=user.id).all()
    user_active_list.sort(key=lambda x: x.last_active_at, reverse=True)
    register_bot_id = REGISTER_SOURCE_MAP.get(user.register_source, 0)
    active_bot_ids = [x.bot_id for x in user_active_list]
    if register_bot_id and register_bot_id not in active_bot_ids:
        active_bot_ids.append(register_bot_id)
    bot_configs = await tg_config_service.list_bots_by_ids(active_bot_ids)
    bot_config_map = {x.bot_id: x for x in bot_configs}
    ret = [
        bot_config_map.get(x.bot_id)
        for x in user_active_list
        if x.bot_id in bot_config_map
    ]
    return [x for x in ret if x]


async def refresh_by_tg(bot_id: int, tg_id: int):
    async def async_task():
        tg_user = await TelegramUser.filter(tg_id=tg_id).first()
        if not tg_user:
            return
        await _refresh_user_active(tg_user.uid, bot_id)

    asyncio.create_task(async_task())


async def refresh_by_api(tg_init_data: str, tg_bot_source: str):
    if not tg_init_data or not tg_bot_source:
        return
    log.info(f"refresh_user_active:{tg_init_data},{tg_bot_source}")
    if not tg_init_data or not tg_bot_source:
        return

    async def async_task():
        tg_id = tg_util.get_user_id_by_tg(tg_init_data)
        if not tg_id:
            return
        tg_user = await TelegramUser.filter(tg_id=tg_id).first()
        if not tg_user:
            return
        # redis_client.active_user_sadd(tg_user.uid)
        bot_config = await tg_config_service.get_bot_config_by_tma_bot_id(tg_bot_source)
        # bot_config = await config_service.get_bot_by_source(tg_bot_source)
        if not bot_config:
            log.warning(
                f"refresh_user_active bot_config not found:user_id:{tg_user.uid},tg_id:{tg_id},{tg_bot_source}"
            )
            return
        await _refresh_user_active(tg_user.uid, bot_config.bot_id)
        return

    asyncio.create_task(async_task())


async def _refresh_user_active(user_id: int, bot_id: int):
    try:
        # upsert UserBotActive
        user_active = await UserBotActive.filter(user_id=user_id, bot_id=bot_id).first()

        if user_active:
            log.info(f"refresh_user_active update:{user_id},{bot_id}")
            user_active.last_active_at = datetime.now(UTC)
            user_active.blocked = False
            await user_active.save()
        else:
            log.info(f"refresh_user_active create:{user_id},{bot_id}")
            await UserBotActive.create(
                user_id=user_id,
                bot_id=bot_id,
                last_active_at=datetime.now(UTC),
                blocked=False,
            )
    except Exception as e:
        log.error(f"refresh_user_active error:{e}")


async def list_join_chat_records(user_id: int) -> list[ChatJoinRecord]:
    chat_join_records = await ChatJoinRecord.filter(user_id=user_id).all()
    chat_join_records.sort(key=lambda x: x.created_at, reverse=True)
    return chat_join_records


async def get_check_in_url(user: User, tma_bot_id: str):
    if not tma_bot_id or tma_bot_id == "0":
        return ""
    try:
        chat_main_bot = await tg_config_service.get_main_bot_by_category(
            BotCategory.CHAT_BOT
        )
        tma_main_bot = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
        if tma_main_bot.tma_bot_id == tma_bot_id:
            return chat_main_bot.url

        return tma_main_bot.url
    except Exception as e:
        log.error(f"get_check_in_url error:{e}")
        return ""
