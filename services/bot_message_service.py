from datetime import datetime, <PERSON>elta
import json
import logging
import re
from typing import Optional
from aiogram.enums import ParseMode
from aiogram import Bot
import requests

from aiogram import Bo<PERSON>, types
from aiogram.types import BufferedInputFile
from aiogram.utils.keyboard import InlineKeyboardBuilder
from common.bot_common import (
    CHANNEL_CONTENT,
    ROLE_GROUP_BOTTOM_MESSAGE,
    Button,
    MessageTemplate,
    RechargeProductCallback,
    RechargeProductPayTypeCallback,
)
from common.common_constant import (
    SKIP_QUEUE_RECHARGE_PRODUCT_ID,
    BotCategory,
    BotReplace,
    BotResourcePosition,
    BotType,
    ChannelCategory,
    GroupCategory,
    Language,
    ProductType,
    ResourcePlatform,
    RolePlayType,
    RoleReplace,
    UserReplace,
)
from persistence import role_broadcast_history_dao
from persistence.models.models import (
    GlobalPinedMessage,
    ResourceTemplate,
    RoleConfig,
    SubTag,
    TelegramUser,
    User,
    <PERSON>rBotActive,
    BotResource,
)
from persistence.models.mongo_models import RoleBroadcastHistory
from persistence.redis_client import redis_client
from services import (
    product_service,
    recharge_service,
    tag_service,
    tg_config_service,
    tg_message_service,
    user_active_service,
    user_service,
)
from services.role import role_loader_service
from utils import env_const, image_util, json_util, request_util, role_util, str_util
from utils.exception_util import async_catch_exception, async_ignore_catch_exception
from utils.translate_util import _tl

log = logging.getLogger(__name__)


async def get_message_template_new(
    platform: ResourcePlatform,
    position: BotResourcePosition,
    target_id: int = 0,
    default_resource: bool = True,
):
    resource = await ResourceTemplate.filter(
        platform=platform.value, position=position.value, target_id=target_id
    ).first()
    if not resource and default_resource and target_id:
        resource = await ResourceTemplate.filter(
            platform=platform.value, position=position.value, target_id=0
        ).first()
    if not resource:
        return None
    message_template = MessageTemplate(
        **json_util.convert_to_dict(resource.message_template)
    )
    message_template.log_tag = "resource_" + str(resource.id)
    return await format_template_replace(message_template)


async def get_message_content_new(
    platform: ResourcePlatform,
    position: BotResourcePosition,
    target_id: int = 0,
    default_resource: bool = True,
):
    resource = await ResourceTemplate.filter(
        platform=platform.value, position=position.value, target_id=target_id
    ).first()
    if not resource and default_resource and target_id:
        resource = await ResourceTemplate.filter(
            platform=platform.value, position=position.value, target_id=0
        ).first()
    if not resource:
        return None
    return await format_content_replace(resource.content)


async def get_resource_content(
    bot_id: int,
    bot_type: BotType,
    position: BotResourcePosition,
    default_resource: bool = False,
):
    resource = await BotResource.filter(bot_id=bot_id, position=position.value).first()
    if not resource and default_resource:
        resource = await BotResource.filter(
            bot_id=0, bot_type=bot_type.value, position=position.value
        ).first()
    if not resource:
        return None
    return await format_content_replace(resource.content)


async def get_message_template(
    bot_id: int,
    bot_type: BotType,
    position: BotResourcePosition,
    default_resource: bool = False,
):
    resource = await BotResource.filter(
        bot_id=bot_id, bot_type=bot_type.value, position=position.value
    ).first()
    if not resource and default_resource:
        resource = await BotResource.filter(
            bot_id=0, bot_type=bot_type.value, position=position.value
        ).first()

    if not resource or not resource.message_template:
        return None
    message = MessageTemplate(**json_util.convert_to_dict(resource.message_template))
    message.log_tag = "resource_" + str(resource.id)
    return await format_template_replace(message)


async def del_message(chat_id: int, message_id: int, bot: Optional[Bot] = None):
    try:
        helper = bot
        if not helper:
            helper = await tg_config_service.get_main_sender_bot_by_category(
                BotCategory.HELPER
            )
        ret = await helper.delete_message(chat_id=chat_id, message_id=message_id)
        log.info(f"del_message chat_id:{chat_id},message_id:{message_id}")
        return ret
    except Exception as e:
        logging.warning(f"del_global_message failed: {e}")
    return False


async def resend_channel_bottom_message(chat_id: int):
    channel = await tg_config_service.get_channel_by_chat_id(chat_id)
    if not channel or not channel.open_bottom_message:
        return None
    message_template = await get_message_template_new(
        ResourcePlatform.CHANNEL, BotResourcePosition.CHANNEL_BOTTOM, chat_id
    )
    if not message_template:
        return None
    log.info(f"resend_channel_bottom_message chat_id:{chat_id}")
    pined_message = await GlobalPinedMessage.filter(
        chat_id=chat_id, position=BotResourcePosition.CHANNEL_BOTTOM.value
    ).first()
    sender_bot = await tg_config_service.get_channel_sender(chat_id)
    if pined_message and pined_message.message_id:
        await del_message(
            chat_id=chat_id, message_id=pined_message.message_id, bot=sender_bot
        )
    if not pined_message:
        pined_message = GlobalPinedMessage(
            chat_id=chat_id, position=BotResourcePosition.CHANNEL_BOTTOM.value
        )
    message = await sender_bot.send_message(
        chat_id=chat_id,
        text=message_template.tips,
        parse_mode=ParseMode.HTML,
        reply_markup=message_template.as_markup(),
    )
    if not message:
        log.error(
            f"resend_bottom_message failed chat_id:{chat_id},message_template:{message_template}"
        )
        return None
    log.info(
        f"resend_bottom_message success chat_id:{chat_id} message:{message.message_id},message_template:{message_template}"
    )
    pined_message.message_id = message.message_id
    await pined_message.save()
    return message


async def resend_bottom_message(
    chat_id: int, helper_bot_id: int, message_template: MessageTemplate
):
    log.info(f"resend_bottom_message chat_id:{chat_id}")
    pined_message = await GlobalPinedMessage.filter(
        chat_id=chat_id, position=BotResourcePosition.CHANNEL_BOTTOM.value
    ).first()
    sender_bot = await tg_config_service.get_sender_bot_by_id(helper_bot_id)
    if pined_message and pined_message.message_id:
        await del_message(
            chat_id=chat_id, message_id=pined_message.message_id, bot=sender_bot
        )
    if not pined_message:
        pined_message = GlobalPinedMessage(
            chat_id=chat_id, position=BotResourcePosition.CHANNEL_BOTTOM.value
        )
    message = await sender_bot.send_message(
        chat_id=chat_id,
        text=message_template.tips,
        parse_mode=message_template.parse_mode,
        reply_markup=message_template.as_markup(),
    )
    if not message:
        log.error(
            f"resend_bottom_message failed chat_id:{chat_id},message_template:{message_template}"
        )
        return None
    log.info(
        f"resend_bottom_message success chat_id:{chat_id} message:{message.message_id},message_template:{message_template}"
    )
    pined_message.message_id = message.message_id
    await pined_message.save()
    return message


def replace_user_info(content: str, tg_info: TelegramUser, require_matches: list[str]):
    if not require_matches:
        require_matches = [x.value for x in UserReplace]
    for user_match in require_matches:
        if user_match == UserReplace.TG_FIRST_NAME.value:
            content = content.replace(user_match, tg_info.first_name)
        if user_match == UserReplace.TG_LAST_NAME.value:
            content = content.replace(user_match, tg_info.last_name)
    return content


def replace_role_info(
    content: str,
    role: RoleConfig,
    author: str,
    require_matches: list[str],
    language: str = Language.ZH.value,
):

    if not require_matches:
        require_matches = [x.value for x in RoleReplace]
    for role_match in require_matches:
        if role_match == RoleReplace.AUTHOR.value:
            content = content.replace(role_match, str_util.format_markdown_text(author))
        if role_match == RoleReplace.ROLE_NAME.value:
            content = content.replace(
                role_match, str_util.format_markdown_text(role.role_name)
            )
        if role_match == RoleReplace.CARD_NAME.value:
            content = content.replace(
                role_match, str_util.format_markdown_text(role.card_name)
            )
        if role_match == RoleReplace.ROLE_INTRO.value:
            intro = str_util.tg_caption_cut(role.introduction, 400)
            intro = str_util.format_markdown_text(intro)
            content = content.replace(role_match, intro)
        if role_match == RoleReplace.ROLE_SUB_TAGS.value:
            sub_tags = json_util.convert_to_list(role.sub_tags)
            sub_tags = [f"#{tag}" for tag in sub_tags]
            sub_tags = [str_util.format_markdown_text(tag) for tag in sub_tags]
            tag_str = str.join(" ", sub_tags)
            content = content.replace(role_match, tag_str)
        if role_match == RoleReplace.ROLE_ID.value:
            content = content.replace(role_match, str(role.id))
        if role_match == RoleReplace.ROLE_FILTER_CHAT_TYPE.value:
            fct = role_util.parse_role_filter_chat_type_desc(
                role.real_role, role.chat_type, json_util.convert_to_list(role.sub_tags)
            )
            fct = _tl(fct, language)
            content = content.replace(role_match, str_util.format_markdown_text(fct))
        if role_match == RoleReplace.ROLE_PLAY_TYPE.value:
            play_type = (
                RolePlayType(role.play_type) if role.play_type else RolePlayType.RIVALRY
            )
            play_type = _tl(play_type.to_desc(), language)
            play_type = str_util.format_markdown_text(play_type)
            content = content.replace(role_match, play_type)
    return content


async def format_content_replace(
    content: str, user_id: int = 0, role_id: int = 0, language: str = Language.ZH.value
) -> str:
    if not content:
        return content
    # 通过正则匹配所有{{}}中的内容,包括{{}}，写个python匹配代码
    pattern = r"\{\{.*?\}\}"
    matches = re.findall(pattern, content)
    matches = [str(x) for x in matches]
    matches = list(set(matches))
    need_matches = [x.value for x in UserReplace if x.value in matches]
    # 任何一个UserReplace在matches中
    if need_matches and user_id:
        tg_info = await user_service.get_tg_user_by_id(user_id)
        if tg_info:
            content = replace_user_info(content, tg_info, need_matches)
    if RoleReplace.TOKEN_COUNT.value in matches:
        role_token = await role_loader_service.role_token_sum(role_id)
        role_token = str(role_token) if role_token > 2000 else "<2000"
        content = content.replace(RoleReplace.TOKEN_COUNT.value, role_token)
    if RoleReplace.SUPPORT_MODELS.value in matches:
        product_short_names = await role_loader_service.support_product_short_names(
            role_id, language
        )
        product_short_names = [
            str_util.format_markdown_text(name) for name in product_short_names
        ]
        product_short_names = [f"\\#{name}" for name in product_short_names]
        short_name = "、".join(product_short_names)
        content = content.replace(RoleReplace.SUPPORT_MODELS.value, short_name)

    need_matches = [x.value for x in RoleReplace if x.value in matches]
    if need_matches and role_id:
        common_nickname = "你" if language == Language.ZH.value else "You"
        role_info = await role_loader_service.load_translated_role(
            role_id, language, common_nickname
        )
        if role_info:
            role_info = role_util.format_role_config(role_info, common_nickname)
            author = await user_service.get_nickname(role_info.uid)
            content = replace_role_info(
                content, role_info, author, need_matches, language
            )

    if BotReplace.MAIN_TMA_BOT.value in matches:
        tma_bot = await tg_config_service.get_main_bot_by_category(BotCategory.TMA)
        content = content.replace(BotReplace.MAIN_TMA_BOT.value, tma_bot.username)
    if BotReplace.MAIN_CHANNEL.value in matches:
        channel_bot = await tg_config_service.get_main_channel_by_category(
            ChannelCategory.ROLE
        )
        content = content.replace(BotReplace.MAIN_CHANNEL.value, channel_bot.username)
    if BotReplace.MAIN_GROUP.value in matches:
        group_bot = await tg_config_service.get_main_group()
        content = content.replace(BotReplace.MAIN_GROUP.value, group_bot.username)
    if BotReplace.MAIN_CHAT_BOT.value in matches:
        chat_bot = await tg_config_service.get_main_bot_by_category(
            BotCategory.CHAT_BOT
        )
        content = content.replace(BotReplace.MAIN_CHAT_BOT.value, chat_bot.username)
    if BotReplace.MAIN_CUSTOMER_BOT.value in matches:
        customer_bot = await tg_config_service.get_main_bot_by_category(
            BotCategory.CUSTOMER
        )
        content = content.replace(
            BotReplace.MAIN_CUSTOMER_BOT.value, customer_bot.username
        )
    if BotReplace.MAIN_WELFARE_CHANNEL.value in matches:
        welfare_channel = await tg_config_service.get_main_channel_by_category(
            ChannelCategory.WELFARE
        )
        content = content.replace(
            BotReplace.MAIN_WELFARE_CHANNEL.value, welfare_channel.username
        )
    if BotReplace.MAIN_BUSINESS_BOT.value in matches:
        business_bot = await tg_config_service.get_main_bot_by_category(
            BotCategory.BUSINESS
        )
        content = content.replace(
            BotReplace.MAIN_BUSINESS_BOT.value, business_bot.username
        )
    if BotReplace.MAIN_GROUP_ROLE.value in matches:
        group_role = await tg_config_service.get_main_group(GroupCategory.ROLE)
        content = content.replace(BotReplace.MAIN_GROUP_ROLE.value, group_role.username)
    if BotReplace.MAIN_EN_CHAT_BOT.value in matches:
        chat_bot = await tg_config_service.get_main_bot_by_category(
            BotCategory.EN_CHAT_BOT
        )
        content = content.replace(BotReplace.MAIN_EN_CHAT_BOT.value, chat_bot.username)
    return content


async def format_template_replace(message_template: MessageTemplate) -> MessageTemplate:
    message_template.tips = await format_content_replace(message_template.tips)
    if message_template.buttons:
        for btn in message_template.buttons:
            btn.url = await format_content_replace(btn.url)
            btn.text = await format_content_replace(btn.text)
    return message_template


# async def send_user_message(user: User, message_template: MessageTemplate):
#     user_id = user.id
#     user_actives = await UserBotActive.filter(user_id=user_id).all()
#     user_actives = [x for x in user_actives if not x.blocked]
#     bot_ids = [x.bot_id for x in user_actives]
#     bots = await bot_config_service.list_active_bot_by_ids(bot_ids)
#     bot_ids = [x.bot_id for x in bots]
#     user_actives = [x for x in user_actives if x.bot_id in bot_ids]
#     if not user_actives:
#         return
#     tg_info = await user_service.get_tg_user_by_id(user.id)
#     if not tg_info:
#         return None
#     user_actives.sort(key=lambda x: x.created_at, reverse=True)

#     for user_active in user_actives:
#         sender_bot = await bot_config_service.get_bot_sender(user_active.bot_id)
#         message_template = await format_template_replace(message_template)
#         message = await sender_bot.send_message(
#             chat_id=tg_info.tg_id,
#             text=message_template.tips,
#             parse_mode=ParseMode.HTML,
#             reply_markup=message_template.as_markup(),
#         )
#         return message

#     return None


# async def refresh_bot_resource():
#     # bots = await BotConfig.all()
#     # bots = [bot for bot in bots if not bot.init or not bot.status]

#     # for bot in bots:
#     #     if not bot.refresh_resource:
#     #         continue
#     #     await bot_message_service.refresh_bot_resource(bot.bot_id)

#     channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
#     push_channels = [x for x in channels if x.push_message]
#     for channel in push_channels:
#         await refresh_push_message(channel.chat_id)

#     refresh_channels = [x for x in channels if x.refresh_message]
#     for channel in refresh_channels:
#         await refresh_channel_message(channel.chat_id)


async def check_and_replace(content: str | None):
    input_content = content
    if not content:
        return False, content
    channel_map = {
        x.username: x
        for x in await tg_config_service.list_channel(category=None, active=False)
    }
    bot_map = {
        x.username: x
        for x in await tg_config_service.list_bot(category=None, active=False)
    }
    group_map = {
        x.username: x for x in await tg_config_service.list_group(active=False)
    }
    if not content:
        return False, content
    # 通过正则匹配@及最近一个空格或者回车之间的内容，不要@符号
    pattern = r"\@.*?\n"
    matches = re.findall(pattern, content)
    matches = list(set([str(x).strip() for x in matches]))
    matches = [x[1:] for x in matches if x]
    if not matches:
        return False, content
    check = False
    for match in matches:
        channel = channel_map.get(match)
        bot = bot_map.get(match)
        group = group_map.get(match)
        if channel and not channel.referenceable:
            main_channel = await tg_config_service.get_main_channel_by_category(
                ChannelCategory(channel.category)
            )
            content = content.replace(match, main_channel.username)
            check = True
        if bot and not bot.referenceable:
            main_bot = await tg_config_service.get_main_bot_by_category(
                BotCategory(bot.category)
            )
            content = content.replace(match, main_bot.username)
            check = True
        if group and not group.referenceable:
            main_group = await tg_config_service.get_main_group()
            content = content.replace(match, main_group.username)
            check = True
    if check:
        log.info(f"check_and_replace input:{input_content},\noutput:{content}")
    return check, content


async def refresh_bot_config():

    @async_catch_exception
    async def bot_desc(bot_id: int):
        bot = await tg_config_service.get_sender_bot_by_id(bot_id)
        desc = await bot.get_my_description()
        check, new_desc = await check_and_replace(desc.description)
        if check:
            await bot.set_my_description(new_desc)
        desc = await bot.get_my_short_description()
        check, new_desc = await check_and_replace(desc.short_description)
        if check:
            await bot.set_my_short_description(new_desc)
        return True

    bots = await tg_config_service.list_bot(BotCategory.CHAT_BOT)
    for bot in bots:
        await bot_desc(bot.bot_id)
    bots = await tg_config_service.list_bot(BotCategory.TMA)
    for bot in bots:
        await bot_desc(bot.bot_id)

    @async_catch_exception
    async def chat_desc(chat_id: int):
        sender_bot = await tg_config_service.get_main_sender_bot_by_category(
            BotCategory.HELPER
        )
        full_info = await sender_bot.get_chat(chat_id)
        if not full_info:
            return False
        check, new_desc = await check_and_replace(full_info.description)
        if check:
            await sender_bot.set_chat_description(chat_id, new_desc)
        if full_info.pinned_message and full_info.pinned_message.text:
            check, new_desc = await check_and_replace(full_info.pinned_message.text)
            if check and new_desc:
                await sender_bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=full_info.pinned_message.message_id,
                    text=new_desc,
                )

    channels = await tg_config_service.list_channel()
    for channel in channels:
        await chat_desc(channel.chat_id)
    groups = await tg_config_service.list_group()
    for group in groups:
        await chat_desc(group.chat_id)


async def refresh_group(chat_id: int):

    @async_catch_exception
    async def chat_desc(chat_id: int):
        group = await tg_config_service.get_group_config_by_chat_id(chat_id)
        if not group:
            log.error("chat_desc not found chat_id:%s", chat_id)
            return False
        bot_ids = group.helper_bot_ids
        if not bot_ids:
            log.error("chat_desc not found bot_ids:%s", bot_ids)
            return False
        bot_id = bot_ids[0]
        sender_bot = await tg_config_service.get_sender_bot_by_id(bot_id)
        log.info("sender main bot %s", sender_bot.id)
        full_info = await sender_bot.get_chat(chat_id)
        if not full_info:
            log.warning("chat_desc not found chat_id:%s", chat_id)
            return False
        check, new_desc = await check_and_replace(full_info.description)
        log.info("chat_desc check:%s,new_desc:%s", check, new_desc)
        if check:
            await sender_bot.set_chat_description(chat_id, new_desc)
        if full_info.pinned_message and full_info.pinned_message.text:
            check, new_desc = await check_and_replace(full_info.pinned_message.text)
            if check and new_desc:
                await sender_bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=full_info.pinned_message.message_id,
                    text=new_desc,
                )

    group = await tg_config_service.get_group_config_by_chat_id(chat_id)
    if not group:
        return False
    await chat_desc(group.chat_id)


async def refresh_channel_desc(chat_id: int):

    @async_catch_exception
    async def chat_desc(chat_id: int):
        channel = await tg_config_service.get_channel_by_chat_id(chat_id)
        if not channel:
            log.error("chat_desc not found chat_id:%s", chat_id)
            return False
        bot_ids = channel.helper_bot_ids
        if not bot_ids:
            log.error("chat_desc not found bot_ids:%s", bot_ids)
            return False
        bot_id = bot_ids[0]
        sender_bot = await tg_config_service.get_sender_bot_by_id(bot_id)
        log.info("sender main bot %s", sender_bot.id)
        full_info = await sender_bot.get_chat(chat_id)
        if not full_info:
            log.warning("chat_desc not found chat_id:%s", chat_id)
            return False
        check, new_desc = await check_and_replace(full_info.description)
        log.info("chat_desc check:%s,new_desc:%s", check, new_desc)
        if check:
            await sender_bot.set_chat_description(chat_id, new_desc)
        if full_info.pinned_message and full_info.pinned_message.text:
            check, new_desc = await check_and_replace(full_info.pinned_message.text)
            if check and new_desc:
                await sender_bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=full_info.pinned_message.message_id,
                    text=new_desc,
                )

    channel = await tg_config_service.get_channel_by_chat_id(chat_id)
    if not channel:
        return False
    await chat_desc(channel.chat_id)


# async def refresh_bot_config():
#     bot_list = []
#     bot_category_list = [BotCategory.CHAT_BOT, BotCategory.TMA]
#     for bot_category in bot_category_list:
#         bot_list.extend(await tg_config_service.list_bot(bot_category))
#     for bot in bot_list:
#         await _refresh_description(ResourcePlatform.BOT, bot.bot_id)

#     channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
#     for channel in channels:
#         await _refresh_description(ResourcePlatform.CHANNEL, channel.chat_id)
#         await _refresh_global_pined_message(ResourcePlatform.CHANNEL, channel.chat_id)
#     channels = await tg_config_service.list_channel(category=ChannelCategory.WELFARE)
#     for channel in channels:
#         await _refresh_description(ResourcePlatform.CHANNEL, channel.chat_id)
#         await _refresh_global_pined_message(ResourcePlatform.CHANNEL, channel.chat_id)
#     groups = await tg_config_service.list_group()
#     for group in groups:
#         await _refresh_description(ResourcePlatform.GROUP, group.chat_id)
#         await _refresh_global_pined_message(ResourcePlatform.GROUP, group.chat_id)
#     return True


# async def _refresh_description(platform: ResourcePlatform, target_id: int):
#     log.info(f"refresh_description platform:{platform},target_id:{target_id}")
#     description = await get_message_content_new(
#         platform, BotResourcePosition.DESCRIPTION, target_id
#     )
#     short_description = await get_message_content_new(
#         platform=platform,
#         position=BotResourcePosition.SHORT_DESCRIPTION,
#         target_id=target_id,
#     )
#     if not description and not short_description:
#         return
#     if platform == ResourcePlatform.BOT:
#         sender = await tg_config_service.get_sender_bot_by_id(target_id)
#         if description:
#             await sender.set_my_description(description)
#         if short_description:
#             await sender.set_my_short_description(short_description)
#     else:
#         sender = await tg_config_service.get_main_sender_bot_by_category(
#             BotCategory.HELPER
#         )
#         if description:
#             await sender.set_chat_description(target_id, description)


# async def _refresh_global_pined_message(platform: ResourcePlatform, target_id: int):
#     log.info(f"refresh_global_pined_message platform:{platform},target_id:{target_id}")
#     pined_message = await GlobalPinedMessage.filter(
#         chat_id=target_id, position=BotResourcePosition.PINED_MESSAGE.value
#     ).first()
#     pined_content = await get_message_content_new(
#         platform=platform,
#         position=BotResourcePosition.PINED_MESSAGE,
#         target_id=target_id,
#         default_resource=False,
#     )
#     if not pined_content:
#         return
#     helper = await tg_config_service.get_main_sender_bot_by_category(BotCategory.HELPER)
#     if not pined_message:
#         ret = await helper.send_message(chat_id=target_id, text=pined_content)
#         await helper.pin_chat_message(chat_id=target_id, message_id=ret.message_id)
#         pined_message = GlobalPinedMessage(
#             chat_id=target_id,
#             position=BotResourcePosition.PINED_MESSAGE.value,
#             message_id=ret.message_id,
#         )
#         await pined_message.save()
#         return
#     full_info = await helper.get_chat(target_id)
#     if full_info and full_info.pinned_message:
#         pined_msg_content = full_info.pinned_message.text
#         if pined_content == pined_msg_content:
#             return
#     if pined_content == pined_msg_content:
#         return
#     await helper.edit_message_text(
#         chat_id=target_id, message_id=pined_message.message_id, text=pined_content
#     )


async def refresh_channel_message(chat_id: int):
    channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
    channels = [x for x in channels if x.chat_id == chat_id]
    if not channels:
        return
    channel = channels[0]
    try:
        broadcast_list = await role_broadcast_history_dao.list_by_chat_id(
            channel.chat_id
        )
        if not broadcast_list:
            return
        for broadcast in broadcast_list:
            log.info(
                "start refresh_channel_message chat_id:%s,message_id:%s",
                chat_id,
                broadcast.message_id,
            )
            try:
                format_content = await format_content_replace(
                    CHANNEL_CONTENT, 0, broadcast.role_id
                )
                help_bot_id = channel.helper_bot_ids[0]
                helper = await tg_config_service.get_sender_bot_by_id(help_bot_id)
                role = await role_loader_service.get_by_id(broadcast.role_id)
                format_content = format_content.replace(
                    BotReplace.CHANNEL_ID.value, str(channel.channel_id)
                )
                safe_url = role_util.get_safe_image_url(role)
                if channel.nsfw:
                    safe_url = role.role_avatar
                image_bytes = await request_util.get_bytes(safe_url)
                if not image_bytes:
                    continue
                image_byte_arr = image_util.random_noise_to_image(image_bytes)
                input_file = BufferedInputFile(
                    image_byte_arr.getvalue(), filename=f"result.png"
                )
                await helper.edit_message_media(
                    chat_id=chat_id,
                    message_id=broadcast.message_id,
                    media=types.InputMediaPhoto(
                        media=input_file,
                        caption=format_content,
                        parse_mode=ParseMode.MARKDOWN_V2,
                    ),
                )
                log.info(
                    "refresh_channel_message success chat_id:%s,message_id:%s",
                    chat_id,
                    broadcast.message_id,
                )
            except Exception as e:
                log.error(
                    f"refresh_channel_message error,chat_id:{chat_id},message_id:{broadcast.message_id},error:{e}"
                )
    except Exception as e:
        log.error(f"refresh_channel_message error:{e}")


async def refresh_old_channel_message(chat_id: int):
    channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
    channels = [x for x in channels if x.chat_id == chat_id]
    if not channels:
        return
    channel = channels[0]
    try:
        broadcast_list = await role_broadcast_history_dao.list_old_by_chat_id(
            channel.chat_id
        )
        if not broadcast_list:
            return
        for broadcast in broadcast_list:
            message_id = int(broadcast.get("message_id", 0))
            role_id = int(broadcast.get("role_id", 0))
            if not message_id or not role_id:
                continue
            log.info(
                "start refresh_channel_message chat_id:%s,message_id:%s",
                chat_id,
                message_id,
            )
            try:
                format_content = await format_content_replace(
                    CHANNEL_CONTENT, 0, role_id
                )
                help_bot_id = channel.helper_bot_ids[0]
                helper = await tg_config_service.get_sender_bot_by_id(help_bot_id)
                role = await role_loader_service.get_by_id(role_id)
                format_content = format_content.replace(
                    BotReplace.CHANNEL_ID.value, str(channel.channel_id)
                )
                safe_url = role_util.get_safe_image_url(role)
                if channel.nsfw:
                    safe_url = role.role_avatar
                image_bytes = await request_util.get_bytes(safe_url)
                if not image_bytes:
                    continue
                image_byte_arr = image_util.random_noise_to_image(image_bytes)
                input_file = BufferedInputFile(
                    image_byte_arr.getvalue(), filename=f"result.png"
                )
                await helper.edit_message_media(
                    chat_id=chat_id,
                    message_id=message_id,
                    media=types.InputMediaPhoto(
                        media=input_file,
                        caption=format_content,
                        parse_mode=ParseMode.MARKDOWN_V2,
                    ),
                )
                log.info(
                    "refresh_channel_message success chat_id:%s,message_id:%s",
                    chat_id,
                    message_id,
                )
            except Exception as e:
                log.error(
                    f"refresh_channel_message error,chat_id:{chat_id},message_id:{message_id},error:{e}"
                )
    except Exception as e:
        log.error(f"refresh_channel_message error:{e}")


async def refresh_channel_message_alone(chat_id: int, message_id: int):
    log.info(f"refresh_channel_message_alone chat_id:{chat_id},message_id:{message_id}")
    channels = await tg_config_service.list_channel(category=ChannelCategory.ROLE)
    channels = [x for x in channels if x.chat_id == chat_id]
    if not channels:
        return
    channel_config = channels[0]
    try:
        broadcast = await role_broadcast_history_dao.get_by_message(chat_id, message_id)
        if not broadcast:
            log.info(
                f"refresh_channel_message_alone not found chat_id:{chat_id},message_id:{message_id}"
            )
            return
        help_bot_id = channel_config.helper_bot_ids[0]
        helper = await tg_config_service.get_sender_bot_by_id(help_bot_id)
        format_content = await format_content_replace(
            CHANNEL_CONTENT, 0, broadcast.role_id
        )
        role = await role_loader_service.get_by_id(broadcast.role_id)
        format_content = format_content.replace(
            BotReplace.CHANNEL_ID.value, str(channel_config.channel_id)
        )
        safe_url = role_util.get_safe_image_url(role)
        if channel_config.nsfw:
            safe_url = role.role_avatar
        image_bytes = await request_util.get_bytes(safe_url)
        if not image_bytes:
            return
        image_byte_arr = image_util.random_noise_to_image(image_bytes)
        input_file = BufferedInputFile(
            image_byte_arr.getvalue(), filename=f"result.png"
        )
        # await bot_config_service.update_photo(
        #     chat_id, broadcast.message_id, input_file, format_content
        # )
        await helper.edit_message_media(
            chat_id=chat_id,
            message_id=broadcast.message_id,
            media=types.InputMediaPhoto(
                media=input_file,
                caption=format_content,
                parse_mode=ParseMode.MARKDOWN_V2,
            ),
        )
        log.info(
            f"refresh_channel_message_alone success chat_id:{chat_id},message_id:{message_id}"
        )
    except Exception as e:
        log.error(f"refresh_channel_message error:{e}")


# async def refresh_push_message(bot_id: int):
#     bot_config = await bot_config_service.get_bot_by_id(bot_id)
#     if not bot_config:
#         return
#     try:
#         # message_template = await get_message_template_new()
#         message_template = await get_message_template(
#             bot_id, BotType(bot_config.type), BotResourcePosition.PUSH_MESSAGE, True
#         )
#         if not message_template:
#             return None
#         sender_bot = await bot_config_service.get_helper_sender_bot()
#         await sender_bot.send_message(
#             chat_id=bot_id,
#             text=message_template.tips,
#             parse_mode=ParseMode.HTML,
#             reply_markup=message_template.as_markup(),
#         )
#     except Exception as e:
#         log.error(f"refresh_channel_message error:{e}")
#     finally:
#         bot_config.push_message = True
#         await bot_config.save()


async def push_role_channel_message(
    chat_id: int,
    role_id: int,
    content_template: str,
    ai_channel: bool = False,
):
    log.info(f"push_role_message chat_id:{chat_id},role_id:{role_id}")
    try:
        channel_config = await tg_config_service.get_channel_by_chat_id(chat_id)
        if not channel_config:
            return False
        if ai_channel:
            has_send = await role_broadcast_history_dao.get_by_chat_and_role(
                chat_id, role_id
            )
            if has_send:
                log.info(
                    f"ai channel check, chat_id: {chat_id}, role_id:{role_id} already send, message_id: {has_send.message_id}"
                )
                return False
        role = await role_loader_service.get_by_id(role_id)
        format_content = await format_content_replace(
            content_template, 0, role_id, channel_config.language
        )
        format_content = format_content.replace(
            BotReplace.CHANNEL_ID.value, str(channel_config.channel_id)
        )
        safe_url = role_util.get_safe_image_url(role)
        if channel_config.nsfw:
            safe_url = role.role_avatar
        image_bytes = await request_util.get_bytes(safe_url)
        if not image_bytes:
            return False
        image_byte_arr = image_util.random_noise_to_image(image_bytes)
        input_file = BufferedInputFile(
            image_byte_arr.getvalue(), filename=f"result.png"
        )
        sender = await tg_config_service.get_channel_sender(channel_config.chat_id)
        message = await sender.send_photo(
            chat_id=chat_id,
            photo=input_file,
            caption=format_content,
            parse_mode=ParseMode.MARKDOWN_V2,
        )
        if not message:
            log.error(f"push_role_message failed chat_id:{chat_id},role_id:{role_id}")
            return False

        log.info(
            f"push_role_message success chat_id:{chat_id},role:{role_id} message:{message.message_id}"
        )
        history = RoleBroadcastHistory(
            role_id=role_id,
            chat_id=chat_id,
            photo_file_id=message.photo[0].file_id if message.photo else "",
            message_id=message.message_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        await role_broadcast_history_dao.insert(history)

        await resend_channel_bottom_message(chat_id)
        if ai_channel:
            post_ai_channel_publish(
                role,
                channel_config.linked_group,
                message.message_id,
                format_content,
                channel_config.channel_id,
            )
        return True
    except Exception as e:
        log.error(
            f"push_role_message error chat_id:{chat_id},role_id:{role_id} error", e
        )
    return False


async def push_role_group_message(
    chat_id: int, role_id: int, content_template: str, resend_bottom: bool = True
):
    log.info(f"push_role_message chat_id:{chat_id},role_id:{role_id}")
    try:
        # has_send = await role_broadcast_history_dao.get_by_chat_and_role(
        #     chat_id, role_id
        # )
        # if has_send:
        #     log.info(f"PushRoleGroup already send,chat_id:{chat_id},role_id:{role_id}")
        #     return False
        config = await tg_config_service.get_group_config_by_chat_id(chat_id)
        if not config:
            return False
        role = await role_loader_service.get_by_id(role_id)
        format_content = await format_content_replace(content_template, 0, role_id)
        format_content = format_content.replace(BotReplace.CHANNEL_ID.value, str(0))
        safe_url = role_util.get_safe_image_url(role)
        image_bytes = await request_util.get_bytes(safe_url)
        if not image_bytes:
            return False
        image_byte_arr = image_util.random_noise_to_image(image_bytes)
        input_file = BufferedInputFile(
            image_byte_arr.getvalue(), filename=f"result.png"
        )
        sender = await tg_config_service.get_group_sender(config.chat_id)
        message = await sender.send_photo(
            chat_id=chat_id,
            photo=input_file,
            caption=format_content,
            parse_mode=ParseMode.MARKDOWN_V2,
        )
        if not message:
            log.error(f"push_role_message failed chat_id:{chat_id},role_id:{role_id}")
            return False

        log.info(
            f"push_role_message success chat_id:{chat_id},role:{role_id} message:{message.message_id}"
        )
        history = RoleBroadcastHistory(
            role_id=role_id,
            chat_id=chat_id,
            photo_file_id=message.photo[0].file_id if message.photo else "",
            message_id=message.message_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        await role_broadcast_history_dao.insert(history)
        if resend_bottom:
            await resend_bottom_message(chat_id, sender.id, ROLE_GROUP_BOTTOM_MESSAGE)
        return True
    except Exception as e:
        log.error(
            f"push_role_message error chat_id:{chat_id},role_id:{role_id} error:{e}"
        )
    return False


def post_ai_channel_publish(
    role: RoleConfig,
    linked_group_id: str,
    message_id: int,
    content: str,
    channel_id: int,
):
    data = {
        "role_id": role.id,
        "message_id": message_id,
        "role_name": role.role_name,
        "content": content,
        "photo": role.role_avatar,
        "channel_id": channel_id,
    }
    chat_id = linked_group_id
    rk = f"role_br_message_{chat_id}:{message_id}"
    logging.info(f"post_ai_channel_publish set redis key {rk}")
    redis_client.set(rk, json.dumps(data), ex=timedelta(hours=1))


REGISTER_SOURCE_MAP = {
    "CHAT_BOT": 7023712364,
    "CHAT_BOT_1": 7794866380,
    "CHAT_BOT_2": 7124316437,
    "TMA": 7428622943,
    "TMA_1": 7622818306,
    "TMA_2": 7733073744,
    "TMA_10086": 8196109543,
    "TMA_666": 7982407404,
}


async def send_user_template_message(
    user: User, message_template: MessageTemplate, auto_deleted: bool = False
):
    user_active_list = await UserBotActive.filter(user_id=user.id).all()
    user_active_list.sort(key=lambda x: x.last_active_at, reverse=True)
    tg_info = await user_service.get_tg_user_by_id(user_id=user.id)
    if not tg_info:
        return None, None

    register_bot_id = REGISTER_SOURCE_MAP.get(user.register_source, 0)
    active_bot_ids = [x.bot_id for x in user_active_list]
    if register_bot_id and register_bot_id not in active_bot_ids:
        active_bot_ids.append(register_bot_id)

    bot_configs = await tg_config_service.list_bots_by_ids(active_bot_ids)
    bot_config_map = {x.bot_id: x for x in bot_configs}

    for bot_id in active_bot_ids:
        bot_config = bot_config_map.get(bot_id, None)
        if not bot_config:
            continue
        try:
            sender = await tg_config_service.get_sender_bot_by_id(bot_id)
            msg = await sender.send_message(
                chat_id=tg_info.tg_id,
                text=message_template.tips,
                parse_mode=message_template.parse_mode,
                reply_markup=message_template.as_markup(),
            )
            if msg and auto_deleted:
                await tg_message_service.add_deleted_message(msg, bot_id=sender.id)
            return sender, msg
        except Exception as e:
            tips = message_template.tips[0:5]
            log.warning(
                f"send_user_template_message error: bot:{sender.id}, chat_id:{tg_info.tg_id}, text: {tips}, error:{e}"
            )
    return None, None


async def send_user_history(user: User, html_file, txt_file):
    tg_info = await user_service.get_tg_user_by_id(user.id)
    if not tg_info:
        log.warning(f"send_user_history user not found:{user.id}")
        return False
    tg_id = tg_info.tg_id
    active_bots = await user_active_service.list_active_bots(user)
    for bot in active_bots:
        try:
            sender = await tg_config_service.get_sender_bot_by_id(bot.bot_id)
            if txt_file:
                await sender.send_document(tg_id, txt_file)
            if html_file:
                await sender.send_document(tg_id, html_file)
            log.info(f"send_user_history success:{user.id},bot_id:{bot.bot_id}")
            return True
        except Exception as e:
            log.warning(f"send_user_history error:{e}")
            return False
    log.error(f"send_user_history failed:{user.id}")
    return False


async def send_by_tips(
    bot: Bot,
    chat_id: int,
    tips: str,
    buttons: list[Button] = [],
    format: bool = False,
    auto_deleted: bool = False,
) -> bool | None:
    template = MessageTemplate(
        tips=tips,
        buttons=buttons,
    )
    if format:
        template = await format_template_replace(template)
    return await send_by_template(
        bot=bot,
        chat_id=chat_id,
        message_template=template,
        auto_deleted=auto_deleted,
    )


@async_ignore_catch_exception
async def send_by_template(
    bot: Bot,
    chat_id: int,
    message_template: MessageTemplate,
    auto_deleted: bool = False,
) -> bool | None:
    message = await bot.send_message(
        chat_id=chat_id,
        text=message_template.tips,
        parse_mode=ParseMode.HTML,
        reply_markup=message_template.as_markup(),
    )
    if not message:
        return None
    if auto_deleted:
        await tg_message_service.add_deleted_message(message)
    return True


async def del_channel_message(chat_id: int, message_id: int):
    channel = await tg_config_service.get_channel_by_chat_id(chat_id)
    channel_group = await tg_config_service.get_group_config_by_chat_id(chat_id)
    if not channel and not channel_group:
        return False
    try:
        helper = None
        if channel:
            helper = await tg_config_service.get_channel_sender(chat_id)
        else:
            helper = await tg_config_service.get_group_sender(chat_id)
        if not helper:
            return False
        ret = await helper.delete_message(chat_id=chat_id, message_id=message_id)
        log.info(f"del_message chat_id:{chat_id},message_id:{message_id}, result:{ret}")
        return ret
    except Exception as e:
        log.warning(
            f"del_channel_message error chat_id:{chat_id},message_id:{message_id}, error:{e}"
        )
    return False


@async_ignore_catch_exception
async def del_channel_messages(chat_id: int, message_ids: list[int]):
    channel = await tg_config_service.get_channel_by_chat_id(chat_id)
    channel_group = await tg_config_service.get_group_config_by_chat_id(chat_id)
    if not channel and not channel_group:
        return False
    username = channel.username if channel else channel_group.username
    helper = None
    if channel:
        helper = await tg_config_service.get_channel_sender(chat_id)
    else:
        helper = await tg_config_service.get_group_sender(chat_id)
    if not helper:
        return False
    ret = await helper.delete_messages(chat_id=chat_id, message_ids=message_ids)
    log.info(
        f"del_message chat_id:{chat_id},channel_name:{username},message_ids:{message_ids}, result:{ret}"
    )
    return True

skip_queue_text_template_str = """⚠️ 免费模式排队提醒

当前免费聊天模式使用量大，需排队等待等待❗️❗️
前方排队{person_ahead}人，预计排队{left_wait_time}秒

说明：聊天消耗免费聊天模式次数，使用的算力是共享的，稳定性差，模型慢

建议：
1、切换【其他聊天模式】继续聊天；
2、充值9.9元，免费模式聊天不排队，持续7天"""

async def send_queue_message(bot: Bot, chat_id: int, person_ahead: int, left_wait_time: int):
    text = skip_queue_text_template_str.format(person_ahead=person_ahead, left_wait_time=left_wait_time)
    product = await recharge_service.get_recharge_product(SKIP_QUEUE_RECHARGE_PRODUCT_ID)
    if not product:
        return None
    builder = InlineKeyboardBuilder()
    text_pre = f"{product.title}-{product.original_price_desc}"
    builder.button(text=f"{text_pre}-微信", callback_data=RechargeProductPayTypeCallback(pid=SKIP_QUEUE_RECHARGE_PRODUCT_ID, type='wechat'))
    builder.button(text=f"{text_pre}-支付宝", callback_data=RechargeProductPayTypeCallback(pid=SKIP_QUEUE_RECHARGE_PRODUCT_ID, type='alipay'))
    builder.button(text=f"{text_pre}-信用卡", callback_data=RechargeProductPayTypeCallback(pid=SKIP_QUEUE_RECHARGE_PRODUCT_ID, type='stripe'))
    builder.button(text=f"{text_pre}-Telegram Star 支付", callback_data=RechargeProductPayTypeCallback(pid=SKIP_QUEUE_RECHARGE_PRODUCT_ID, type='star'))
    builder.adjust(1)

    message =await bot.send_message(
            chat_id=chat_id,
            text=text,
            parse_mode=ParseMode.HTML,
            reply_markup=builder.as_markup(),
        )
    if message:
        await tg_message_service.add_deleted_message(
            message,
            bot_id=bot.id,
            expire_delta=timedelta(minutes=3),
        )

