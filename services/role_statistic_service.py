from datetime import datetime
import logging
import math
from common.common_constant import ChatModeType
from common.role_model import CardDetail
from persistence.models.models import (
    FavoriteStats,
    GroupStatistic,
    LikeStats,
    RoleAudit,
    RoleStatistic,
    UserFavoriteRecord,
)


logger = logging.getLogger(__name__)


async def user_role_statistic(role_ids: list[int]) -> dict[int, int]:
    role_statistics = await RoleStatistic.filter(role_id__in=role_ids).all()
    popular_map = {
        x.role_id: x.total_conversation * 100
        + x.total_message * 10
        + x.total_diamond
        + 1
        for x in role_statistics
    }
    return popular_map


async def user_group_statistic(group_ids: list[int]) -> dict[int, int]:
    group_list = await GroupStatistic.filter(group_id__in=group_ids).all()
    popular_map = {
        x.group_id: x.total_conversation * 100
        + x.total_message * 10
        + x.total_diamond
        + 1
        for x in group_list
    }
    return popular_map


async def role_all_statistic() -> dict[int, RoleStatistic]:
    role_statistics = await RoleStatistic.all()
    return {x.role_id: x for x in role_statistics}


async def maps_statistic(role_ids: list[int]) -> dict[int, RoleStatistic]:
    role_statistics = await RoleStatistic.filter(role_id__in=role_ids).all()
    return {x.role_id: x for x in role_statistics}


async def role_hot_v1(role_ids: list[int]) -> dict[int, int]:
    role_statistics = {
        x.role_id: x for x in await RoleStatistic.filter(role_id__in=role_ids).all()
    }
    ret = {}
    for role_id in role_ids:
        stat = role_statistics.get(role_id)
        if not stat:
            continue
        ret[role_id] = stat.hot_v1
    return ret


async def role_hot_all() -> dict[int, int]:
    role_statistics = await RoleStatistic.all()
    return {x.role_id: x.hot_v1 for x in role_statistics}


async def group_hot_all():
    all_list = await GroupStatistic.all()
    return {x.group_id: x.hot_v1 for x in all_list}


async def group_hot_v1(group_ids: list[int]) -> dict[int, int]:
    all_list = await GroupStatistic.all()
    return {x.group_id: x.hot_v1 for x in all_list if x.group_id in group_ids}


async def group_all_statistic():
    all_list = await GroupStatistic.all()
    return {x.group_id: x for x in all_list}


async def card_h24_hk_count():
    role_statistics = await RoleStatistic.all()
    group_statistics = await GroupStatistic.all()
    ret = {}
    for x in role_statistics:
        key = CardDetail.key(ChatModeType.SINGLE.value, x.role_id)
        ret[key] = x.h24_haiku_count
    for x in group_statistics:
        key = CardDetail.key(ChatModeType.GROUP.value, x.group_id)
        ret[key] = x.h24_haiku_count
    return ret


async def card_hot_count():
    role_statistics = await RoleStatistic.all()
    group_statistics = await GroupStatistic.all()
    ret = {}
    for x in role_statistics:
        key = CardDetail.key(ChatModeType.SINGLE.value, x.role_id)
        ret[key] = x.hot_v1
    for x in group_statistics:
        key = CardDetail.key(ChatModeType.GROUP.value, x.group_id)
        ret[key] = x.hot_v1
    return ret


async def like_count():
    like_stats = await LikeStats.filter(
        mode_type=ChatModeType.SINGLE.value,
    ).all()
    return {x.mode_target_id: x.like_count for x in like_stats}


async def favorite_count():
    fav_stats = await FavoriteStats.filter(
        mode_type=ChatModeType.SINGLE.value,
    ).all()
    return {x.mode_target_id: x.favorite_count for x in fav_stats}


async def role_update_time():
    audit_list = await RoleAudit.filter(
        mode_type=ChatModeType.SINGLE.value,
        open_role_id__gt=0,
    ).filter()
    ret = {}
    for x in audit_list:
        ret[x.open_role_id] = x.published_at.timestamp()
    return ret


async def role_daily_hot():
    role_statistics = (
        await RoleStatistic.filter(
            daily_hot__gt=0,
        )
        .only("role_id", "daily_hot")
        .all()
    )
    ret = {}
    for x in role_statistics:
        ret[x.role_id] = x.daily_hot
    return ret


async def role_weekly_hot():
    role_statistics = (
        await RoleStatistic.filter(
            weekly_hot__gt=0,
        )
        .only("role_id", "weekly_hot")
        .all()
    )
    ret = {}
    for x in role_statistics:
        ret[x.role_id] = x.weekly_hot
    return ret


async def role_monthly_hot():
    role_statistics = (
        await RoleStatistic.filter(
            monthly_hot__gt=0,
        )
        .only("role_id", "monthly_hot")
        .all()
    )
    ret = {}
    for x in role_statistics:
        ret[x.role_id] = x.monthly_hot
    return ret
