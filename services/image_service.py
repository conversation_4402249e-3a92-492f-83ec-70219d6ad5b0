import asyncio
from datetime import datetime
import logging
import uuid
from common.common_constant import (
    ImageAspectRatio,
    ImageQuality,
    ImageRecordStatus,
    ImageStyle,
    ProductType,
)
from persistence.models.models import UserGenerateImageRecord
from services import account_service, product_service
from services.third import image_server_service
from utils import exception_util, request_util


async def user_generate_image(
    user_id: int,
    prompt: str,
    image_style: ImageStyle,
    image_quality: ImageQuality,
    image_aspect_ratio: ImageAspectRatio,
) -> int:

    record = UserGenerateImageRecord(
        user_id=user_id,
        prompt=prompt,
        image_style=image_style.value,
        image_quality=image_quality.value,
        image_aspect_ratio=image_aspect_ratio.value,
        commit_id=uuid.uuid4().hex,
        status=ImageRecordStatus.SUBMIT.value,
        generate_start_time=int(datetime.now().timestamp()),
    )
    await record.save()
    asyncio.create_task(run_generate_image(record.id, record.commit_id, 1))
    return record.id


async def run_generate_image(record_id: int, commit_id: str, retry_count: int = 0):
    record = await UserGenerateImageRecord.filter(id=record_id).first()
    if not record:
        raise exception_util.verify_exception(message="记录不存在")
    logging.info(f"RunGenerateImage: record_id={record_id}, commit_id={commit_id}")
    update = (
        await UserGenerateImageRecord.select_for_update()
        .filter(
            id=record_id,
            commit_id=commit_id,
            deleted=False,
            status=ImageRecordStatus.SUBMIT.value,
        )
        .update(
            status=ImageRecordStatus.GENERATING.value,
            retry_count=record.retry_count + 1,
        )
    )
    if not update or not record:
        logging.error(
            f"RunGenerateImage: record_id={record_id} not found or already processed"
        )
        raise exception_util.verify_exception(message="记录不存在")
    image_quality = ImageQuality(record.image_quality)
    product = await product_service.get_online_product_by_type_and_mid(
        ProductType.GENERATE_IMAGE, image_quality.value
    )
    if not product:
        raise exception_util.verify_exception(message="未找到对应的产品")
    resolution = f"{record.image_quality}_{record.image_aspect_ratio}"
    response = await image_server_service.generate_image_request(
        style=record.image_style,
        prompt=record.prompt,
        resolution=resolution,
        request_id=record.commit_id,
    )
    if response.success():
        await UserGenerateImageRecord.filter(id=record_id).update(
            status=ImageRecordStatus.SUCCESS.value,
            image_url=response.image_url,
            generate_end_time=int(datetime.now().timestamp()),
            image_width=response.width if response.width else 0,
            image_height=response.height if response.height else 0,
        )
        await account_service.AccountService.create_pay_order(
            user_id=record.user_id, product=product
        )
        return True
    if retry_count > 0:
        await UserGenerateImageRecord.filter(id=record_id).update(
            status=ImageRecordStatus.SUBMIT.value,
            error_message=response.message,
        )
        await asyncio.sleep(2)  # 等待2秒后重试
        await run_generate_image(
            record_id=record_id,
            commit_id=commit_id,
            retry_count=retry_count - 1,
        )
    await UserGenerateImageRecord.filter(id=record_id).update(
        status=ImageRecordStatus.FAILED.value,
        error_message=response.message,
    )
    return False


async def check_generate_image_status(
    user_id: int, record_id: int
) -> UserGenerateImageRecord:
    record = await UserGenerateImageRecord.filter(id=record_id, user_id=user_id).first()
    if not record or record.deleted:
        raise exception_util.verify_exception(message="记录不存在")
    return record


async def user_records(user_id: int, offset: int, limit: int):
    records = (
        await UserGenerateImageRecord.filter(user_id=user_id, deleted=False)
        .order_by("-id")
        .only("id")
        .all()
    )
    ids = [record.id for record in records]
    count = len(ids)
    if offset >= count:
        return count, []
    if offset + limit > count:
        ids = ids[offset:]
    else:
        ids = ids[offset : offset + limit]
    records = await UserGenerateImageRecord.filter(id__in=ids).order_by("-id").all()
    now_time = int(datetime.now().timestamp())
    for record in records:
        if record.status != ImageRecordStatus.GENERATING.value:
            continue
        diff_time = now_time - record.generate_start_time
        if diff_time > 60 * 2:
            # 超过2分钟未完成,则认为任务失败
            await UserGenerateImageRecord.filter(id=record.id).update(
                status=ImageRecordStatus.FAILED.value,
                error_message="任务超时",
                generate_end_time=now_time,
            )
            record.status = ImageRecordStatus.FAILED.value
            record.error_message = "任务超时"
    return count, records


async def delete_user_generate_image_record(user_id: int, record_id: int):
    record = await UserGenerateImageRecord.filter(id=record_id, user_id=user_id).first()
    if not record or record.deleted:
        raise exception_util.verify_exception(message="记录不存在")
    if record.status == ImageRecordStatus.GENERATING.value:
        raise exception_util.verify_exception(
            message="正在生成中的记录不能删除,请稍后再试"
        )
    await UserGenerateImageRecord.filter(id=record_id).update(deleted=True)


async def update_like_status(record_id: int, like_status: int, user_id: int):
    record = await UserGenerateImageRecord.filter(id=record_id, user_id=user_id).first()
    if not record or record.deleted:
        raise exception_util.verify_exception(message="记录不存在")

    await UserGenerateImageRecord.filter(id=record_id).update(like_status=like_status)


async def exist_generating_image(user_id: int) -> bool:
    record = await UserGenerateImageRecord.filter(
        user_id=user_id,
        status=ImageRecordStatus.GENERATING.value,
        deleted=False,
    ).first()
    return bool(record)
