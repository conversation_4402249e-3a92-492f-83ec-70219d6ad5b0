from datetime import UTC, datetime, timedelta
import logging
from typing import List, <PERSON><PERSON>

from pydantic import BaseModel
from common.common_constant import RechargeRouteStrategy
from persistence.redis_client import redis_client
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeChannelConfig,
    RechargeChannelStats,
    RechargeChannelControl,
    RechargeChannelWhitelist,
    RechargeOrder,
    RechargeStatusEnum
)

all_channels = [
    RechargeChannelEnum.TMPAY, 
    # RechargeChannelEnum.FFPAY,
    # RechargeChannelEnum.QSZF,
    RechargeChannelEnum.SJZF,
    RechargeChannelEnum.JLBZF,
    RechargeChannelEnum.XJTZF,
    RechargeChannelEnum.TTZF,
    # RechargeChannelEnum.SDFKW_API,
]


class RechargeChannelRatio(BaseModel):
    channel: str
    ratio: int
    current: int

async def get_configs() -> list[RechargeChannelConfig]:
    return await RechargeChannelConfig.all()

async def add_config(config: RechargeChannelConfig):
    await config.save()

async def update_config(config: RechargeChannelConfig): 
    await config.save()

async def get_by_product_id(product_id: str) -> RechargeChannelConfig | None:
    return await RechargeChannelConfig.get_or_none(recharge_product_id=product_id)

# 定义通道状态
CHANNEL_STATUS_AVAILABLE = "available"
CHANNEL_STATUS_UNAVAILABLE = "unavailable"
CHANNEL_STATUS_RETRIABLE = "retriable"

# Redis键前缀
REDIS_CHANNEL_STATUS_KEY_PREFIX = "recharge:channel:status:"
REDIS_CHANNEL_RETRY_TIME_KEY_PREFIX = "recharge:channel:retry_time:"

async def get_current_mode() -> RechargeRouteStrategy:
    cm = redis_client.get("recharge_route_strategy")
    if not cm:
        return RechargeRouteStrategy.MANUAL
    cm = cm.decode()
    return RechargeRouteStrategy(cm)

def get_all_can_use_channels() -> list[RechargeChannelEnum]:
    channels = []
    for channel in all_channels:
        status = get_channel_status(channel)
        if status != CHANNEL_STATUS_UNAVAILABLE:
            channels.append(channel)
    if len(channels) == 0:
        channels = all_channels
    return channels

def set_channel_status(channel: str, status: str):
    key = f"{REDIS_CHANNEL_STATUS_KEY_PREFIX}{channel}"
    redis_client.set(key, status)

def get_channel_status(channel: str):
    key = f"{REDIS_CHANNEL_STATUS_KEY_PREFIX}{channel}"
    status = redis_client.get(key)
    return status.decode('utf-8') if status else CHANNEL_STATUS_AVAILABLE

def set_channel_retry_time(channel: str, retry_time: datetime):
    key = f"{REDIS_CHANNEL_RETRY_TIME_KEY_PREFIX}{channel}"
    timestamp = retry_time.timestamp()
    redis_client.set(key, str(timestamp))

def get_channel_retry_time(channel: str):
    key = f"{REDIS_CHANNEL_RETRY_TIME_KEY_PREFIX}{channel}"
    timestamp = redis_client.get(key)
    if timestamp:
        return datetime.fromtimestamp(float(timestamp.decode('utf-8')), UTC)
    return None

async def update_channel_success_rate(channel: str):
    """更新渠道成功率统计 - 按支付类型分别统计"""
    two_four_hours_ago = datetime.now(UTC) - timedelta(hours=2)

    pay_types_to_stats = ['wechat', 'alipay', 'ALL']

    updated_stats = []

    for pay_type in pay_types_to_stats:
        if pay_type == 'ALL':
            # 统计所有订单
            total_orders_list = await RechargeOrder.filter(
                recharge_channel=channel,
                created_at__gte=two_four_hours_ago,
                pay_fee__gt=0
            ).order_by('-id').limit(12)

        else:
            # 统计特定支付类型的订单
            total_orders_list = await RechargeOrder.filter(
                recharge_channel=channel,
                pay_type=pay_type,
                created_at__gte=two_four_hours_ago,
                pay_fee__gt=0
            ).order_by('-id').limit(12)

        success_orders = len([o for o in total_orders_list if o.status == RechargeStatusEnum.SUCCEED])
        total_orders = len(total_orders_list)
        success_rate = (success_orders / total_orders * 100) if total_orders > 0 else 0.0

        # 更新或创建统计记录
        stats, created = await RechargeChannelStats.get_or_create(
            channel=channel,
            pay_type=pay_type,
            defaults={
                'total_orders': total_orders,
                'success_orders': success_orders,
                'success_rate': success_rate,
                'pay_type': pay_type
            }
        )

        if not created:
            stats.total_orders = total_orders
            stats.success_orders = success_orders
            stats.success_rate = success_rate
            stats.pay_type = pay_type
            await stats.save()

        updated_stats.append(stats)

    return updated_stats


async def get_manual_strategy_channels(pay_type: str, amount: int): 
    return await get_enabled_controls(pay_type, amount)

async def get_channel_queues_for_routing(queue_type: str, pay_type: str = 'alipay', amount: int = 0) -> Tuple[List[str], List[str]]:
    specific_stats = await RechargeChannelStats.filter(pay_type=pay_type).all()

    stats_map = {}
    for stat in specific_stats:
        stats_map[stat.channel] = stat

    enabled_channels = await get_enabled_channels_from_controls(pay_type, amount)

    # 为启用的渠道构建统计数据
    channel_stats = []
    for channel in enabled_channels:
        if channel in stats_map:
            stat = stats_map[channel]
            channel_stats.append((channel.value, stat.success_rate))
        else:
            # 新渠道默认成功率为50%
            channel_stats.append((channel.value, 50.0))

    # 按成功率从高到低排序
    channel_stats.sort(key=lambda x: x[1], reverse=True)

    # 构建第一个队列：成功率 >= 40% 的渠道
    high_success_channels = [ch for ch, rate in channel_stats if rate >= 40.0]

    # 构建第二个队列：从排名第二位开始，包括成功率 < 40% 的渠道
    low_success_channels = [ch for ch, rate in channel_stats if rate < 40.0]
    if len(high_success_channels) > 1:
        # 从第二位开始的高成功率渠道 + 低成功率渠道
        secondary_queue = high_success_channels[1:] + low_success_channels
    else:
        secondary_queue = low_success_channels

    if queue_type == 'type1':
        return high_success_channels, secondary_queue
    else:  # type2
        return secondary_queue, high_success_channels


async def filter_channels_by_user_history(channels: List[str], user_id: int) -> List[str]:
    from services import recharge_service

    unpaid_channels = await recharge_service.get_user_unpaid_channels_in_last_10_minutes(user_id)

    # 过滤掉用户近期未支付的渠道
    filtered_channels = [ch for ch in channels if ch not in unpaid_channels]

    # 如果所有渠道都被过滤掉了，返回原始列表（避免无渠道可用）
    return filtered_channels if filtered_channels else channels

async def get_channel_controls() -> List[RechargeChannelControl]:
    """获取所有渠道控制配置"""
    return await RechargeChannelControl.all()


async def add_channel_control(control: RechargeChannelControl) -> RechargeChannelControl:
    """添加渠道控制配置"""
    await control.save()
    return control


async def update_channel_control(control: RechargeChannelControl) -> RechargeChannelControl:
    """更新渠道控制配置"""
    await control.save()
    return control


async def delete_channel_control(control_id: int) -> bool:
    """删除渠道控制配置"""
    try:
        control = await RechargeChannelControl.get(id=control_id)
        await control.delete()
        return True
    except:
        return False


async def get_channel_control_by_id(control_id: int) -> RechargeChannelControl | None:
    """根据ID获取渠道控制配置"""
    return await RechargeChannelControl.get_or_none(id=control_id)

async def get_enabled_controls(pay_type: str, amount: int) -> List[RechargeChannelControl]:
    controls = await RechargeChannelControl.filter(
        pay_type=pay_type,
        enabled=True,
        ratio__gt=0
    ).all()

    return [control for control in controls
        if (
            (control.min_amount * 100000 < amount < control.max_amount * 100000) 
            and control.enabled 
        )
    ]

async def get_enabled_channels_from_controls(pay_type: str, amount: int) -> List[RechargeChannelEnum]:
    controls = await get_enabled_controls(pay_type, amount)
    return [c.channel for c in controls]

async def is_channel_enabled_for_request(channel: RechargeChannelEnum, pay_type: str, amount: int) -> bool:
    # 使用优化后的批量查询方法
    enabled_channels = await get_enabled_channels_from_controls(pay_type, amount)
    return channel in enabled_channels

async def get_enabled_channels_for_request(pay_type: str, amount: int) -> List[str]:
    enabled_channels = await get_enabled_channels_from_controls(pay_type, amount)
    return [channel.value for channel in enabled_channels]

async def initialize_default_channel_controls():
    """初始化默认的渠道控制配置"""
    channels = [
        RechargeChannelEnum.TMPAY,
        RechargeChannelEnum.FFPAY,
        RechargeChannelEnum.QSZF,
        RechargeChannelEnum.SJZF,
        RechargeChannelEnum.JLBZF,
        RechargeChannelEnum.XJTZF,
        RechargeChannelEnum.SDFKW_API,
    ]

    pay_types = ['alipay', 'wechat']

    for channel in channels:
        for pay_type in pay_types:
            # 检查是否已存在配置
            existing = await RechargeChannelControl.get_or_none(
                channel=channel,
                pay_type=pay_type,
                min_amount=0,
                max_amount=0
            )

            if not existing:
                control = RechargeChannelControl(
                    channel=channel,
                    pay_type=pay_type,
                    min_amount=0,
                    max_amount=0,
                    enabled=True,
                    priority=100,
                    remark=f"默认配置 - {channel.description} - {pay_type}"
                )
                await control.save()

async def get_channel_queue_item(pay_type: str) -> str:
    queue_key = f"recharge:channel:queue:{pay_type}"
    for _ in range(len(all_channels)):
        channel = redis_client.rpoplpush(queue_key, queue_key)
        if channel:
            cv = channel.decode('utf-8')
            if cv in all_channels:
                return cv

    logging.warning(f"No available channel in queue for pay_type: {pay_type}")
    return ""

def _generate_dispersed_queue(valid_controls: List, queue_size: int) -> List[str]:
    """
    生成打散的渠道队列，避免连续相同渠道

    改进的算法思路：
    1. 根据比例计算每个渠道应该出现的次数
    2. 使用改进的贪心算法，优先考虑打散效果
    3. 限制连续相同渠道的最大长度
    4. 确保首尾不同，避免循环时出现连续
    """
    if not valid_controls:
        return []

    # 计算总权重和每个渠道的目标次数
    total_weight = sum(control.ratio for control in valid_controls)
    if total_weight == 0:
        return []

    # 为每个渠道计算目标出现次数
    channel_targets = []
    for control in valid_controls:
        target_count = int((control.ratio / total_weight) * queue_size)
        if target_count == 0 and control.ratio > 0:  # 确保有权重的渠道至少出现一次
            target_count = 1
        channel_targets.append({
            'channel': control.channel.value,
            'target': target_count,
            'current': 0,
            'ratio': control.ratio
        })

    # 调整目标次数，确保总和等于队列大小
    total_targets = sum(ct['target'] for ct in channel_targets)
    if total_targets < queue_size:
        # 按比例分配剩余的位置
        remaining = queue_size - total_targets
        for i in range(remaining):
            # 找到比例最大且当前分配相对不足的渠道
            best_channel = max(channel_targets,
                             key=lambda ct: ct['ratio'] / max(ct['target'], 1))
            best_channel['target'] += 1
    elif total_targets > queue_size:
        # 减少目标次数
        excess = total_targets - queue_size
        for i in range(excess):
            # 找到目标次数最多的渠道减少
            best_channel = max(channel_targets, key=lambda ct: ct['target'])
            if best_channel['target'] > 1:
                best_channel['target'] -= 1

    queue = []
    last_channel = None
    consecutive_count = 0
    max_consecutive_allowed = max(1, min(3, queue_size // len(channel_targets)))  # 更严格的连续限制

    for _ in range(queue_size):
        # 找到最需要被选择的渠道
        available_channels = [ct for ct in channel_targets if ct['current'] < ct['target']]

        if not available_channels:
            # 所有渠道都达到目标，重新开始分配
            available_channels = channel_targets

        # 如果上一个渠道已经连续出现太多次，强制选择不同的渠道
        if last_channel and consecutive_count >= max_consecutive_allowed:
            different_channels = [ct for ct in available_channels if ct['channel'] != last_channel]
            if different_channels:
                available_channels = different_channels
        # 否则优先选择与上一个不同的渠道
        elif last_channel:
            different_channels = [ct for ct in available_channels if ct['channel'] != last_channel]
            if different_channels:
                available_channels = different_channels

        # 在可用渠道中选择最需要的（当前次数相对于目标次数最少的）
        chosen = min(available_channels,
                    key=lambda ct: ct['current'] / max(ct['target'], 1))

        queue.append(chosen['channel'])
        chosen['current'] += 1

        # 更新连续计数
        if chosen['channel'] == last_channel:
            consecutive_count += 1
        else:
            consecutive_count = 1

        last_channel = chosen['channel']

    # 后处理：尝试进一步优化连续序列
    queue = _optimize_queue_dispersion(queue, max_consecutive_allowed)

    # 特殊处理：如果仍有很长的连续序列，使用间隔插入策略
    if _has_long_consecutive_sequence(queue, max_consecutive_allowed * 2):
        queue = _apply_interval_insertion_strategy(queue, channel_targets)

    return queue

def _optimize_queue_dispersion(queue: List[str], max_consecutive_allowed: int) -> List[str]:
    """优化队列的打散效果"""
    if len(queue) <= 1:
        return queue

    # 检查首尾是否相同，如果相同则尝试调整
    if queue[0] == queue[-1]:
        # 寻找一个可以与末尾交换的位置
        for i in range(len(queue) - 2, 0, -1):  # 从倒数第二个开始往前找
            if queue[i] != queue[0] and (i == len(queue) - 2 or queue[i] != queue[-2]):
                # 交换位置
                queue[-1], queue[i] = queue[i], queue[-1]
                break

    # 尝试减少过长的连续序列
    for attempt in range(2):  # 最多尝试2次优化
        improved = False
        i = 0
        while i < len(queue) - max_consecutive_allowed:
            # 检查是否有超过限制的连续序列
            if all(queue[j] == queue[i] for j in range(i, i + max_consecutive_allowed + 1)):
                # 找到连续序列的结束位置
                j = i + max_consecutive_allowed + 1
                while j < len(queue) and queue[j] == queue[i]:
                    j += 1

                # 尝试在连续序列中插入不同的元素
                if _try_insert_different_elements(queue, i, j, max_consecutive_allowed):
                    improved = True
                    break

            i += 1

        if not improved:
            break

    return queue

def _try_insert_different_elements(queue: List[str], start: int, end: int, max_consecutive: int) -> bool:
    """尝试在连续序列中插入不同的元素来打散"""
    consecutive_channel = queue[start]

    # 寻找可以移动到这里的不同元素
    for i in range(len(queue)):
        if i < start or i >= end:  # 不在连续序列内
            if queue[i] != consecutive_channel:
                # 检查移动这个元素是否会破坏其他地方的打散效果
                if _can_move_element_safely(queue, i, start + max_consecutive):
                    # 移动元素
                    element = queue.pop(i)
                    insert_pos = start + max_consecutive
                    if i < start:
                        insert_pos -= 1  # 调整插入位置
                    queue.insert(insert_pos, element)
                    return True

    return False

def _can_move_element_safely(queue: List[str], from_pos: int, to_pos: int) -> bool:
    """检查移动元素是否安全（不会创建新的连续序列）"""
    element = queue[from_pos]

    # 检查目标位置
    if to_pos > 0 and queue[to_pos - 1] == element:
        return False
    if to_pos < len(queue) and queue[to_pos] == element:
        return False

    # 检查原位置移除后是否会有问题
    # 这里简化处理，实际可以更复杂
    return True

def _has_long_consecutive_sequence(queue: List[str], threshold: int) -> bool:
    """检查队列是否有超过阈值的连续序列"""
    if len(queue) <= threshold:
        return False

    current_consecutive = 1
    for i in range(1, len(queue)):
        if queue[i] == queue[i-1]:
            current_consecutive += 1
            if current_consecutive > threshold:
                return True
        else:
            current_consecutive = 1

    return False

def _apply_interval_insertion_strategy(queue: List[str], channel_targets: List) -> List[str]:
    """应用间隔插入策略来处理极端比例情况"""
    if len(channel_targets) < 2:
        return queue

    # 统计各渠道的目标次数
    target_counts = {}
    for ct in channel_targets:
        target_counts[ct['channel']] = ct['target']

    # 找到主要渠道（目标次数最多的）
    main_channel = max(target_counts.keys(), key=lambda x: target_counts[x])
    other_channels = [ch for ch in target_counts.keys() if ch != main_channel]

    if not other_channels:
        return queue

    main_count = target_counts[main_channel]
    other_total = sum(target_counts[ch] for ch in other_channels)

    # 使用更智能的间隔策略
    new_queue = []

    # 如果其他渠道总数很少，尽量均匀分布
    if other_total <= main_count // 3:
        # 计算间隔：尽量让其他渠道均匀分布在主渠道中
        interval = max(2, main_count // (other_total + 1))

        # 准备其他渠道的循环队列
        other_cycle = []
        for ch in other_channels:
            other_cycle.extend([ch] * target_counts[ch])

        other_index = 0
        main_added = 0

        for i in range(len(queue)):
            # 每隔一定数量的主渠道插入一个其他渠道
            if other_index < len(other_cycle) and main_added > 0 and main_added % interval == 0:
                new_queue.append(other_cycle[other_index])
                other_index += 1
            elif main_added < main_count:
                new_queue.append(main_channel)
                main_added += 1
            elif other_index < len(other_cycle):
                new_queue.append(other_cycle[other_index])
                other_index += 1
    else:
        # 如果其他渠道较多，使用原来的策略
        return queue

    # 确保首尾不同
    if len(new_queue) > 1 and new_queue[0] == new_queue[-1] and len(other_channels) > 0:
        # 尝试调整末尾
        for i in range(len(new_queue) - 2, 0, -1):
            if new_queue[i] != new_queue[0]:
                new_queue[-1], new_queue[i] = new_queue[i], new_queue[-1]
                break

    return new_queue

def _analyze_queue_quality(queue: List[str]) -> int:
    """分析队列质量，返回最大连续相同渠道数"""
    if len(queue) <= 1:
        return len(queue)

    max_consecutive = 1
    current_consecutive = 1

    for i in range(1, len(queue)):
        if queue[i] == queue[i-1]:
            current_consecutive += 1
        else:
            max_consecutive = max(max_consecutive, current_consecutive)
            current_consecutive = 1

    max_consecutive = max(max_consecutive, current_consecutive)
    return max_consecutive

async def generate_channel_queue():
    for pay_type in ['alipay', 'wechat']:
        controls = await RechargeChannelControl.filter(
            pay_type=pay_type,
            enabled=True
        ).all()
        # Filter out controls with zero ratio
        valid_controls = [control for control in controls if control.ratio > 0]

        if not valid_controls:
            logging.warning(f"No valid controls found for pay_type: {pay_type}")
            continue

        total_weight = sum(control.ratio for control in valid_controls)
        queue_size = total_weight
        queue = _generate_dispersed_queue(valid_controls, queue_size)

        logging.info(f"Generated dispersed queue for {pay_type}: {queue}")
        logging.info(f"Queue length: {len(queue)}, Unique channels: {len(set(queue))}")

        # 统计每个渠道的出现次数用于验证
        channel_counts = {}
        for channel in queue:
            channel_counts[channel] = channel_counts.get(channel, 0) + 1
        logging.info(f"Channel distribution for {pay_type}: {channel_counts}")

        # 分析队列质量
        max_consecutive = _analyze_queue_quality(queue)
        logging.info(f"Max consecutive same channels: {max_consecutive}")

        # 如果质量不佳，记录警告
        if max_consecutive > 5:
            logging.warning(f"Queue quality may be poor for {pay_type}, max consecutive: {max_consecutive}")

        with redis_client.pipeline() as pipe:
            pipe.delete(f"recharge:channel:queue:{pay_type}")
            if queue:  # 只有当队列不为空时才添加
                pipe.rpush(f"recharge:channel:queue:{pay_type}", *queue)
            pipe.execute()

async def get_user_whitelist_channel(tg_id: int) -> RechargeChannelWhitelist | None:
    return await RechargeChannelWhitelist.filter(tg_id=tg_id, enabled=True).first()

async def add_whitelist_entry(tg_id: int, channel: RechargeChannelEnum, remark: str = "") -> RechargeChannelWhitelist:
    entry, created = await RechargeChannelWhitelist.get_or_create(
        tg_id=tg_id,
        channel=channel,
        defaults={
            'enabled': True,
            'remark': remark
        }
    )
    if not created:
        entry.enabled = True
        entry.remark = remark
        await entry.save()
    return entry


async def remove_whitelist_entry(tg_id: int, channel: RechargeChannelEnum) -> bool:
    entry = await RechargeChannelWhitelist.get_or_none(tg_id=tg_id, channel=channel)
    if entry:
        entry.enabled = False
        await entry.save()
        return True
    return False

async def get_whitelist_entries() -> List[RechargeChannelWhitelist]:
    return await RechargeChannelWhitelist.all()


async def get_whitelist_by_channel(channel: RechargeChannelEnum) -> List[RechargeChannelWhitelist]:
    return await RechargeChannelWhitelist.filter(channel=channel, enabled=True).all()

async def update_whitelist_entry(entry_id: int, enabled: bool | None = None, remark: str = '') -> RechargeChannelWhitelist:
    entry = await RechargeChannelWhitelist.get(id=entry_id)
    if enabled is not None:
        entry.enabled = enabled
    if remark is not None:
        entry.remark = remark
    await entry.save()
    return entry


async def reorder_channel_queue_to_front(pay_type: str, target_channel: str) -> Tuple[bool, str]:
    queue_key = f"recharge:channel:queue:{pay_type}"

    try:
        # 获取当前队列的所有元素
        current_queue = redis_client.lrange(queue_key, 0, -1)
        if not current_queue:
            logging.warning(f"No queue found for pay_type: {pay_type}")
            return False, f"No queue found for pay_type: {pay_type}"

        # 将字节转换为字符串
        queue_list = [item.decode('utf-8') for item in current_queue]

        # 检查目标渠道是否在队列中
        if target_channel not in queue_list:
            logging.warning(f"Target channel {target_channel} not found in queue for pay_type: {pay_type}")
            return False, f"Target channel {target_channel} not found in queue for pay_type: {pay_type}"

        # 移除目标渠道的所有实例
        filtered_queue = [channel for channel in queue_list if channel != target_channel]

        # 计算目标渠道原本的数量
        target_channel_count = len(queue_list) - len(filtered_queue)

        # 重新构建队列：目标渠道放在最前面，其他保持原有顺序
        new_queue = [target_channel] * target_channel_count + filtered_queue

        # 更新 Redis 队列
        with redis_client.pipeline() as pipe:
            pipe.delete(queue_key)
            if new_queue:
                pipe.rpush(queue_key, *new_queue)
            pipe.execute()

        logging.info(f"Successfully reordered queue for {pay_type}, moved {target_channel} to front")
        logging.info(f"New queue preview (first 10): {new_queue[:10]}")

        return True, f"New queue preview (first 10): {new_queue[:10]}"

    except Exception as e:
        logging.error(f"Error reordering channel queue for {pay_type}: {str(e)}")
        return False, str(e)
