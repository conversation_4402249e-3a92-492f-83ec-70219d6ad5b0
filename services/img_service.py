from datetime import datetime
from typing import Optional
import logging

import pytz
from persistence.models.models_bot_image import BotImgGenTask, BotImgBasicProfile, ImgGenStatus, ImageBotSettings

from common.image_bot_model import GenImageBaseProfileBO,ImageBotSettingsBO

logger = logging.getLogger(__name__)
class ImageBotService:


    @classmethod
    async def update_b_profile_resolution(cls, tg_id:int,img_def:str) -> GenImageBaseProfileBO:
        
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            bot_img_profile = await BotImgBasicProfile.create(tg_id=tg_id,img_gen_profile={"style":"","resolution":img_def,"privacy":"public"})
        else:
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["resolution"] = img_def
            await bot_img_profile.save(update_fields=["img_gen_profile"])
        
        return GenImageBaseProfileBO.from_model(bot_img_profile)
    
    @classmethod
    async def update_b_profile_style(cls, tg_id:int,style:str) -> GenImageBaseProfileBO:
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        if not bot_img_profile:
            bot_img_profile = await BotImgBasicProfile.create(tg_id=tg_id,img_gen_profile={"style":style,"resolution":"low","privacy":"public"})
        else:
            if not isinstance(bot_img_profile.img_gen_profile, dict):
                bot_img_profile.img_gen_profile = {}
            bot_img_profile.img_gen_profile["style"] = style
            await bot_img_profile.save(update_fields=["img_gen_profile"])
        
        return GenImageBaseProfileBO.from_model(bot_img_profile)
    
    @classmethod
    async def get_basic_profile(cls, tg_id:int) -> GenImageBaseProfileBO:
        # 获取用户画图基础信息
        bot_img_profile = await BotImgBasicProfile.get_or_none(tg_id=tg_id)
        
        if not bot_img_profile:
            return GenImageBaseProfileBO(style="")
        return GenImageBaseProfileBO.from_model(bot_img_profile)
    @classmethod
    async def add_img_gen_task(cls, tg_id:int, prompt:str, basic_profile:GenImageBaseProfileBO) -> BotImgGenTask:
        # 添加画图任务
        req_json = basic_profile.model_dump()
        req_json["prompt"] = prompt
        img_gen_task = await BotImgGenTask.create(tg_id=tg_id,prompt=prompt,req_json=req_json,status=ImgGenStatus.PROCESSING)
        
        return img_gen_task
    
    @classmethod
    async def get_img_gen_task_by_id(cls,task_id:int) -> Optional[BotImgGenTask]:
        # 获取画图任务
        img_gen_task = await BotImgGenTask.get_or_none(id=task_id)
    
        return img_gen_task
    
    @classmethod
    async def get_img_gen_task_by_tg_id(cls,tg_id:int) -> Optional[BotImgGenTask]:
        # 获取画图任务
        img_gen_task = await BotImgGenTask.filter(tg_id=tg_id).filter(status=ImgGenStatus.PROCESSING).first()
    
        return img_gen_task
    @classmethod
    async def check_in_progress_img_gen_task(cls,tg_id:int) -> bool:
        # 检查是否有进行中的画图任务
        img_gen_task = await BotImgGenTask.filter(tg_id=tg_id).filter(status=ImgGenStatus.PROCESSING).first()
        if img_gen_task:
            c_time = img_gen_task.created_at
            now = datetime.now(tz=pytz.utc)
            if (now - c_time).total_seconds() > 60 * 2:
                # 超过2分钟没有更新,则认为任务失败
                img_gen_task.status = ImgGenStatus.FAILED
                img_gen_task.completed_at = now
                img_gen_task.error_message = "任务超时"
                await img_gen_task.save(update_fields=["status","completed_at","error_message"])
                logger.warning(f"任务超时,更新任务状态为失败,任务ID: {img_gen_task.id}")
                return False
            return True
        return False
    @classmethod
    async def update_img_gen_task(cls, req_id:int, status:ImgGenStatus, gen_result:Optional[dict]=None, error_message:Optional[str]=None) -> Optional[BotImgGenTask]:
        # 更新画图任务
        img_gen_task = await BotImgGenTask.get_or_none(id=req_id)
        if not img_gen_task:
            return None
        
        img_gen_task.status = status
        img_gen_task.completed_at = datetime.now(tz=pytz.utc)
        if gen_result:
            img_gen_task.gen_result = gen_result
        if error_message:
            img_gen_task.error_message = error_message
        await img_gen_task.save(update_fields=["status","gen_result","error_message","completed_at"])

    @classmethod
    async def get_img_bot_start_msg(cls, bot_id:int) -> str:
        # 获取机器人启动消息
        img_bot_settings = await ImageBotSettings.get_or_none(bot_id=bot_id)
        if not img_bot_settings:
            return "欢迎使用机器人"
        return img_bot_settings.start_msg
    
    @classmethod
    async def get_share_group_topic(cls, bot_id:int, style:str) -> tuple[int,int]:
        # 获取机器人分享群和话题
        img_bot_settings = await ImageBotSettings.get_or_none(bot_id=bot_id)
        logger.info(f"获取机器人分享群和话题,bot_id:{bot_id},style:{style},img_bot_settings:{img_bot_settings}")
        if img_bot_settings and img_bot_settings.style_group_mapping:
            style_group_mapping = img_bot_settings.style_group_mapping
            if style in style_group_mapping:
                group_id = style_group_mapping.get(style).get("group_id") # type: ignore
                topic_id = style_group_mapping.get(style).get("topic_id")  # type: ignore
                return group_id, topic_id
        
        # 如果没有找到对应的映射，返回默认值
        return 0, 0
        