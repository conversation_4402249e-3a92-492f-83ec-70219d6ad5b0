

from common.models.ai_bot_admin.user_card_admin_bean import OpAction

from  persistence.models.models_bot_group import OpLog

class OpLogBiz:
    
    @classmethod
    async def save_op_log(cls,op_action:OpAction,op_type:int=0):
        
        await OpLog.create(op_type=op_type,tg_id=op_action.tg_id,group_id=op_action.group_id,op_action=op_action.model_dump_json(),op_action_type=op_action.action_type.code) 
        