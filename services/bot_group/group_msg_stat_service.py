import asyncio
from datetime import date, datetime, timedelta
import logging
import os
import random

from aiogram import Bo<PERSON>
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
import pytz


from services.bot_group.biz.tg_group_user_msg_biz import FFBotMsgBiz, TgGroupMessageBiz, TgGroupUserMapBiz, GroupMsgUserStatBiz, UserInviteShareStatBiz






log = logging.getLogger(__name__)    


class GroupMsgStatService:    
    
    @classmethod
    async def cx_group_msg_status(cls,group_id:int,cx_date:date,cx_hour :int =-1)->str:
        log.info(f"cx_group_msg_status:{group_id},{cx_date},{cx_hour}")

        if cx_hour == -1:
            # 查询维度为天
            cx_day_re = await GroupMsgUserStatBiz.get_daily_user_msg_stat(group_id,cx_date)
            total_msg_cnt,total_user_cnt = await GroupMsgUserStatBiz.get_daily_user_msg_stat_total(group_id,cx_date)
            cx_result = (
                f"<b><em>当天: {cx_date}</em>的群组全部消息统计:</b>\n\n"
                f"发送日期: {datetime.now()}\n"
                f"群组ID: {group_id}\n"
                f"<b>总消息数量: {total_msg_cnt}</b>\n"
                f"<b>总活跃人数: {total_user_cnt}</b>\n"
                f"<i>Top 100 用户统计:</i>\n\n"
            )
            i =1
            for user_stat in cx_day_re:
                if user_stat.user_id == group_id:
                    # 过滤掉群组消息
                    continue
                user_stat_str = f"{i}.<u>{user_stat.tg_nick_name}</u> - {user_stat.message_count}"
                cx_result += f"{user_stat_str}\n"
                i += 1
            return cx_result
        else:
            # 查询维度为小时
            cx_hour_re = await GroupMsgUserStatBiz.get_hours_user_msg_stat(group_id,cx_date,cx_hour)
   
            cx_result = (
                    f"<b>{cx_date}日{cx_hour}点-小时维度的用户消息统计:</b>\n\n"
                    f"发送日期: {datetime.now()}\n"
                    f"群组ID: {group_id}\n"
                    )
            i =1
            for user_stat in cx_hour_re:
                user_stat_str = f"{i}.{user_stat.tg_nick_name}-{user_stat.message_count}"
                cx_result += f"{user_stat_str}\n"
                i += 1
            
            return cx_result

    @classmethod
    async def cx_user_invite_share_stat(cls,cx_date:date)->str:
        log.info(f"cx_user_invite_share_stat:{cx_date}")
        
        
        cx_re = await UserInviteShareStatBiz.get_daily_user_invite_stat(cx_date)
        cx_result = (
            f"<b>当天：{cx_date}的用户邀请统计:</b>\n\n"
            f"发送日期: {datetime.now()}\n"

        )
        i =1
        for user_stat in cx_re:
            user_stat_str = f"{i}.<u>{user_stat.tg_nick_name}</u> - {user_stat.invite_count}"
            cx_result += f"{user_stat_str}\n"
            i += 1
        return cx_result

    @classmethod
    async def cx_user_share_stat(cls,cx_date:date)->str:
        log.info(f"cx_user_share_stat:{cx_date}")
        
        cx_re = await UserInviteShareStatBiz.get_daily_user_share_stat(cx_date)
        cx_result = (
            f"<b>当天：{cx_date}的用户分享统计</b>:\n\n"
            f"发送日期: {datetime.now()}\n"
        )
        i =1
        for user_stat in cx_re:
            user_stat_str = f"{i}.<u>{user_stat.tg_nick_name}</u> - {user_stat.share_count}"
            cx_result += f"{user_stat_str}\n"
            i += 1
        return cx_result

    @classmethod
    async def send_share_stat_to_bot(cls,cal_date:date):
        log.info("send_now_share_stat_to_bot:%s",cal_date)
        
        share_stat = await cls.cx_user_share_stat(cal_date)
        
        topic_id,thread_id = FFBotMsgBiz.get_msg_stat_topic_id()
        
        await FFBotMsgBiz.send_message(topic_id,thread_id,share_stat)
        
    @classmethod
    async def send_invite_stat_to_bot(cls,cal_date:date):
        log.info("send_invite_share_stat_to_bot:%s",cal_date)
        
        invite_share_stat = await cls.cx_user_invite_share_stat(cal_date)
        
        topic_id,thread_id = FFBotMsgBiz.get_msg_stat_topic_id()
        
        await FFBotMsgBiz.send_message(topic_id,thread_id,invite_share_stat)
    
    @classmethod
    async def send_group_msg_stat_to_bot(cls,group_id:int,cal_date:date):
        log.info("send_group_msg_stat_to_bot:%s",cal_date)
        
        group_msg_stat = await cls.cx_group_msg_status(group_id,cal_date)
        
        topic_id,thread_id = FFBotMsgBiz.get_msg_stat_topic_id()
        
        await FFBotMsgBiz.send_message(topic_id,thread_id,group_msg_stat)
    
    @classmethod
    async def send_group_hour_msg_stat_to_bot(cls,group_id:int,cal_date:date,hour:int):
        log.info("send_group_hour_msg_stat_to_bot:%s,%s",cal_date,hour)
        
        group_msg_stat = await cls.cx_group_msg_status(group_id,cal_date,hour)
        
        topic_id,thread_id = FFBotMsgBiz.get_msg_stat_topic_id()
        
        await FFBotMsgBiz.send_message(topic_id,thread_id,group_msg_stat)