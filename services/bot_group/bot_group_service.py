
import asyncio
import logging
import os
import re
import json
from datetime import datetime, timedelta, timezone
from sys import exception
from typing import Union

from aiogram import types
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import Inline<PERSON>eyboardBuilder

from datetime import datetime, timedelta
from dotenv import load_dotenv
from tortoise.functions import Count
from tortoise.exceptions import DoesNotExist
from aibot_handlers.tg_util import check_admin
from common.common_constant import ImageCheckLabel, ImageCheckSuggestions
from common.models.ai_bot_admin.user_card_admin_bean import OpAction, OpActionType
from services.bot_group.group_user_op_service import group_user_op_service
from persistence.models.models_bot_group import BotGroupConfig,SpamProtectionRule, AutoReplyRule, TgGroupMessage, UserSpamBehavior, BotWelcomeConfigModel,BotGroupMap,VideoModerationTask,MediaModerationTask,GroupImageForwardCfg,JoinGroupUserProcess,JoinGroupUserProcessHistory

from utils.ims_util import upload_img_to_cos, image_moderation

from services.bot_group.aibot_help import SaveMessageMiddleware,BotSendMsgPrivate,ForwardUserInfoMessageService

from services.bot_group.biz.media_moderate_biz import IMSModerationBiz

from common.models.ai_bot_admin.group_config import AutoReplyResultBO, AutoReplyRuleBO, MsgContentBO, RubbishAction, SpamProtectionRuleBO
from common.models.ai_bot_admin.ims_moderation_common import MediaModerateConstants,SpamRubbishType

from services.bot_group.biz.tg_group_user_msg_biz import FFBotMsgBiz
from services.bot_group.group_msg_service import GroupUserCardInfoService
from services.bot_group.group_user_op_service import group_user_op_service

from persistence.models.models import TelegramUser
log = logging.getLogger(__name__)


# 加载环境变量
load_dotenv()

TMA_BOT_TOKEN = os.getenv("TMA_BOT_TOKEN")
CHAT_BOT_TOKEN = os.getenv("CHAT_BOT_TOKEN")

IMAGE_CHECK_API = os.getenv("IMAGE_CHECK_API")






class BotWelcomeConfigHandler():
    
    def __init__(self, group_id:int):
        self.group_id = group_id
        self._stop_event = asyncio.Event()  

    async def load_bot_welcome_config(self):
        # 从数据库加载配置
        config = await BotWelcomeConfigModel.filter(group_id=self.group_id).first()
        self.config = config
      
    async def start_periodic_loading(self, interval: int):
        log.info(f"welcome_config start_periodic_loading interval:{interval}")
        while not self._stop_event.is_set():
            await self.load_bot_welcome_config()
            await asyncio.sleep(interval)
    
    async def get_welcome_message(self) -> BotWelcomeConfigModel|None:
        return self.config
class AutoReplyRuleHandler():
    def __init__(self,group_id:int):
        
        self.group_id = group_id
        self._stop_event = asyncio.Event()
        # self.rules = await self.load_rules_from_db(group_id=group_id) 
    
    async def load_rules_from_db(self)->list[AutoReplyRuleBO]|None:
        # 从数据库加载规则
        rules = await AutoReplyRule.filter(group_id=self.group_id).filter(is_enable=True).order_by('-pripority').all()
        self.rules = [AutoReplyRuleBO.from_orm(rule) for rule in rules]
    
    async def start_periodic_loading(self, interval: int):
        log.info(f"AutoReplyRule start_periodic_loading interval:{interval}")
        while not self._stop_event.is_set():
            await self.load_rules_from_db()
            await asyncio.sleep(interval)
    
    def math_auto_reply_rule(self,user_id:int, message:str)->AutoReplyRuleBO|None:
        
        if  not self.rules:
            return None
        
        for rule in self.rules:
            keys_word = rule.keyword_list.split(',')
            if rule.keyword_type == 1: # 完全匹配
                if message in keys_word:
                    return rule
            elif rule.keyword_type == 2: # 包含匹配
                if any(keyword in message for keyword in keys_word):
                    return rule
            elif rule.keyword_type == 3: # 正则匹配
                if any(re.match(keyword, message) for keyword in keys_word):
                    return rule
        return None
    
    def math_auto_reply_rule_from_user(self,user_id:int, username:str)->AutoReplyRuleBO|None:
        
        if  not self.rules:
            return None
        
        for rule in self.rules:
            keys_word = rule.keyword_list
            if rule.keyword_type == 21: # tg-id完全匹配
                if str(user_id) == keys_word:
                    return rule
            elif rule.keyword_type == 22: # username完全匹配
                if username == keys_word:
                    return rule
        return None
    
    def execute_rule(self,rule:AutoReplyRuleBO,user_id:int, message:str) -> AutoReplyResultBO|None:
        reply_msg = None
        if rule.msg_type == 1: # 普通文本消息
            msg_text = rule.msg_content.get('msg_text')
            
            reply_msg = AutoReplyResultBO(msg_type=1, message_text=msg_text,del_bot_msg_delay=rule.del_bot_msg_delay,del_src_msg_delay=rule.del_src_msg_delay,is_quote=rule.is_quote)
        elif rule.msg_type == 2: # 图片消息
            msg_text = rule.msg_content.get('msg_text')
            image_url = rule.msg_content.get('image_url')
            reply_msg = AutoReplyResultBO(msg_type=2, message_text=msg_text,image_url=image_url,del_bot_msg_delay=rule.del_bot_msg_delay,del_src_msg_delay=rule.del_src_msg_delay,is_quote=rule.is_quote)
        return reply_msg 
            
    
    #处理自动回复,根据消息
    def check_message(self, user_id:int, message:str) -> AutoReplyResultBO|None:
        rule = self.math_auto_reply_rule(user_id, message)
        log.info(f"math_auto_reply_rule,user_id:{user_id},msg:{message},rule:{rule}")
        if rule:
            return self.execute_rule(rule, user_id,message)
        else:
            # 没有匹配到规则，继续处理消息
            return None
    # 处理自动回复,根据用户
    def check_message_from_user(self, user_id:int, username:str,message:str) -> AutoReplyResultBO|None:
        rule = self.math_auto_reply_rule_from_user(user_id, username)
        log.info(f"math_auto_reply_rule,user_id:{user_id},msg:{username},rule:{rule}")
        if rule:
            return self.execute_rule(rule, user_id,message)
        else:
            # 没有匹配到规则，继续处理消息
            return None




class SpamProtectionHandler():
    def __init__(self,group_id:int):
        self.group_id = group_id
        self.rules = None
        self._stop_event = asyncio.Event()

    async def load_rules_from_db(self)->list[SpamProtectionRuleBO]|None:
        # 从数据库加载规则
        rules = await SpamProtectionRule.filter(group_id=self.group_id).all()
        self.rules =  [SpamProtectionRuleBO.from_orm(rule) for rule in rules]
    
    async def start_periodic_loading(self, interval: int):
        log.info(f"start_periodic_loading interval:{interval}")
        while not self._stop_event.is_set():
            await self.load_rules_from_db()
            await asyncio.sleep(interval)


    @staticmethod
    def is_link(text: str) -> bool:
        # 定义一个正则表达式模式来匹配 tg URL
        url_pattern = re.compile(
            r'http[s]?://'  # http:// 或 https://
            r'(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|'  # 域名部分
            r'(?:%[0-9a-fA-F][0-9a-fA-F]))+'  # 百分号编码
        )
        bot_pattern = re.compile(r'@[\w\d_]+(?:_bot|Bot)')
        # 使用正则表达式模式匹配输入字符串
        return (re.search(url_pattern, text) is not None) or (re.search(bot_pattern, text) is not None)
    @classmethod
    async def is_forward_from_whitelist(cls,group_id:int,forward_from_id:int,msg:dict) -> tuple[bool,dict]:
        """
        检查转发消息是否来自白名单
        """
        msg_check_result = msg
        if forward_from_id != -1:
            forward_cfg = await GroupImageForwardCfg.filter(group_id=group_id,forward_id=forward_from_id).first()
            if forward_cfg:
                log.info(f"image_ff_check,forward_cfg:{forward_cfg}")
                await IMSModerationBiz.add_admin_skip_media_task(msg)
                msg_check_result["suggestions"] = MediaModerateConstants.PASS
                msg_check_result["check_label"] = MediaModerateConstants.LABEL_FORWARD_SKIP
                msg_check_result["image_key"] = msg.get("image_key","")
                msg_check_result["request_id"] = ""
                return True,msg_check_result
        return False,msg_check_result
    
    async def handle_admin_skip(msg, msg_check_result, msg_id, image_key):
        """
        处理管理员跳过逻辑
        """
        log.info(f"image_ff_check,admin skip:{msg_id}")
        await IMSModerationBiz.add_admin_skip_media_task(msg)
        msg_check_result["suggestions"] = MediaModerateConstants.PASS
        msg_check_result["check_label"] = MediaModerateConstants.LABEL_ADMIN_SKIP
        msg_check_result["image_key"] = image_key
        msg_check_result["request_id"] = ""
    
    @classmethod
    async def image_ff_check(cls, msg: dict) -> bool:
        
        log.info(f"image_ff_check,begein:{msg}")
        
        msg_check_result = msg
        # 调用图片审核接口
        try:
            image_url = msg.get("image_url", "")
            chat_id = msg.get("group_id", 0)
            msg_id = msg.get("msg_id", 0)
            tg_id = msg.get("tg_id", 0)
            file_id = msg.get("file_id", "")
            file_unique_id = msg.get("file_unique_id", "")
            # 上传图片到COS
            image_key = upload_img_to_cos(image_url)
            msg['image_key'] = image_key
            
            forward_from_id = msg.get("forward_from_id", -1)
            
            #check转发的消息是否来自白名单的群组或者频道id
            
            is_from_white, msg_check_r = await cls.is_forward_from_whitelist(chat_id, forward_from_id,msg)
            
            if is_from_white:
                log.info(f"image_ff_check,forward_cfg:{msg_check_r}")
                msg_check_result = msg_check_r
            
            elif check_admin(tg_id):
                log.info(f"image_ff_check,admin skip:{msg_id}")
                await IMSModerationBiz.add_admin_skip_media_task(msg)
                
                msg_check_result["suggestions"] = MediaModerateConstants.PASS
                msg_check_result["check_label"] = MediaModerateConstants.LABEL_ADMIN_SKIP
                msg_check_result["image_key"] = image_key
                msg_check_result["request_id"] = ""
            else:
                await IMSModerationBiz.add_media_task(msg)

                # 调用图片审核接口
                image_result = await IMSModerationBiz.image_moderation(image_url=image_key,file_id=file_id,file_unique_id=file_unique_id,chat_id=chat_id)
                msg_check_result["suggestions"] = image_result.suggestions
                msg_check_result["check_label"] = image_result.check_label
                msg_check_result["request_id"] = image_result.request_id
                msg_check_result["image_key"] = image_key
                log.info(f"image_ff_check,end:{msg_check_result}")
            
            await IMSModerationBiz.update_media_task(chat_id, msg_id,request_id=msg_check_result["request_id"],image_key=image_key,suggestions=msg_check_result["suggestions"],check_label=msg_check_result["check_label"])
            
            # 如果是block,删除消息
            if msg_check_result["suggestions"] == MediaModerateConstants.BLOCK:
                await UserSpamBehaviorService.increase_user_spam_cnt(tg_id, chat_id,SpamRubbishType.PHOTO_SPAM,msg_id=msg_id) # type: ignore
                await FFBotMsgBiz.del_group_msg(chat_id, msg_id)

            
            # 发送审核结果消息
            await FFBotMsgBiz.send_image_check_photo_msg(msg_check_result)
        except Exception as e:
            log.error(f"image_ff_check error:{e}",exc_info=True)
        return False
    
    @classmethod
    async def stick_gif_check(cls, msg: dict) -> bool:
        
        log.info(f"st_emoji_check,begin:{msg}")
        try:
            # 调用视频审核接口
            msg_id = msg.get("msg_id", 0)
            file_url = msg.get("file_url", "")
            file_id = msg.get("file_id", "")
            chat_id = msg.get("group_id", 0)
            tg_id = msg.get("tg_id", 0)
            file_unique_id = msg.get("file_unique_id", "")
            
            msg_check_result = msg
            
            # 上传视频到cos
            image_key = upload_img_to_cos(file_url,chat_id)
            if check_admin(tg_id):
                log.info(f"stick_gif_check,admin skip:{msg_id}")
                await IMSModerationBiz.add_admin_skip_media_task(msg)   
                msg_check_result["suggestions"] = MediaModerateConstants.PASS
                msg_check_result["check_label"] = MediaModerateConstants.LABEL_ADMIN_SKIP
                msg_check_result["image_key"] = image_key
                msg_check_result["request_id"] = ""
            else:
                msg["image_key"] = image_key
                if file_url.endswith(".tgs"):
                    log.info(f"st_emoji_check ignore tgs:{file_url}")
                    #忽略这种格式的
                    await IMSModerationBiz.add_admin_skip_media_task(msg,check_label="skip_tgs")
                    msg_check_result["suggestions"] = MediaModerateConstants.INIT
                    msg_check_result["check_label"] = MediaModerateConstants.LABEL_TGS_SKIP
                    msg_check_result["image_key"] = image_key
                    msg_check_result["request_id"] = ""
                else:
                    # 调用视频审核接口,异步调用
                    await IMSModerationBiz.add_media_task(msg)
                    create_result =  IMSModerationBiz.video_moderation(image_key,file_unique_id,chat_id)
                    msg_check_result["suggestions"] = MediaModerateConstants.INIT
                    msg_check_result["check_label"] = "stick_gif_check"
                    msg_check_result["image_key"] = image_key
                    
                    # 保存审核任务
                    await VideoModerationTask.create(task_id=create_result.task_id,group_id=chat_id,msg_id=msg_id,request_id=create_result.request_id,msg_dict=msg)
                    await IMSModerationBiz.update_media_task(chat_id, msg_id, create_result.request_id, MediaModerateConstants.INIT, "stick_gif_check",image_key)
                    
                    log.info(f"st_emoji_check_create_task,end:{create_result}")
            
            
            # 发送审核结果消息        
            await FFBotMsgBiz.send_image_check_photo_msg(msg_check_result)

        except Exception as e:
            log.error(f"st_emoji_check error:{e}",exc_info=True)
        return False
    
    @classmethod
    async def video_ff_check_task(cls):
        
        check_status = ["INIT", "PENDING", "RUNNING"]
        
        while True:
            await asyncio.sleep(15) # type: ignore
            log.info("video_ff_check_task start")
            m_tasks = await VideoModerationTask.filter(status__in=check_status).all().order_by('-created_at').limit(10)

            for task in m_tasks:
                try:
                    ims_v_r = IMSModerationBiz.video_moderation_check_result(task.task_id)

                    if ims_v_r.status == "FINISH" or ims_v_r.status == "ERROR" or ims_v_r.status == "CANCELLED":
                        task.status = ims_v_r.status
                        task.suggestion = ims_v_r.suggestions
                        task.task_result = ims_v_r.raw_response
                        task.task_cnt += 1
                        task.check_label = ims_v_r.check_label
                        await task.save()
                        msg_dict = task.msg_dict
                        
                        
                        await IMSModerationBiz.update_media_task(task.group_id, task.msg_id, ims_v_r.request_id, ims_v_r.suggestions, ims_v_r.check_label,msg_dict.get("image_key","")) # type: ignore
                        
                        if ims_v_r.suggestions == MediaModerateConstants.BLOCK:
                            await FFBotMsgBiz.del_group_msg(task.group_id, task.msg_id)
                            
                            await UserSpamBehaviorService.increase_user_spam_cnt(msg_dict["tg_id"], task.group_id,SpamRubbishType.PHOTO_SPAM,msg_id=msg_dict["msg_id"]) # type: ignore
                            if isinstance(msg_dict, dict):
                                msg_dict["suggestions"] = ims_v_r.suggestions
                                msg_dict["check_label"] = ims_v_r.check_label
                                msg_dict["request_id"] = ims_v_r.request_id
                            await FFBotMsgBiz.send_image_check_photo_msg(msg_dict) # type: ignore

                except Exception as e:
                    log.error(f"video_ff_check_task error:{task
                    }, {e}")
            log.info("video_ff_check_task end")

            
        
    # 检查消息是否包含 tg 链接或者 tg bot
    @staticmethod
    def find_tg_link_bot(text: str) -> list[str]|None:
        # 定义一个正则表达式模式来匹配 tg URL
        bot_pattern = re.compile(r'@[\w\d_]+(?:_bot|Bot)')
        
        link_pattern = re.compile(r'http[s]?://t\.me/[\w\d_\+]+')
        
        
        match_link = re.findall(link_pattern, text)
        match_bot = re.findall(bot_pattern, text)
        
        if match_link:
            # 去掉链接中的参数
            for i in range(len(match_link)):
                match_link[i] = match_link[i].split('?')[0]
        
        match_result = match_link + match_bot
        return match_result
    
    @staticmethod
    def find_tg_user(text: str) -> list[str]|None:
        # 定义一个正则表达式模式来匹配 tg URL
        user_pattern = re.compile(r'@[\w\d_]+')
        
        match_user = re.findall(user_pattern, text)
        
        return match_user
    
    async def check_message(self, message:types.Message, user_id:int)->SpamProtectionRuleBO|None:
        
        if not self.rules:
            return None
        
        # 对rule 根据 sort 排序
        self.rules.sort(key=lambda x: x.rubbish_sort, reverse=True)
        for rule in self.rules:
            # # 检查用户类型
            # if rule.check_user_type != 'all' and not self.check_user_type(user_id, rule.check_user_type):
            #     continue
            
            # 检查白名单
            if rule.white_user_list and (user_id in rule.white_user_list):
                continue
            
            # 检查垃圾类型匹配
            if await self.match_condition(message, rule):
                return rule
        return None


    async def match_condition(self, message:types.Message,  rubbish_rule:SpamProtectionRuleBO):
        
        condition = rubbish_rule.rubbish_condition
        rubbish_type = rubbish_rule.rubbish_type
    
        keywords = condition.split(',')
        #消息内容
        msg_text = message.text if message.text else ''
        #群成员昵称
        full_name = message.from_user.full_name if message.from_user else ''
        #转发标题
        forward_from = message.forward_from_chat.title if message.forward_from_chat else ''
    
        if rubbish_type == 6:  # 关键词匹配
 
            return any(keyword.strip().lower() in msg_text.lower() for keyword in keywords) or \
                any(keyword.strip().lower() in full_name.lower() for keyword in keywords) or \
                any(keyword.strip().lower() in forward_from.lower() for keyword in keywords)
        
        if rubbish_type == 1:  # link匹配&bot匹配
            
            #白名单link,不包含参数部分
            match_links = self.find_tg_link_bot(msg_text)
            
            log.info(f"match_links:{match_links},msg_text:{msg_text}")
            
            if match_links and len(match_links)>0:
                if rubbish_rule.white_link_list:
                    return any(link not in rubbish_rule.white_link_list for link in match_links)      
                else:
                    return True
            
            match_users = self.find_tg_user(msg_text)
            log.info(f"match_users:{match_users},msg_text:{msg_text}")
            
            #验证匹配的user是否是group或者channel
            if match_users and len(match_users)>0:
                
                #验证匹配的user是否是group或者channel
                for user in match_users:
                    try:
                        if rubbish_rule.white_link_list and user in rubbish_rule.white_link_list:
                            log.info(f"white_link_list match:{user}")
                            continue
                        chat_info = await message.bot.get_chat(chat_id=user,request_timeout=3)
                        if chat_info.type in ['group','supergroup','channel']:
                            log.warning(f"check tg user match:{msg_text} :{user}:\n{chat_info}")
                            return True
                    except Exception as e:
                        log.error(f"get_chat error:{user},{e}")
                        continue
            
            return False
            

        

    def apply_action(self, action, message, user_id):
        result = {}

        if action['del']['isDel']:
            result['delete'] = True

        if action['alert']['isAlert']:
            result['alert'] = {
                'times': action['alert']['alertTimes']
            }

        if action['kick']['isKick']:
            result['kick'] = {
                'time': self.parse_time(action['kick']['kickTime'])
            }

        if action['forbid']['isForbid']:
            result['forbid'] = {
                'time': self.parse_time(action['forbid']['forbidTime'])
            }

        return result

    def parse_time(self, time_str):
        if not time_str:
            return None
        value, unit = time_str.split('-')
        value = int(value)
        if unit == 's':
            return timedelta(seconds=value)
        elif unit == 'm':
            return timedelta(minutes=value)
        elif unit == 'h':
            return timedelta(hours=value)
        elif unit == 'd':
            return timedelta(days=value)

    def check_user_type(self, user_id, required_type):
        # 实现用户类型检查逻辑
        pass


class BotGroupConfigAdminService():
        
    @classmethod
    async  def get_bot_group_config(cls, bot_name:str)->BotGroupConfig|None:
        return await BotGroupConfig.get_or_none(bot_name=bot_name)

    @classmethod
    async def save_bot_group_config(cls, bot_name:str, bot_token:str,bot_group_id:int, g_white_users:list,bot_type:str):
        # 保存bot配置
        await BotGroupConfig.create(bot_name=bot_name, bot_token=bot_token, bot_group_id=bot_group_id,g_white_users=g_white_users,bot_type=bot_type)
    
    @classmethod
    async def update_bot_group_config(cls, bot_name:str, bot_token:str,bot_group_id:int):
        # 更新bot配置
        await BotGroupConfig.filter(bot_name=bot_name).update(bot_token=bot_token, bot_group_id=bot_group_id)
        
    @classmethod
    async def delete_bot_group_config(cls, bot_name:str):
        # 删除bot配置
        await BotGroupConfig.filter(bot_name=bot_name).update(deleted=True)
    
    @classmethod
    async def get_all_active_bot_configs(cls) -> list[BotGroupConfig]:
        return await BotGroupConfig.filter(deleted=False).all()
    



class AutoReplyRuleService:
    
    
    
    
    @classmethod
    async def add_author_auto_reply_rule_by_msg_link(cls,msg_link:str,author_username:str)->str:
        log.info(f"add_author_auto_reply_rule_by_msg_link:{msg_link},{author_username}")
        try:
            link_spli = msg_link.split('/')
            group_user_name = link_spli[3]
            msg_id = link_spli[-1]
            
            str = await AutoReplyRuleService.add_author_auto_reply_rule(msg_link=msg_link,author_username=author_username,group_user_name=group_user_name,group_ids=[-1002357871790,-1002223050046])
            
            return str
        except exception as e:
            log.error(f"add_author_auto_reply_rule_by_msg_link error:{e}",exc_info=True)
            return "add_author_auto_reply_rule_by_msg_link error:{msg_link}"
    
    @classmethod
    async def add_author_auto_reply_rule(cls, msg_link:str,author_username:str,group_ids,group_user_name:str) -> str:
        
        log.info(f"add_author_auto_reply_rule:{msg_link},{author_username},{group_ids}")
        
        try:
            link_spli = msg_link.split('/')
            # group_user_name = link_spli[-2]
            msg_id = link_spli[-1]
            
            log.info(f"add_author_auto_reply_rule:{group_user_name},{msg_id}")
            tg_group_msg = await TgGroupMessage.get_or_none(message_id=msg_id,group_user_name=group_user_name)
            
            if tg_group_msg:
                
                for group_id in group_ids:
                    data = {
                        "group_id": group_id,
                        "keyword_type": 22,
                        "keyword_list": author_username,
                        "msg_type": 1,
                        "msg_content": {"msg_text": tg_group_msg.message_text,
                                        "msg_type": 1
                                        },
                        "del_bot_msg_delay": 0,
                        "del_src_msg_delay": 0,
                        "is_quote": False,
                        "is_enable": True
                    }
                    #删除老的规则
                    await AutoReplyRule.filter(group_id=group_id,keyword_type=22,keyword_list=author_username).delete()
                    #添加新的规则
                    rule = await AutoReplyRule.create(**data)
                return tg_group_msg.message_text

            return f"tg_group_msg is null:{msg_link}"
        except exception as e:
            log.error(f"add_author_auto_reply_rule error:{e}",exc_info=True)
            return "add_author_auto_reply_rule error:{msg_link}"
            
    @classmethod
    async def add_auto_reply_rule(cls, data: dict) -> AutoReplyRule:
        """
        添加自动回复规则
        :param data: 自动回复规则的数据字典
        :return: 创建的 AutoReplyRule 对象
        """
        rule = await AutoReplyRule.create(**data)
        return rule

    @classmethod
    async def get_auto_reply_rule(cls, rule_id: int) -> AutoReplyRule | None:
        """
        根据 ID 获取自动回复规则
        :param rule_id: 自动回复规则的 ID
        :return: AutoReplyRule 对象
        """
        try:
            rule = await AutoReplyRule.get(id=rule_id)
            return rule
        except DoesNotExist:
            return None

    @classmethod
    async def update_auto_reply_rule(cls, rule_id: int, data: dict) -> AutoReplyRule:
        """
        更新自动回复规则
        :param rule_id: 自动回复规则的 ID
        :param data: 更新的数据字典
        :return: 更新后的 AutoReplyRule 对象
        """
        rule = await AutoReplyRule.get(id=rule_id)
        await rule.update_from_dict(data)
        await rule.save()
        return rule

    @classmethod
    async def delete_auto_reply_rule(cls, rule_id: int) -> bool:
        """
        删除自动回复规则
        :param rule_id: 自动回复规则的 ID
        :return: 是否成功删除
        """
        rule = await AutoReplyRule.get(id=rule_id)
        await rule.delete()
        return True

    @classmethod
    async def get_all_auto_reply_rules_by_group(cls,group_id :int) -> list[AutoReplyRule]:
        """
        获取所有自动回复规则
        :return: AutoReplyRule 对象的列表
        """
        rules = await AutoReplyRule.filter(group_id=group_id).all()
        return rules


class UserSpamBehaviorService:
    
    @classmethod
    async def get_user_spam_cnt(cls,user_id, group_id, rubbish_type):
        
        try:
            behavior = await UserSpamBehavior.get(user_id=user_id, group_id=group_id, rubbish_type=rubbish_type)
            return behavior.rubbbish_cnt
        except DoesNotExist:
            return 0

    @classmethod
    async def get_user_spam_last_time(cls,user_id, group_id, rubbish_type) -> datetime:
        
        try:
            behavior = await UserSpamBehavior.get(user_id=user_id, group_id=group_id, rubbish_type=rubbish_type)
            return behavior.updated_at
        except DoesNotExist:
            return datetime.now().replace(year=1970)
    
    @classmethod
    async def increase_user_spam_cnt(cls,user_id, group_id, rubbish_type,msg_id:int=0) -> int:
        
        try:
            behavior = await UserSpamBehavior.get(user_id=user_id, group_id=group_id, rubbish_type=rubbish_type)
            behavior.rubbbish_cnt += 1
            await behavior.save()
        except DoesNotExist:
            behavior = await UserSpamBehavior.create(user_id=user_id, group_id=group_id, rubbish_type=rubbish_type, rubbbish_cnt=1)
        
        asyncio.create_task(cls.check_user_spam_cnt(user_id, group_id, rubbish_type,msg_id))
        return behavior.rubbbish_cnt

    @classmethod
    async def exec_spam_rule_action(cls, tg_id:int, group_id:int,msg_id:int,spam_rule:SpamProtectionRuleBO):
        log.info(f"exec_spam_rule_action:{tg_id},{group_id},{spam_rule}")
        
        # 执行垃圾消息处理
        if spam_rule.rubbish_action.alert and spam_rule.rubbish_action.alert.isAlert:
           
            alter_times = spam_rule.rubbish_action.alert.alertTimes
            spam_cnt = 0
        
            behavior = await UserSpamBehavior.get_or_none(user_id=tg_id, group_id=group_id, rubbish_type=spam_rule.rubbish_type)
            if behavior:
                spam_cnt = behavior.rubbbish_cnt
            
                
            group_msg = await TgGroupMessage.filter(group_id=group_id,message_id=msg_id).first()                
            user_name = f'<a href="https://t.me/{group_msg.user_name}">{group_msg.user_name}</a>' if group_msg.user_name else ""
            message_thread_id = group_msg.message_thread_id if group_msg.message_thread_id else 0

            warn_msg = f"{user_name}[{group_msg.user_id}]发送垃圾消息！"
            replay_kb = InlineKeyboardBuilder()
            if spam_cnt < alter_times:
                warn_msg += f"\n操作：被警告{spam_cnt}次({spam_cnt}/{alter_times}),上限{alter_times}次\n"
                replay_kb.add(
                    InlineKeyboardButton(text='✅撤销', callback_data=f'{tg_id}:{OpActionType.ClEAR_ALERT.code}'))
                
            else:
                # 达到阈值，禁言或者封禁用户                
                if spam_rule.rubbish_action.kick and spam_rule.rubbish_action.kick.isKick:
                    # 踢出用户
                    op_action = OpAction(
                        operator_id=-1,
                        operator_nickname="机器人系统",
                        action_type=OpActionType.KICK_1_YEAR,
                        tg_id=tg_id,
                        group_id=group_id
                    )
                    #city[1234567890]发送垃圾消息，累计6次！操作： 已封鎖 🚷 一直到 05/04/2026 09:20
                    kick_time = datetime.now() + timedelta(seconds=OpActionType.KICK_1_YEAR.duration)
                    await group_user_op_service.exec_op_action(op_action)
                    warn_msg += f"累积{spam_cnt}次\n操作：已封鎖 🚷 一直到{kick_time.strftime('%m/%d/%Y %H:%M')}"
                    
                    replay_kb.add(
                        InlineKeyboardButton(text='🔓解封', callback_data=f'{tg_id}:{OpActionType.UNBAN.code}'))
                    
            
            #发送警告消息
            await FFBotMsgBiz.send_to_group_msg_by_group_id(group_id=group_id, msg_text=warn_msg,message_thread_id=message_thread_id,mark_up=replay_kb.as_markup())
            # 转发到干部群里
                        
            u_card,card_mark_p = await GroupUserCardInfoService.get_user_card_info_and_key_markup(tg_id=tg_id)

            u_card = warn_msg + f"\n\n{u_card}"
            await FFBotMsgBiz.forward_warn_msg_ganbu_group(u_card, card_mark_p)
            
            
            
            
            
            
     
    @classmethod
    async def check_user_spam_cnt(cls,user_id:int, group_id:int, rubbish_type:int,msg_id:int):
        log.info(f"check_user_spam_cnt:{user_id},{group_id},{rubbish_type}")
         # 1.发送警告 2. 转发到干部群 3. 超过阈值之后封禁用户
        if rubbish_type == SpamRubbishType.PHOTO_SPAM:

            photo_spam_rule =  await SpamProtectionRule.filter(group_id=group_id,rubbish_type=rubbish_type).first()
            
            if not photo_spam_rule:
                log.info(f"check_user_spam_cnt,photo_spam_rule is null:{user_id},{group_id},{rubbish_type}")
                return False
            else:
                photo_spam_rule_bo = SpamProtectionRuleBO.from_orm(photo_spam_rule)
                log.info(f"check_user_spam_cnt,photo_spam_rule_bo:{photo_spam_rule_bo}")
                # 执行垃圾消息处理
                await cls.exec_spam_rule_action(user_id, group_id, msg_id, photo_spam_rule_bo)

class TgGroupMsgService:
    

    @classmethod
    async def get_daily_top_200__msg_users(cls,exclude_thread_ids: list[int]) -> list[dict]:
        today = datetime.now()
        tomorrow = today + timedelta(days=1)

        top_users = await TgGroupMessage.filter(
            created_at__gte=today,
            created_at__lt=tomorrow
        ).exclude(
            message_thread_id__in=exclude_thread_ids
        ).group_by(
            'user_id'
        ).annotate(
            message_count=Count('id')
        ).order_by('-message_count').limit(200).values('user_id', 'message_count')

        return top_users
    
    
    @classmethod
    async def get_range_top_200__msg_users(cls,start_date:datetime,end_date:datetime,include_thread_ids:list[int],exclude_thread_ids: list[int]) -> list[dict]:

        top_users = await TgGroupMessage.filter(
            created_at__gte=start_date,
            created_at__lt=end_date
        ).filter(
            message_thread_id__in=include_thread_ids
        ).exclude(
            message_thread_id__in=exclude_thread_ids
        ).group_by(
            'user_id'
        ).annotate(
            message_count=Count('id')
        ).order_by('-message_count').limit(200).values('user_id', 'message_count')

        return top_users
    


class BotHandlerManager():
    def __init__(self):
        self.auto_reply_rule_handlers : dict[int,AutoReplyRuleHandler] = {}
        self.spam_protection_rule_handlers : dict[int,SpamProtectionHandler] = {}
        self.welcome_config : dict[int,BotWelcomeConfigHandler] = {}
        self.sender_bot = BotSendMsgPrivate(TMA_BOT_TOKEN,CHAT_BOT_TOKEN) # type: ignore
    
    def add_bot_handler(self,group_id:int,handler:Union[AutoReplyRuleHandler,SpamProtectionHandler,BotWelcomeConfigHandler]):
        
        if isinstance(handler, AutoReplyRuleHandler):
            self.auto_reply_rule_handlers[group_id] = handler
        elif isinstance(handler, SpamProtectionHandler):
            self.spam_protection_rule_handlers[group_id] = handler
        elif isinstance(handler, BotWelcomeConfigHandler):
            self.welcome_config[group_id] = handler
        else:
            log.error(f"add_bot_handler error: {handler}")
    
    async def load_all_handler(self,group_id:int):
        
        log.info(f"load_all_handler group_id:{group_id}")
        
        handler = AutoReplyRuleHandler(group_id)
        await handler.load_rules_from_db()
        asyncio.create_task(handler.start_periodic_loading(60))
        self.add_bot_handler(group_id, handler)
        
        handler = SpamProtectionHandler(group_id)
        await handler.load_rules_from_db()
        asyncio.create_task(handler.start_periodic_loading(60))
        self.add_bot_handler(group_id, handler)
        
        handler = BotWelcomeConfigHandler(group_id)
        await handler.load_bot_welcome_config()
        asyncio.create_task(handler.start_periodic_loading(60))
        self.add_bot_handler(group_id, handler)
        
    async def load_bot_handler(self,bot_id:int):
        
        log.info(f"load_bot_handler bot_id:{bot_id}")
        bot_group_map_list = await BotGroupMap.filter(bot_id=bot_id).all()
        
        for bot_group_map in bot_group_map_list:
            group_id = bot_group_map.group_id
            await self.load_all_handler(group_id)



class BgGroupUserSevrvice():
    """
    处理群组用户相关的操作
    """
    
    @classmethod
    async def add_group_join_user(cls, tg_id:int, group_id:int,bot_id:int):
        await JoinGroupUserProcess.create(tg_id=tg_id,group_id=group_id,bot_id=bot_id)

    
    @classmethod
    async def process_join_group_user(cls, group_id:int):
        """
        处理加入群组的用户
        """
        
        filter_time = datetime.now() - timedelta(hours=3)
        
        # 增加一个天的时间过滤，确保处理的是昨天加入的用户，保证数量和性能
        last_day_time = datetime.now() - timedelta(days=1)
        filter_time = filter_time.astimezone(tz=timezone.utc).replace(tzinfo=None)  # 确保时间是 UTC 格式
        
        last_day_time = last_day_time.astimezone(tz=timezone.utc).replace(tzinfo=None)  # 确保时间是 UTC 格式
        # 确保时间是 UTC 格式
        log.info(f"process_join_group_user:{group_id},filter_time:{filter_time}")
        # 获取加入群组超过 3 小时的用户
        join_users = await JoinGroupUserProcess.filter(group_id=group_id,created_at__lte=filter_time,created_at__gte=last_day_time,process_status="init").order_by('-created_at').limit(15).all()
        
        for join_user in join_users:
            try:
                log.info(f"process_join_group_user,join_user:{join_user}")
                # 检查用户是否未注册
                await cls._check_register_user(join_user.tg_id, group_id,join_user.id)
                # 更新处理状态

                join_user.process_status = "success"
                await join_user.save()
     
            except Exception as e:
                log.error(f"process_join_group_user error:{e}",exc_info=True)
    
    @classmethod
    async def _check_register_user(cls, tg_id:int, group_id:int,join_log_id:int) -> bool:
        """
        检查用户是否未注册
        """
        user_info = await TelegramUser.get_or_none(tg_id=tg_id)
        if not user_info:
            log.info(f"_check_register_user,not register user:{tg_id}")
            
            join_group_history,c_new = await JoinGroupUserProcessHistory.update_or_create(
                tg_id=tg_id,
                group_id=group_id
            )
            
            join_group_history.join_log_id = join_log_id
            if c_new:
                log.info(f"_check_register_user,create join_group_history:{join_group_history}")
                # 首次进群未注册，进行剔除
                
                op_action = OpAction(
                    operator_id=-1,
                    operator_nickname="机器人系统",
                    action_type=OpActionType.KICK,
                    tg_id=tg_id,
                    group_id=group_id
                )
                
                join_group_history.process_status = "success"
                join_group_history.process_reason = "首次进群未注册，进行剔除"
                await join_group_history.save()
                
            else:
                log.info(f"_check_register_user,join_group_history already exists:{join_group_history}")
                # 2次以上进群未注册
                op_action = OpAction(
                    operator_id=-1,
                    operator_nickname="机器人系统",
                    action_type=OpActionType.BAN,
                    tg_id=tg_id,
                    group_id=group_id
                )
                join_group_history.process_status = "success"
                join_group_history.process_reason = "2次以上进群未注册，进行封禁"
                await join_group_history.save()
            
            await group_user_op_service.exec_op_action(op_action)
            
            return False
        else:
            log.info(f"check_not_register_user,register user:{tg_id}")
            return True    

            
            
            
            
            
            
            
