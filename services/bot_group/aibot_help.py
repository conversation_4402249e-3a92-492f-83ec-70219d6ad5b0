

from typing import Any, Dict
from aiogram import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
from aiogram.filters import Command
import logging

from common.models.ai_bot_admin.forward_group_msg import GUserInfoCard
from persistence.models.models_bot_group import BotMessageTypeEnum, TgGroupMessage
from persistence.models.models import User,TelegramUser, UserRegisterSource
from aiogram.types import Update, Message, InlineKeyboardButton, InlineKeyboardMarkup
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram import F
from aiogram.filters import Chat<PERSON>emberUpdatedFilter, IS_MEMBER, IS_NOT_MEMBER, Command
from aiogram.types import ChatMemberUpdated, ChatPermissions
from aiogram.utils.keyboard import InlineKeyboardBuilder

from services.bot_group.group_msg_service import GroupUserCardInfoService

logging.basicConfig(level=logging.INFO)



def remove_none(data: dict):
    return {k: v for k, v in data.items() if v is not None}  


def convert_message_json(message: types.Message):
    #message 不为none的json格式
    data = message.model_dump_json(exclude_none=True, exclude_defaults=True,exclude_unset=True)
    
    return data
    



# 存储消息的中间件
class SaveMessageMiddleware(BaseMiddleware):
    
    @staticmethod
    def get_msg_type(message: Message):
        if message.text:
            return BotMessageTypeEnum.TEXT
        elif message.photo:
            return BotMessageTypeEnum.PHOTO
        elif message.voice:
            return BotMessageTypeEnum.VOICE
        elif message.video:
            return BotMessageTypeEnum.VIDEO
        elif message.audio:
            return BotMessageTypeEnum.AUDIO
        elif message.document:
            return BotMessageTypeEnum.DOCUMENT
        elif message.sticker:
            return BotMessageTypeEnum.STICKER
        elif message.animation:
            return BotMessageTypeEnum.ANIMATION
        else:
            return BotMessageTypeEnum.MESSAGE
        
        
    async def __call__(self, handler, update:Update, data:Dict[str, Any]):
        
        logging.info(f"SaveMessageMiddleware: {update.update_id}")
        
        update_id = update.update_id
        
        if update.message or update.edited_message:
            if update.message:
                event = update.message     
            if update.edited_message:
                event = update.edited_message
            try:
                message_id = event.message_id
                user_id = event.from_user.id
                user_name = event.from_user.username
                full_name = event.from_user.full_name
                
                group_id = event.chat.id
                group_user_name = event.chat.username
                if not group_user_name:
                    group_user_name = ""
                bot_id = event.bot.id
                message_thread_id = event.message_thread_id if event.message_thread_id is not None else 0
                message_text = event.text
                raw_data = convert_message_json(event)
                
                msg_type = self.get_msg_type(event)
                await TgGroupMessage.create(update_id=update_id,message_id=message_id,user_id=user_id, user_name=user_name, group_id=group_id,message_thread_id=message_thread_id, bot_id=bot_id,message_text=message_text,raw_data=raw_data,full_name=full_name,message_type=msg_type,group_user_name=group_user_name)
                
            except Exception as e:
                logging.error(f"SaveMessageMiddleware error: {e}", exc_info=True)

        return await handler(update, data)

class HandleGroupUserMsgMiddleware(BaseMiddleware):
    
    async def __call__(self, handler, update:Update, data:Dict[str, Any]):
        
        logging.info(f"HandleGroupUserMsgMiddleware: {update.update_id}")
        
        update_id = update.update_id
        
        if update.message:
            event = update.message
            try:
                message_id = event.message_id
                user_id = event.from_user.id
                user_name = event.from_user.username
                full_name = event.from_user.full_name
                
                group_id = event.chat.id

                bot_id = event.bot.id

                msg_dict = {
                    "tg_id": user_id,
                    "f_group_id": group_id,
                    "f_bot_id": bot_id,
                    "nickname": full_name,
                    "message_id": message_id,
                    "username": user_name
                }
                await GroupUserCardInfoService.add_user_msg_queue(msg_dict)

            except Exception as e:
                logging.error(f"handle_user_group_msg error update_id:{update_id},msg_id:{message_id},{e}", exc_info=True)

        return await handler(update, data)

# check if the user is a staff member
class StaffGroupCheckMiddleware(BaseMiddleware):
    
    async def __call__(self, handler, update:Update, data:Dict[str, Any]):
        
        logging.info(f"StaffGroupCheckMiddleware begin:{update.update_id}")
        
        update_id = update.update_id
        group_id = 0
        try:
            if update.message:
                event = update.message
                group_id = event.chat.id
            
            elif update.callback_query:
                event = update.callback_query
                group_id = event.message.chat.id
            elif update.edited_message:
                event = update.edited_message
                group_id = event.chat.id
            else:
                return await handler(update, data)
        except Exception as e:
                logging.error(f"StaffGroupCheckMiddleware error update_id:{update_id},{e}", exc_info=True)


        return await handler(update, data)
    
    
class BotSendMsgPrivate:
    def __init__(self, tma_bot_token: str,chat_bot_token: str):
        #TMA 小程序的 FancyTavernBot, CHAT_BOT 是陪聊的，@FancyAI2Bot
        self.tma_bot = Bot(token=tma_bot_token)
        self.chat_bot = Bot(token=chat_bot_token)
        
    async def send_message(self, tg_id: int, message: str):
        
        try:
            tg_user =await TelegramUser.get_or_none(tg_id=tg_id)
            if tg_user:
                user = await User.get_or_none(id=tg_user.uid)
                if user and user.register_source == UserRegisterSource.TMA:
                    await self.tma_bot.send_message(tg_id, message)
                elif user and user.register_source == UserRegisterSource.CHAT_BOT:
                    await self.chat_bot.send_message(tg_id, message)
                else:
                    logging.warning(f"BotSendMsgPrivate error: user not found{user}")
            else:
                logging.warning(f"BotSendMsgPrivate error: tg_user not found{tg_user}")
        except Exception as e:
            logging.error(f"BotSendMsgPrivate error{tg_id},{message}: {e}")

user_info = {
            "nickname": "用户昵称",
            "tg_id": "123456789",
            "registration_time": "2023-01-01 08:00:00",
            "userid": "987654321",
            "total_messages": 100,
            "remaining_score": 50,
            "recent_messages": [
                "这是最近的第一条消息",
                "这是最近的第二条消息",
                "这是最近的第三条消息",
                "这是最近的第四条消息",
                "这是最近的第五条消息"
            ]
        }
    
class ForwardUserInfoMessageService:
    
    
    
    def __init__(self):
        pass
    
    
    def handler_user_group_msg(self,tg_id:int,f_group_id:int,f_bot_id:int):
        logging.info(f"handler_user_group_msg: {tg_id},{f_group_id},{f_bot_id}")
        
        pass
    @staticmethod
    def generate_user_info_message()->str:
        
        
        user_info_message = (
            f"用户信息:\n"
            f"- 用户TG昵称: #{user_info['nickname']}\n"
            f"- 用户TGID: #{user_info['tg_id']}\n"
            f"- 幻梦注册时间: {user_info['registration_time']}\n"
            f"- 幻梦userid: #{user_info['userid']}\n"
            f"- 用户在群里的累计天数: {user_info['total_messages']}\n"
            f"- 用户在幻梦的累计充值金额: {user_info['remaining_score']}\n"
            f"- 用户今日发的消息展示最近5条\n"
        )
        for message in user_info["recent_messages"]:
            user_info_message += f"  - {message}\n"
        return user_info_message
    
    @staticmethod
    def gen_u_info_keybord_markup() -> InlineKeyboardMarkup:
        # 生成内联键盘按钮
        inline_kb = InlineKeyboardBuilder()

        msg_cx_kb = InlineKeyboardBuilder()
        msg_cx_kb.add(
                InlineKeyboardButton(text="发言查询", callback_data='choose_next_button'),
                InlineKeyboardButton(text="今日发言", callback_data='today_msg_cx'),
                InlineKeyboardButton(text="7日发言", callback_data='7day_msg_cx'),
            )
        
        d_msg_kb = InlineKeyboardBuilder()
        d_msg_kb.add(
            InlineKeyboardButton(text='删除所有消息', callback_data='delete_all')
        )
        forbid_kb = InlineKeyboardBuilder()
        forbid_kb.add(
            InlineKeyboardButton(text='禁言5分钟', callback_data='mute_5min'),
            InlineKeyboardButton(text='禁言10分钟', callback_data='mute_10min'),
            InlineKeyboardButton(text='禁言30分钟', callback_data='mute_30min')
        )
        kick_ban_kb = InlineKeyboardBuilder()
        kick_ban_kb.add(
            InlineKeyboardButton(text='踢出', callback_data='kick'),
            InlineKeyboardButton(text='封锁', callback_data='ban')
        )
        
        inline_kb.attach(msg_cx_kb)
        inline_kb.attach(d_msg_kb).attach(forbid_kb).attach(kick_ban_kb)
        return inline_kb.as_markup()
    
    
    