from datetime import datetime
from decimal import Decimal
import hashlib
import logging
import requests
import os
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
)
from utils import env_const
from services import out_recharge_common

jlbzf_recharge_notify_url = f'{os.environ['TG_WEBHOOK_URL']}/jlbzf/notify'
jlbzf_recharge_return_url = f'{os.environ['TG_WEBHOOK_URL']}/jlbzf/result'

APP_PAY_ID = env_const.JLBZF_APP_ID
APP_PAY_KEY = env_const.JLBZF_APP_KEY
APP_PAY_HOST = env_const.JLBZF_HOST

async def create_recharge_order_with_product(user_id: int, recharge_product: RechargeProduct, bot_id: int = 0) -> RechargeOrder | None:
    return await out_recharge_common.create_recharge_order_with_product(user_id, recharge_product, RechargeChannelEnum.JLBZF, bot_id)

async def update_out_order_id(recharge_order_id: str, out_order_id: str, pay_url: str | None = None):
    return await out_recharge_common.update_out_order_id(recharge_order_id, out_order_id, pay_url)

async def pay_success(recharge_order_id: str,
                      out_order_id: str,
                      raw_data: str) -> RechargeOrder:
    return await out_recharge_common.pay_success(recharge_order_id, out_order_id, raw_data)

def create_jlbzf_order(order: RechargeOrder, type: str, client_ip: str):
    amount = f'{(Decimal(order.pay_fee) / 1000 / 100):.2f}'
    # if order.pay_fee < 1000000:
    #     amount = '10'
    pay_bankcode = 1 if type == 'alipay' else '2'
    params = {
        'pay_memberid': APP_PAY_ID,
        'pay_orderid': order.recharge_order_id,
        'pay_applydate': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'pay_bankcode': pay_bankcode,
        'pay_notifyurl': jlbzf_recharge_notify_url,
        'pay_callbackurl': jlbzf_recharge_return_url,
        'pay_amount': amount,
    }
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    sign_str = '&'.join(f'{k}={v}' for k, v in sorted_params) + f'&key={APP_PAY_KEY}'
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    params['pay_md5sign'] = sign.upper()

    # post params as application/x-www-form-urlencoded
    resp = requests.post(APP_PAY_HOST, 
                        data=params, 
                        proxies=env_const.RECHARGE_PROXY,
                        verify=False)

    logging.info(f'jlbzf_recharge_response: {resp.text}')
    return resp.json()