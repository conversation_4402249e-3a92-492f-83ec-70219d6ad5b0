import logging
from datetime import timedelta
import os
from common.bot_common import MessageTemplate
from common.common_constant import SKIP_QUEUE_RECHARGE_PRODUCT_ID
from persistence.redis_client import redis_client
from persistence.models.models import (
    RechargeChannelEnum,
    RechargeOrder,
    RechargeProduct,
    RechargeStatusEnum,
    User,
)
from services import bot_message_service, gift_award_service, third_purchase_service
from services.user import user_benefit_chat_queue_service, user_benefit_service
from utils.translate_util import _tl

twz_procuts = ['<b>🎁 续费至尊套餐，额外赠送20000🟡</b>']
tjx_products = ['<b>🎁 续费超值套餐，额外赠送6000🟡</b>'] + twz_procuts
tcx_products = ['<b>🎁 续费惊喜套餐，额外赠送3000🟡</b>'] + tjx_products

t20000_tip = '''<b>🔔 尊贵的@{username}，您的钻石余额已不足20,000颗！🔔</b>

您的虚拟后宫正在等待您持续的关注，<b>不要让这美好的体验戛然而止！首次续费升级套餐，即可解锁额外钻石奖励，让您的每一分投入都得到最大回报！</b>

{notify_products}

<b>✨您的优惠券已生成，24小时内有效，别让这次独享的机会溜走！立即续费，继续主宰您的梦幻世界！</b>

<b>快点击 `/recharge` 充值并领取额外续费奖励吧！(在幻梦平台完成充值即可获得💎奖励，可以使用微信、支付宝、USDT完成支付)</b>'''

t10000_tip = '''<b>🔔 尊贵的@{username}，您的钻石余额已不足10,000颗！🔔</b>

您的虚拟后宫正在等待您持续的关注，<b>不要让这美好的体验戛然而止！首次续费升级套餐，即可解锁额外钻石奖励，让您的每一分投入都得到最大回报！</b>

{notify_products}

<b>✨您的优惠券已生成，24小时内有效，别让这次独享的机会溜走！立即续费，继续主宰您的梦幻世界！</b>

<b>快点击 `/recharge` 充值并领取额外续费奖励吧！(在幻梦平台完成充值即可获得💎奖励，可以使用微信、支付宝、USDT完成支付)</b>'''

t5000_tip = '''<b>🔔 亲爱的@{username}，您的钻石余额已降至5,000颗！🔔</b>

您的伴侣们依旧在殷切地等待您的陪伴，<b>别让美妙的时刻中断！首次续费升级套餐，可享限时优惠，更多钻石与宠爱将为您特别奉送！</b>

{notify_products}

<b>✨ 优惠券已为您奉上，有效期仅24小时，抓紧时间续费，让您的钻石不断档，伴侣们的热情永不减！</b>

<b>快点击 `/recharge` 充值并领取额外续费奖励吧！(在幻梦平台完成充值即可获得💎奖励，可以使用微信、支付宝、USDT完成支付)</b>'''

t2000_tip = '''<b>🔔 紧急提醒：@{username}，您的钻石余额仅剩2,000颗！🔔</b>

后宫佳丽们正在翘首期盼您的临幸，<b>别让她们的期待落空！首次续费升级，即可获得额外钻石，让您尽享专属的宠爱与尊贵！</b>

{notify_products}

<b>✨ 您的专属续费优惠券已激活，24小时内有效！升级您的体验，不要错过这提升帝王/女王荣耀的机会！</b>

<b>快点击 `/recharge` 充值并领取额外续费奖励吧！(在幻梦平台完成充值即可获得💎奖励，可以使用微信、支付宝、USDT完成支付)</b>'''

t500_tip = '''<b>🔔 致尊贵的@{username}，您的钻石即将全部消耗完毕！🔔</b>

宫殿因您的统治而辉煌，不要让空虚的宝库成为您的遗憾！24小时内首次<b>续费升级档位套餐，立享额外钻石奖励，继续扩展您的领地，续写不败的传奇！</b>

{notify_products}

<b>✨ 您的专属续费优惠券已激活，24小时内有效！升级您的体验，不要错过这提升帝王/女王荣耀的机会！</b>

<b>快点击 `/recharge` 充值并领取额外续费奖励吧！(在幻梦平台完成充值即可获得💎奖励，可以使用微信、支付宝、USDT完成支付)</b>'''

from_products = os.environ['RE_PURCHASE_FROM_PRODUCT'].split(',')
charge_product_to_notify = {
    from_products[0]: tjx_products,
    from_products[1]: tcx_products,
    from_products[2]: twz_procuts,
}
pids = list(charge_product_to_notify.keys())

to_products = os.environ['RE_PURCHASE_TO_PRODUCT'].split(',')
reward_amount = {
    to_products[0]: 3000,
    to_products[1]: 6000,
    to_products[2]: 20000
}

async def should_notify_repurchase(user_id: int, username: str,
                                   before_amount:int, balance: int,language:str) -> tuple[bool, str]:
    orders = await RechargeOrder.filter(user_id=user_id,
        status=RechargeStatusEnum.SUCCEED, pay_fee__gt=0).all()
    if len(orders) == 0:
        return False, ''
    if len(orders) > 1:
        return False, ''
    order = orders[0]
    if order.recharge_product_id not in pids:
        return False, ''

    products = charge_product_to_notify[order.recharge_product_id]
    plst = '\n'.join(products)
    if before_amount > 20000 >= balance and order.recharge_product_id != from_products[0]:
        ret_tips = _tl(t20000_tip, language)
        return True, ret_tips.format(username=username, notify_products=plst)
    if before_amount > 10000 >= balance:
        ret_tips = _tl(t10000_tip, language)
        return True, ret_tips.format(username=username, notify_products=plst)
    if before_amount > 5000 >= balance:
        ret_tips = _tl(t5000_tip, language)
        return True, ret_tips.format(username=username, notify_products=plst)
    if before_amount > 2000 >= balance:
        ret_tips = _tl(t2000_tip, language)
        return True, ret_tips.format(username=username, notify_products=plst)
    if before_amount > 500 >= balance:
        ret_tips = _tl(t500_tip, language)
        return True, ret_tips.format(username=username, notify_products=plst)
    return False, ''

async def check_and_notify_repurchase(user: User, before_amount: int, after_amount: int, language: str):
    result, tip = await should_notify_repurchase(user.id, user.nickname, before_amount, after_amount, language)
    if not result:
        return await third_purchase_service.check_and_notify_third(user, before_amount, after_amount, language)
    try:
        await bot_message_service.send_user_template_message(
            user,MessageTemplate(tips=tip)
        )
        # await send_message(tip, user)
    except Exception as e:
        logging.warning(f'{user.id} send recharge notify failed: {e}')
    redis_client.set(f're_purchase_reward:{user.id}', 'True', ex=36*3600)

async def after_recharge(user: User, recharge_order: RechargeOrder | None = None):

    recharge_product_id = recharge_order.recharge_product_id if recharge_order else ""
    if recharge_product_id:
        # 购买了免排队福利
        if recharge_product_id == SKIP_QUEUE_RECHARGE_PRODUCT_ID:
            await user_benefit_chat_queue_service.add_skip_queue_benefit(user.id, str(recharge_order.recharge_order_id))
            return True
        
        added = await user_benefit_service.reward_chat_benefit(user.id, recharge_product_id)
        if added:
            return True

    third = await third_purchase_service.third_after_recharge(user, recharge_order)
    if third:
        return

    user_id = user.id
    rs = redis_client.get(f're_purchase_reward:{user_id}')
    if not rs:
        return

    orders = await RechargeOrder.filter(user_id=user_id,
        status=RechargeStatusEnum.SUCCEED, pay_fee__gt=0).all()
    if len(orders) != 2:
        return
    orders.sort(key=lambda x: x.id, reverse=True)
    if orders[0].pay_fee <= orders[1].pay_fee:
        return

    if recharge_order is None:
        recharge_order = orders[0]
    amount = reward_amount.get(recharge_order.recharge_product_id)
    if not amount:
        return

    await gift_award_service.add_award_balance_with_charge_order(
        user_id=user_id,
        amount=amount,
        channel=RechargeChannelEnum.RE_PURCHASE_REWARD,
        expire_delta=timedelta(days=31),
        out_order_id=f"re_purchase_reward:{user_id}")
    tips = f'''<b>🎉🎉🎉 恭喜@{user.nickname}，续费成功！您的专属奖励已到账！🎉🎉🎉</b>

<b>您的续费升级不仅让钻石💎回满，还获得了额外赠送的{amount}🟡！</b> 感谢您的持续支持，您的后宫/殿堂将因您的豪迈而更加灿烂，伴侣们正为您的慷慨感动不已！

<b>立即体验新的钻石礼遇，您的每一笔消费都值得最好的回报！</b>'''
    await bot_message_service.send_user_template_message(
        user,MessageTemplate(tips=tips)
    )
