import datetime
import logging
import os

from aiogram import Bo<PERSON>
from aiogram.enums import ParseMode

from aiogram.filters.callback_data import CallbackData
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.bot_common import Button, MessageTemplate
from common.common_constant import (
    ActionType,
    BotReplace,
    ChatChannel,
    ChatPlatform,
    Env,
    Language,
    PopupPosition,
    PopupShowPeriod,
    PopupType,
    PopupUserScope,
    ProductPermission,
)
from common.operation_model import Popup
from controllers.bot_hooks import bot_setting
from controllers.bot_hooks.bot_services import bot_model_switch
from persistence.models.models import (
    PopupConfig,
    PopupRecord,
    User,
    UserStatus,
    WelfareTask,
)
from persistence import chat_history_dao
from services import (
    bot_message_service,
    bot_services,
    tg_message_service,
    user_service,
    welfare_service,
)
from services import product_service
from services.user import user_benefit_service
from utils import date_util, env_util
from utils.exception_util import async_ignore_catch_exception
from utils.translate_util import _tl

log = logging.getLogger(__name__)


def _role_create_identifier(user_id: int):
    return f"{PopupType.ROLE_CREATE.value}${user_id}"


def _init_role_create_popup(user_id: int):
    return Popup(
        type=PopupType.ROLE_CREATE.value,
        content="除「幻梦平台」的角色卡，您还可以创建专属的角色卡哦😘～（昔日的男神女神，你懂的❤️🫣）",
        button_text="去创建",
        button_action_type=ActionType.INNER_LINK.value,
        button_link_url="/create/myroles",
    )


def create_identifier(popup_type: PopupType, user_id: int):
    return f"{popup_type.value}${user_id}"


def _pay_switch_identifier(user_id: int):
    return f"{PopupType.PAY_SWITCH_MODEL.value}${user_id}"


def _bot_pay_switch_identifier(user_id: int):
    return f"{PopupType.BOT_PAY_SWITCH_MODEL.value}${user_id}"


def _init_pay_switch_model_popup(user_id: int):
    return Popup(
        type=PopupType.PAY_SWITCH_MODEL.value,
        title="解锁新模式! !",
        content="已解锁新的聊天模式，反应回复速度更快，文笔炸裂，AI记忆更强，支持更复杂的语言逻辑,强烈推荐[世界卡模式]",
        extra="m6",
    )


async def _pay_user(user: User) -> bool:
    if user.status == UserStatus.ADMIN.value:
        return True
    return await user_service.is_payed_user(user.id)


async def _init_pay_switch_model_popup_record(user_id: int):
    user = await user_service.get_by_id(user_id)
    if not await _pay_user(user):
        return None
    try:
        record = await PopupRecord().filter(
            identifier=_pay_switch_identifier(user.id),
            user_id=user.id,
            type=PopupType.PAY_SWITCH_MODEL.value,
        )
        if record:
            return None
        record = await PopupRecord.create(
            type=PopupType.PAY_SWITCH_MODEL.value,
            identifier=_pay_switch_identifier(user.id),
            user_id=user.id,
            read=True,
        )
        product = await product_service.get_user_chat_product(user)
        if not product:
            return None
        if product.permission != ProductPermission.PAID_USER.value:
            return record
        return None
    except Exception as e:
        return None


async def _init_bot_pay_switch_model_popup_record(user_id: int):
    user = await user_service.get_by_id(user_id)
    if not await _pay_user(user):
        return None
    try:
        record = await PopupRecord().filter(
            identifier=_bot_pay_switch_identifier(user.id),
            user_id=user.id,
            type=PopupType.PAY_SWITCH_MODEL.value,
        )
        if record:
            return None
        record = await PopupRecord.create(
            type=PopupType.BOT_PAY_SWITCH_MODEL.value,
            identifier=_bot_pay_switch_identifier(user.id),
            user_id=user.id,
            read=True,
        )
        product = await product_service.get_user_chat_product(user)
        if not product:
            return None
        if product.permission != ProductPermission.PAID_USER.value:
            return record
        return None
    except Exception as e:
        return None


ID_MAP = {
    PopupType.ROLE_CREATE: _role_create_identifier,
    PopupType.PAY_SWITCH_MODEL: _pay_switch_identifier,
    PopupType.BOT_PAY_SWITCH_MODEL: _bot_pay_switch_identifier,
}
INIT_POPUP_MAP = {
    PopupType.ROLE_CREATE: _init_role_create_popup,
    PopupType.PAY_SWITCH_MODEL: _init_pay_switch_model_popup,
    PopupType.BOT_PAY_SWITCH_MODEL: _init_pay_switch_model_popup,
}
INIT_RECORD_MAP = {
    PopupType.PAY_SWITCH_MODEL: _init_pay_switch_model_popup_record,
    PopupType.BOT_PAY_SWITCH_MODEL: _init_bot_pay_switch_model_popup_record,
}


async def exist_record(user_id: int, popup_type: PopupType):
    f = ID_MAP.get(popup_type)
    if not f:
        return False
    identifier = f(user_id)
    popup = await PopupRecord.filter(user_id=user_id, identifier=identifier).first()
    return bool(popup)


async def pull_popup_message(user_id: int, popup_type: PopupType):

    init_id_method = ID_MAP.get(popup_type)
    init_popup_method = INIT_POPUP_MAP.get(popup_type)
    if not init_id_method or not init_popup_method:
        return None
    identifier = init_id_method(user_id)
    popup = await PopupRecord.filter(user_id=user_id, identifier=identifier).first()
    if popup and popup.read:
        return None
    if popup and not popup.read:
        ret = (
            await PopupRecord.select_for_update()
            .filter(user_id=user_id, identifier=identifier)
            .update(read=True)
        )
        return init_popup_method(user_id) if ret else None

    # 记录为空，初始化记录
    init_record_method = INIT_RECORD_MAP.get(popup_type)
    if not init_record_method or not await init_record_method(user_id):
        return None
    return init_popup_method(user_id)


# async def after_chat_message(user: User, role_id: int):
#     if await exist_record(user.id, PopupType.ROLE_CREATE):
#         return

#     chat_count = await chat_history_dao.count_ai_msg_by_role_id(user.id, role_id)
#     if chat_count < 15:
#         return
#     try:
#         await PopupRecord(
#             type=PopupType.ROLE_CREATE.value,
#             identifier=_role_create_identifier(user.id),
#             user_id=user.id,
#             read=False,
#         ).save()
#     except Exception as e:
#         log.warn("error", e)
#         return

#     tips = """
#         除「幻梦平台」的角色卡，您还可以创建专属的角色卡哦😘～（昔日的男神女神，你懂的❤️🫣）
#     """

#     url = f"https://t.me/{BotReplace.MAIN_TMA_BOT.value}/tavern?startapp=u_27-r_-e_eyJwIjoiaGlzdG9yeS9teXJvbGVzIn0"
#     log.info(f"send role create popup to user {user.id},tips:{tips}")
#     template_message = MessageTemplate(
#         tips=tips,
#         buttons=[Button(url=url, text="去创建")],
#     )
#     template_message = await bot_message_service.format_template_replace(
#         template_message
#     )
#     await bot_message_service.send_user_template_message(user, template_message)
# await bot_services.send_message_replay_markup(
#     tips, user, ParseMode.HTML, start_markup
# )


# @async_ignore_catch_exception
# async def check_bot_pay_switch_message(user: User, chat_id: int, bot: Bot):
#     popup = await pull_popup_message(user.id, PopupType.BOT_PAY_SWITCH_MODEL)
#     if not popup:
#         return False
#     template = await bot_model_switch.model_select_template(user)
#     if not template:
#         return False
#     message = await bot.send_message(
#         chat_id,
#         text=template.tips,
#         parse_mode=ParseMode.HTML,
#         reply_markup=template.as_markup(),
#     )
#     await tg_message_service.add_deleted_message(
#         message, expire_delta=datetime.timedelta(minutes=2), bot_id=bot.id
#     )
#     return True

# user_llm_model = (
#     user.llm_model if user.llm_model else os.environ.get("ai_model", "")
# )
# user_product = await product_service.get_chat_product_by_model(user_llm_model)
# display_name = user_product.model_name if user_product else "未选择"
# text = f"当前模型：{display_name}\n\n点击按钮选择模型："
# builder = InlineKeyboardBuilder()
# model_ids = ["m1", "m6"]
# products = await product_service.list_chat_product_new()
# products = [x for x in products if x.mid in model_ids]
# for m in products:
#     builder.button(text=m.model_name, callback_data=ModelSelected(mid=m.mid))
#     text += f"\n\n{m.model_name}：{m.desc} ({m.price}💎/条)"

# await bot_services.send_message_replay_markup_by_chat_bot(
#     text, user, ParseMode.HTML, builder.as_markup(), chat_id
# )


async def list_popup_config():
    return await PopupConfig.all()


async def pull_common_popup_message_by_language(
    user: User,
    popup_type: PopupType,
    chat_platform: ChatPlatform,
    position: PopupPosition,
    language: str = Language.ZH.value,
):
    config = await pull_common_popup_message(user, popup_type, chat_platform, position)
    if not config:
        return None
    config.title = _tl(config.title, language, PopupConfig.__name__)
    config.content = _tl(config.content, language, PopupConfig.__name__)
    config.button_text = _tl(config.button_text, language, PopupConfig.__name__)
    return config


async def pull_common_popup_message(
    user: User,
    popup_type: PopupType,
    chat_platform: ChatPlatform,
    position: PopupPosition,
):
    user_id = user.id
    config_list = await PopupConfig.filter(
        position=position.value, published=True
    ).all()
    if not config_list:
        return None

    def check_scope(config: PopupConfig):
        if not config.user_scopes:
            return False
        now_time = date_util.now().timestamp()
        if PopupUserScope.ALL.value in config.user_scopes:
            return True
        if (
            PopupUserScope.POPUP_AFTER_REG.value in config.user_scopes
            and config.start_at.timestamp() < user.created_at.timestamp()
        ):
            return True
        diff_timestamp = now_time - user.created_at.timestamp()
        if (
            PopupUserScope.REG_24H.value in config.user_scopes
            and diff_timestamp < 24 * 3600
        ):
            return True
        if (
            PopupUserScope.REG_72H.value in config.user_scopes
            and diff_timestamp < 72 * 3600
        ):
            return True
        if (
            PopupUserScope.AFTER_REG_24H.value in config.user_scopes
            and diff_timestamp > 24 * 3600
        ):
            return True
        if (
            PopupUserScope.AFTER_REG_72H.value in config.user_scopes
            and diff_timestamp > 72 * 3600
        ):
            return True
        return False

    def valid_config(config: PopupConfig):
        now_time = int(date_util.now().timestamp())
        if now_time < config.start_at.timestamp():
            return False
        if now_time > config.end_at.timestamp():
            return False
        if chat_platform.value not in config.chat_platform:
            return False
        return True

    valid_list = [x for x in config_list if valid_config(x) and check_scope(x)]
    valid_list.sort(key=lambda x: x.updated_at, reverse=True)
    every_time_list = [
        config
        for config in valid_list
        if config.show_period == PopupShowPeriod.EVERY_TIME.value
    ]
    if every_time_list:
        return every_time_list[0]

    def unique_id(config: PopupConfig):
        return_id = f"{config.id}"
        now_datetime = date_util.now(tz_offset=8)
        show_period = config.show_period
        if show_period == PopupShowPeriod.EVERY_DAY.value:
            return f"{return_id}${show_period}${now_datetime.strftime('%Y%m%d')}"
        if show_period == PopupShowPeriod.EVERY_WEEK.value:
            return f"{return_id}${show_period}${now_datetime.strftime('%Y%W')}"
        if show_period == PopupShowPeriod.EVERY_8H.value:
            index = now_datetime.hour / 8
            return (
                f"{return_id}${show_period}${now_datetime.strftime('%Y%m%d')}${index}"
            )
        if show_period == PopupShowPeriod.ONCE.value:
            return f"{return_id}${show_period}"
        raise ValueError(f"invalid show_period {show_period}")

    record_ids = [unique_id(config) for config in valid_list]
    records = await PopupRecord.filter(
        identifier__in=record_ids, user_id=user_id, type=popup_type.value
    ).all()
    db_record_ids = [record.identifier for record in records]
    record_ids = [x for x in record_ids if x not in db_record_ids]
    valid_list = [config for config in valid_list if unique_id(config) in record_ids]
    if not valid_list:
        return None
    return_config = valid_list[0]
    await PopupRecord.create(
        identifier=unique_id(return_config),
        user_id=user_id,
        type=popup_type.value,
        read=True,
    )
    return return_config


async def get_popup_record(user_id: int, identifier: str, popup_type: PopupType):
    return await PopupRecord.filter(
        user_id=user_id, identifier=identifier, type=popup_type.value
    ).first()


async def add_popup_record(user_id: int, identifier: str, popup_type: PopupType):
    try:
        return await PopupRecord.create(
            identifier=identifier,
            user_id=user_id,
            type=popup_type.value,
            read=True,
        )
    except Exception as e:
        log.warning(
            f"add popup record failed,user_id:{user_id},identifier:{identifier}"
        )
        return None


class ComCallbackCommand(CallbackData, prefix="com_command"):
    command: str


async def send_bot_popup(
    bot: Bot,
    user: User,
    tg_user_id: int,
    position: PopupPosition,
    language: str = Language.ZH.value,
):
    if language == Language.EN.value:
        return
    record = await pull_common_popup_message_by_language(
        user, PopupType.COMMON, ChatPlatform.CHAT_BOT, position, language
    )
    if not record:
        return
    content = f"{record.title}\n\n{record.content}"
    try:
        template = MessageTemplate(tips=content)
        if (
            record.button_action_type
            in [ActionType.LINK.value, ActionType.OUTER_LINK.value]
            and record.button_link_url
        ):
            button = Button(
                url=record.button_link_url,
                text=record.button_text,
            )
            template.buttons.append(button)
        if (
            record.button_action_type == ActionType.CALLBACK_CMD.value
            and record.button_link_url
        ):
            button = Button(
                callback_data=ComCallbackCommand(command=record.button_link_url),
                text=record.button_text,
            )
            template.buttons.append(button)
        message = await bot.send_message(
            tg_user_id,
            content,
            parse_mode=ParseMode.HTML,
            reply_markup=template.as_markup(),
        )
        await tg_message_service.add_deleted_message(
            message, "CHAT_BOT", datetime.timedelta(minutes=1), bot.id
        )
    except Exception as e:
        log.warning(f"send bot message failed, {e}")
        return
    return


async def pull_and_send_common_popup_message(
    bot: Bot,
    user: User,
    tg_user_id: int,
    chat_platform: ChatPlatform,
    position: PopupPosition,
):
    record = await pull_common_popup_message(
        user, PopupType.COMMON, chat_platform, position
    )
    if not record:
        return

    content = f"{record.title}\n\n{record.content}"
    try:
        message = await bot.send_message(tg_user_id, content, parse_mode=ParseMode.HTML)
        await tg_message_service.add_deleted_message(
            message, "CHAT_BOT", datetime.timedelta(minutes=1), bot.id
        )
    except Exception as e:
        log.warning(f"send bot message failed, {e}")
        return
    return


async def new_user_benefit_popup(user: User, language: str):
    user_id = user.id
    identifier = create_identifier(PopupType.NEW_USER_BENEFIT, user_id)
    record = await get_popup_record(user_id, identifier, PopupType.NEW_USER_BENEFIT)
    if record:
        return None
    record = await add_popup_record(
        user_id,
        identifier,
        PopupType.NEW_USER_BENEFIT,
    )
    if not record:
        return None
    received = await welfare_service.check_user_received_benefits(user_id)
    if received:
        return None

    msg_count = await chat_history_dao.count_human_msg(user_id)
    if msg_count > 0:
        return None
    ret = await welfare_service.receive_benefit(user,language)
    if not ret:
        return None
    return Popup(
        type=PopupType.NEW_USER_BENEFIT.value,
        title=_tl("已自动发放60次免费聊天权益", language, WelfareTask.__name__),
        content=_tl(
            "聊天使用免费权益对应的聊天模式，聊天不花钱！（每天都可以领取哦）",
            language,
            WelfareTask.__name__,
        ),
        button_text=_tl("去聊天", language, WelfareTask.__name__),
    )


async def bot_new_user_benefit_popup_auto(user: User, bot: Bot, tg_id: int,language:str):
    popup = await new_user_benefit_popup(user, language)
    if not popup:
        return None
    tips = f"{popup.title}\n\n{popup.content}\n\n{_tl("（已自动领取）",language)}"
    await bot_message_service.send_by_tips(bot, tg_id, tips, auto_deleted=True)

    benefit_maps = await user_benefit_service.map_valid_by_all_product_mids(user)
    products = await product_service.list_display_chat_product(language)
    products.sort(key=lambda x: x.price, reverse=True)
    products = [
        product
        for product in products
        if product.mid in benefit_maps
        and benefit_maps[product.mid].sum_remain_times > 0
    ]
    if not products:
        return None
    product = products[0]
    await bot_model_switch.auto_select(user, ChatChannel.FREE_BENEFIT, product.mid)
    chat_channel = _tl(ChatChannel.FREE_BENEFIT.short_display(), language)
    tips = _tl("已切换模型为: {short_name}（{chat_channel}）\n\n", language)
    tips = tips.format(short_name=product.short_name, chat_channel=chat_channel)
    await bot_message_service.send_by_tips(bot, tg_id, tips, auto_deleted=True)
    return popup


async def bot_new_user_benefit_popup_manual(
    user: User, bot: Bot, tg_id: int, language: str
):
    identifier = create_identifier(PopupType.NEW_USER_BENEFIT_NON_CHANNEL, user.id)

    record = await get_popup_record(
        user.id, identifier, PopupType.NEW_USER_BENEFIT_NON_CHANNEL
    )
    if record:
        return None
    ret = await welfare_service.exist_receive_tasks(user.id)
    if not ret:
        return None
    record = await add_popup_record(
        user.id,
        identifier,
        PopupType.NEW_USER_BENEFIT_NON_CHANNEL,
    )
    if not record:
        return None
    tips = _tl(
        "领取免费权益，使用免费权益对应的聊天模式，聊天不花钱！（每天都可以领取哦）",
        language,
        WelfareTask.__name__,
    )
    btn_text = _tl("领取免费权益", language, WelfareTask.__name__)
    button = Button(
        text=btn_text, callback_data=ComCallbackCommand(command="free_benefit")
    )
    template = MessageTemplate(
        tips=tips,
        buttons=[button],
    )
    template = await bot_message_service.send_by_template(
        bot, tg_id, template, auto_deleted=True
    )
    return True
