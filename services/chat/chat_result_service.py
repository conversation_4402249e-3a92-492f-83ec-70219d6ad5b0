from datetime import datetime
import logging
from typing import Optional
from common.common_constant import LiteLlmErrorType
from common.models.chat_model import ChatNextInput
from common.models.mongo_model import Chat<PERSON><PERSON>rRecord
from persistence import chat_error_dao
from services import model_service
from utils import date_util, exception_util


log = logging.getLogger(__name__)


# Error handler for chat operations
@exception_util.async_ignore_catch_exception
async def error_handler(
    input: ChatNextInput,
    exception: Optional[Exception] = None,
    error_type: Optional[str] = None,
    error_message: Optional[str] = None,
    extra: Optional[dict] = None,
):
    await model_service.log_model_request(input.preset_model, 0)
    if not error_type and exception:
        error_type = exception_util.llm_exception_to_type(exception)
    if not error_message and exception:
        error_message = str(exception)

    log.error(
        "ChatRetError uid: %s, conversation_id: %s, preset_model: %s, request_model: %s, error_type: %s, error_message: %s",
        input.user_id,
        input.conversation_id,
        input.preset_model,
        input.request_model,
        error_type if error_type else "None",
        error_message if error_message else "None",
    )
    record = ChatErrorRecord(
        user_id=input.user_id,
        conversation_id=input.conversation_id,
        preset_model=input.preset_model,
        request_model=input.request_model,
        error_type=error_type if error_type else LiteLlmErrorType.UNKNOWN.value,
        exception_message=error_message if error_message else "",
        extra=extra if extra else {},
        created_at=int(datetime.now().timestamp()),
        updated_at=int(datetime.now().timestamp()),
    )

    await chat_error_dao.insert_message(record)


async def get_max_error_record(
    preset_model: str,
):
    records = await chat_error_dao.list_error_records(
        preset_model=preset_model,
        limit=20,
    )
    if not records:
        return 0, None
    # 按照error_type分组
    error_record = records[0]
    error_type_count = {}
    for record in records:
        error_type = record.error_type
        error_type_count[error_type] = error_type_count.get(error_type, 0) + 1
        if error_type_count[error_type] > error_type_count.get(
            error_record.error_type, 0
        ):
            error_record = record
    count = error_type_count.get(error_record.error_type, 0)
    return count, error_record


async def get_monitor_error_record(preset_model: str):

    _, error_record = await get_max_error_record(
        preset_model=preset_model,
    )
    return error_record
