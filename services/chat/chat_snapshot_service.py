import logging
import re
from ai import lite_llm_bot
from common.common_constant import (
    COMMON_USER_NAME,
    <PERSON>rror<PERSON><PERSON>,
    <PERSON>rror<PERSON>ey,
    Language,
    LlmModel,
    PresetReplace,
    ProductType,
)
from common.models.chat_model import ChatHistoryType
from common.role_model import (
    CardDetail,
    RoleDataConfig,
    UserRoleBrief,
    UserSnapshotCardDetail,
)
from persistence import chat_history_dao
from persistence.models.models import UrsConversationRela<PERSON>, User, UserRoleSnapshot
from presets import snapshot_preset
from services import account_service, product_service, user_service
from services.chat import chat_message_service
from services.role import role_loader_service
from utils import exception_util, json_util, message_utils, role_util, str_util
from utils.translate_util import _tl


async def get_user_role_snapshot_by_id(
    user_snapshot_id: int, format: bool = False, language: str = Language.ZH.value
) -> UserRoleSnapshot | None:
    if not user_snapshot_id:
        return None
    snapshot = await UserRoleSnapshot.filter(id=user_snapshot_id).first()
    if not snapshot:
        return None
    if format and snapshot.role_id:
        user = await user_service.get_user_by_id(snapshot.user_id)
        role_name_map = await role_loader_service.map_translated_role_names(
            [snapshot.role_id], language
        )
        role_name = role_name_map.get(snapshot.role_id, "")
        snapshot.scenario = str_util.format_char_and_user(
            snapshot.scenario, role_name, user.nickname
        )
    return snapshot


async def get_user_role_snapshot_by_conv_id(
    user_id: int, conv_id: str, format: bool = False, language: str = Language.ZH.value
) -> UserRoleSnapshot | None:
    relation = await UrsConversationRelation.filter(
        user_id=user_id, conversation_id=conv_id
    ).first()
    if not relation:
        return None
    snapshot = await UserRoleSnapshot.filter(id=relation.user_snapshot_id).first()
    if format and snapshot and snapshot.role_id:
        user = await user_service.get_user_by_id(user_id)
        role_name_map = await role_loader_service.map_translated_role_names(
            [snapshot.role_id], language
        )
        role_name = role_name_map.get(snapshot.role_id, "")
        snapshot.scenario = str_util.format_char_and_user(
            snapshot.scenario, role_name, user.nickname
        )
    return snapshot


async def create_urs_relation(
    user: User, role_id: int, conv_id: str, user_snapshot_id: int
):
    user_snapshot = await UserRoleSnapshot.filter(
        id=user_snapshot_id, user_id=user.id, status=1
    ).first()
    if not user_snapshot:
        raise exception_util.verify_exception(message="快照不存在或已被删除")
    await UrsConversationRelation(
        user_id=user.id,
        conversation_id=conv_id,
        user_snapshot_id=user_snapshot_id,
        role_id=role_id,
    ).save()
    return await get_user_role_snapshot_by_conv_id(user.id, conv_id)


# async def generate_chat_snapshot(
#     user: User, conversation_id: str, language: str
# ) -> dict:
#     nickname = COMMON_USER_NAME
#     history_list = await chat_message_service.display_history_by_conv_id(
#         user.id, nickname, conversation_id, language=language
#     )
#     if not history_list:
#         raise exception_util.verify_exception(message="消息不存在")
#     chat_history = "\n\n".join([x.content for x in history_list])

#     role_id = history_list[0].role_id
#     role_config = await role_loader_service.load_translated_role(
#         role_id, language, nickname
#     )
#     if not role_config:
#         raise exception_util.verify_exception(message="角色配置不存在")
#     ai_history_messages = [
#         x for x in history_list if x.type == ChatHistoryType.AI.value
#     ]
#     if not ai_history_messages:
#         raise exception_util.verify_exception(message="没有AI消息记录")

#     last_message_id = ai_history_messages[-1].message_id
#     last_message_version = ai_history_messages[-1].version
#     last_message = await chat_history_dao.get_by_message_id_and_version(
#         last_message_id, last_message_version
#     )
#     if not last_message:
#         raise exception_util.verify_exception(message="最后一条消息不存在")

#     role_name = role_config.role_name
#     data_config = RoleDataConfig(**json_util.convert_to_dict(role_config.data_config))
#     description = data_config.description or ""

#     product = await product_service.get_online_by_type_first(
#         ProductType.CHAT_SNAPSHOT.value
#     )
#     if not product:
#         raise exception_util.verify_exception(message="快照产品未上线")
#     total_balance = await account_service.get_total_balance(user.id)
#     if total_balance < product.price:
#         raise exception_util.verify_exception(
#             error_code=ErrorCode.INSUFFICIENT_BALANCE.value,
#             error_key=ErrorKey.INSUFFICIENT_BALANCE.value,
#         )

#     async def run_llm_task(
#         system_message: str,
#         user_message: str,
#         ai_message: str,
#         model: str,
#         max_tokens: int = 600,
#     ):
#         res = await lite_llm_bot.run_task(
#             model,
#             system_message,
#             user_message,
#             ai_message,
#             max_tokens=max_tokens,
#             metadata={
#                 "trace_user_id": user.id,
#                 "generation_name": "generate_chat_snapshot",
#                 "session_id": f"{user.id}_{conversation_id}",
#             },
#         )
#         search_ret = re.search(r"<response>(.*?)</response>", res, re.DOTALL)
#         return search_ret.group(1).strip() if search_ret else ""

#     sys_msg = snapshot_preset.GENERATE_SCENARIO_SYSTEM_PRESET
#     user_msg = snapshot_preset.GENERATE_SCENARIO_USER_PRESET.replace(
#         PresetReplace.CHAT_HISTORY.value, chat_history
#     )
#     ai_msg = snapshot_preset.GENERATE_SCENARIO_AI_PRESET
#     new_scenario = await run_llm_task(
#         sys_msg, user_msg, ai_msg, LlmModel.CLAUDE_3_5_HAIKU.value
#     )
#     # new_scenario_search = re.search(
#     #     r"<scenario>(.*?)</scenario>", new_sce_fm, re.DOTALL
#     # )
#     # new_scenario = new_scenario_search.group(1).strip() if new_scenario_search else ""
#     # new_first_message_search = re.search(
#     # r"<first_message>(.*?)</first_message>", new_sce_fm, re.DOTALL
#     # # )
#     # first_message = (
#     #     new_first_message_search.group(1).strip() if new_first_message_search else ""
#     # )
#     if not new_scenario:
#         raise exception_util.verify_exception(
#             message="生成的场景内容为空，请检查聊天记录是否足够丰富,或者稍后再试"
#         )
#     new_scenario = new_scenario.replace(role_name, PresetReplace.CHAR.value)
#     new_scenario = new_scenario.replace(nickname, PresetReplace.USER.value)

#     sys_msg = snapshot_preset.GENERATE_DESCRIPTION_SYSTEM_PRESET
#     user_msg = snapshot_preset.GENERATE_DESCRIPTION_USER_PRESET.replace(
#         PresetReplace.CHAR_DESCRIPTION.value, description
#     ).replace(PresetReplace.CHAT_HISTORY.value, chat_history)
#     ai_msg = snapshot_preset.GENERATE_DESCRIPTION_AI_PRESET

#     new_description = await run_llm_task(
#         sys_msg, user_msg, ai_msg, LlmModel.CLAUDE_3_5_SONNET.value, max_tokens=3000
#     )
#     new_description = new_description.replace(role_name, PresetReplace.CHAR.value)
#     new_description = new_description.replace(nickname, PresetReplace.USER.value)
#     role_snapshot_count = await UserRoleSnapshot.filter(
#         user_id=user.id, role_id=role_id
#     ).count()

#     snapshot = UserRoleSnapshot(
#         user_id=user.id,
#         role_id=role_id,
#         generate_order=role_snapshot_count + 1,
#         first_message=last_message.content,
#         scenario=new_scenario,
#         description=new_description,
#         from_conv_id=conversation_id,
#     )

#     await snapshot.save()

#     await account_service.AccountService.create_pay_order(user.id, product, role_id)
#     return {
#         "user_snapshot_id": snapshot.id,
#         "role_id": role_id,
#         "generate_order": snapshot.generate_order,
#         "scenario": new_scenario,
#         "description": new_description,
#     }


async def list_user_snapshots(
    user: User, offset: int = 0, limit: int = 10, language: str = Language.ZH.value
):
    user_snapshots = (
        await UserRoleSnapshot.filter(user_id=user.id, status=1)
        .order_by("-id")
        .only("id", "role_id", "generate_order")
    )
    total_count = len(user_snapshots)
    if offset > total_count or total_count == 0:
        return 0, []
    limit = min(limit, total_count - offset)
    user_snapshots = user_snapshots[offset : offset + limit]
    role_ids = [snapshot.role_id for snapshot in user_snapshots]
    role_configs = await role_loader_service.load_translated_roles(
        role_ids, language, user.nickname
    )
    ret_roles = [UserRoleBrief.from_config_and_audit(x) for x in role_configs]
    card_name_map = {x.id: x.card_name for x in ret_roles}
    card_map = {x.id: CardDetail.from_role(x) for x in ret_roles}

    ret_snapshots = []
    suffix = _tl("快照", language)
    for snapshot in user_snapshots:
        card_detail = card_map.get(snapshot.role_id)
        if not card_detail:
            continue
        title = f"{suffix}{snapshot.generate_order}-{card_name_map.get(snapshot.role_id, '未知角色')}"
        ret_snapshots.append(
            UserSnapshotCardDetail(id=snapshot.id, title=title, card_detail=card_detail)
        )
    return total_count, ret_snapshots


async def generate_chat_snapshot_new(
    user: User, conversation_id: str, language: str
) -> dict:
    nickname = COMMON_USER_NAME
    history_list = await chat_message_service.display_history_by_conv_id(
        user.id, nickname, conversation_id, language=language
    )
    if not history_list:
        raise exception_util.verify_exception(message="消息不存在")
    if len(history_list) > 20:
        history_list = history_list[-20:]
    chat_history = "\n\n".join([x.content for x in history_list])

    role_id = history_list[0].role_id
    role_config = await role_loader_service.load_translated_role(
        role_id, language, nickname
    )
    if not role_config:
        raise exception_util.verify_exception(message="角色配置不存在")
    role_name = role_config.role_name
    ai_history_messages = [
        x for x in history_list if x.type == ChatHistoryType.AI.value
    ]
    if not ai_history_messages:
        raise exception_util.verify_exception(message="没有AI消息记录")

    last_message_id = ai_history_messages[-1].message_id
    last_message_version = ai_history_messages[-1].version
    last_message = await chat_history_dao.get_by_message_id_and_version(
        last_message_id, last_message_version
    )
    if not last_message:
        raise exception_util.verify_exception(message="最后一条消息不存在")

    product = await product_service.get_online_by_type_first(
        ProductType.CHAT_SNAPSHOT.value
    )
    if not product:
        raise exception_util.verify_exception(message="快照产品未上线")
    total_balance = await account_service.get_total_balance(user.id)
    if total_balance < product.price:
        raise exception_util.verify_exception(
            error_code=ErrorCode.INSUFFICIENT_BALANCE.value,
            error_key=ErrorKey.INSUFFICIENT_BALANCE.value,
        )

    async def run_llm_task(
        system_message: str,
        user_message: str,
        ai_message: str,
        model: str,
        max_tokens: int = 2000,
    ):
        res = ""
        try:
            # 使用 lite_llm_bot 运行任务
            res = await lite_llm_bot.run_task(
                model,
                system_message,
                user_message,
                ai_message=ai_message,
                max_tokens=max_tokens,
                metadata={
                    "trace_user_id": user.id,
                    "generation_name": "generate_chat_snapshot",
                    "session_id": f"{user.id}_{conversation_id}",
                },
            )
        except Exception as e:
            # 处理异常，记录日志或其他操作
            logging.error(f"Error running LLM task:user_id {user.id} {e}")
            return "", ""
        if not res or "<scenario>" not in res or "<first_message>" not in res:
            return "", ""
        search_ret = re.search(r"<scenario>(.*?)</scenario>", res, re.DOTALL)
        scenario = search_ret.group(1).strip() if search_ret else ""
        search_ret = re.search(r"<first_message>(.*?)</first_message>", res, re.DOTALL)
        first_message = search_ret.group(1).strip() if search_ret else ""
        return scenario, first_message

    sys_msg = str_util.format_char_and_user(
        snapshot_preset.SYSTEM_PRESET, role_name, nickname
    ).replace(PresetReplace.CHAT_HISTORY.value, chat_history)
    user_msg = str_util.format_char_and_user(
        snapshot_preset.USER_PRESET, role_name, nickname
    ).replace(PresetReplace.CHAT_HISTORY.value, chat_history)
    ai_msg = str_util.format_char_and_user(
        snapshot_preset.AI_PRESET, role_name, nickname
    )
    ai_msg = ai_msg.replace(PresetReplace.LANGUAGE.value, Language.load_desc(language))
    scenario, first_message = await run_llm_task(
        sys_msg, user_msg, ai_msg, LlmModel.CLAUDE_3_5_SONNET.value
    )

    if not scenario or not first_message:
        raise exception_util.verify_exception(
            message="快照生成失败，稍后尝试（本次不扣费）"
        )
    first_message = first_message.replace(role_name, PresetReplace.CHAR.value)
    first_message = first_message.replace(nickname, PresetReplace.USER.value)
    scenario = scenario.replace(role_name, PresetReplace.CHAR.value)
    scenario = scenario.replace(nickname, PresetReplace.USER.value)
    role_snapshot_count = await UserRoleSnapshot.filter(
        user_id=user.id, role_id=role_id
    ).count()

    status_block = ""
    if last_message.type == ChatHistoryType.AI.value:
        status_block = str_util.parse_status_block(last_message.content)

    first_message = role_util.format_first_message_on_save_by_str(
        first_message, role_config, status_block
    )
    snapshot = UserRoleSnapshot(
        user_id=user.id,
        role_id=role_id,
        generate_order=role_snapshot_count + 1,
        first_message=first_message,
        scenario=scenario,
        from_conv_id=conversation_id,
    )
    await snapshot.save()

    await account_service.AccountService.create_pay_order(user.id, product, role_id)
    return {
        "user_snapshot_id": snapshot.id,
        "role_id": role_id,
        "generate_order": snapshot.generate_order,
        "scenario": scenario,
        "first_message": first_message,
    }
