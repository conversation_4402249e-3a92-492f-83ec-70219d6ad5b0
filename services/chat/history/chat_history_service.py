from itertools import groupby
import re
from common.models.chat_model import B<PERSON><PERSON><PERSON>oryQuery, ChatHistory, ChatHistoryType
from common.models.chat_request import BuildHistoryRet, ChatHistoryItem
from persistence import chat_history_dao
from services import regex_service, user_alt_profile_service
from services.role import role_loader_service
from utils import message_utils, str_util


async def repair_history_message(
    history: list[ChatHistory], role_maps: dict[int, str], query: BuildHistoryQuery
) -> list[ChatHistory]:
    not_repair_msg_id = ""
    if not history:
        return []
    if history[-1].type == ChatHistoryType.AI.value and history[-1].chat_continue:
        not_repair_msg_id = history[-1].message_id
    role_name = ""
    for his in history:
        if (
            his.message_id == not_repair_msg_id
            or his.type == ChatHistoryType.HUMAN.value
        ):
            continue
        role_name = role_maps.get(his.role_id, "")
        his.content = message_utils.repair_content(his.content)
        his.content = re.sub(r"<wit>.*?</wit>", "", his.content, flags=re.DOTALL)
        if role_name:
            his.content = re.sub(f"{role_name}[:：]", "", his.content, flags=re.DOTALL)
    history_nicknames = await user_alt_profile_service.get_user_history_nicknames(
        query.user_id
    )
    history = message_utils.process_user_name(query, history, history_nicknames)
    return history


async def build_history(
    query: BuildHistoryQuery, user_history: list[ChatHistory] = []
) -> BuildHistoryRet:
    if not user_history:
        user_history = await chat_history_dao.list_user_history(query.conversation_id)
    history = [
        max(g, key=lambda x: x.timestamp)
        for k, g in groupby(user_history, key=lambda x: x.message_id)
    ]
    if any([x.user_id != query.user_id for x in history]):
        return BuildHistoryRet(
            history_list=[],
            latest_status_block="",
        )
    role_name_maps = await role_loader_service.map_translated_role_names(
        list(set([x.role_id for x in history])), query.language
    )
    final_user_name = (
        query.request_user_name if query.use_request_user_name else query.nickname
    )
    history = await repair_history_message(history, role_name_maps, query)
    rules = await regex_service.list_by_option(query.regex_option)
    # 相同AI消息深度相同
    depth = 0
    index = len(history)
    latest_status_block = ""

    while index > 0:
        index -= 1
        x = history[index]
        if not latest_status_block:
            latest_status_block = str_util.parse_status_block(x.content)
        role_name = role_name_maps.get(x.role_id, "")
        if index < len(history) - 1 and x.type != history[index + 1].type:
            depth += 1
        if message_utils.require_remove_status_block(query, x):
            x.content = message_utils.remove_status_block(x.content)
        #先处理正则，然后在增加名字
        x.content = message_utils.process_new_regex_rules(
            rules,
            x.content,
            x.type,
            role_name,
            final_user_name,
            depth,
        )
        if query.add_name:
            x.content = message_utils.add_name_to_content(
                x.content, x.type, role_name, final_user_name
            )
    history_ret = [message_utils.chat_history_to_item(x) for x in history]
    if history and history[-1].type == ChatHistoryType.AI.value:
        history_ret[-1].can_continue_replay = history[-1].chat_continue
    return BuildHistoryRet(
        history_list=history_ret, latest_status_block=latest_status_block
    )
